package com.github.manasmods.tensura.event;

import com.github.manasmods.tensura.ability.magic.MagicElemental;
import com.github.manasmods.tensura.ability.magic.spiritual.SpiritualMagic;
import net.minecraft.world.entity.player.Player;
import net.minecraftforge.event.entity.player.PlayerEvent;
import net.minecraftforge.eventbus.api.Cancelable;

@Cancelable
public class SpiritLevelUpdateEvent extends PlayerEvent {
   private MagicElemental elemental;
   private SpiritualMagic.SpiritLevel spiritLevel;

   public SpiritLevelUpdateEvent(Player entity, int elemental, int spiritLevel) {
      super(entity);
      this.elemental = MagicElemental.byId(elemental);
      this.spiritLevel = SpiritualMagic.SpiritLevel.byId(spiritLevel);
   }

   public MagicElemental getElemental() {
      return this.elemental;
   }

   public void setElemental(MagicElemental elemental) {
      this.elemental = elemental;
   }

   public SpiritualMagic.SpiritLevel getSpiritLevel() {
      return this.spiritLevel;
   }

   public void setSpiritLevel(SpiritualMagic.SpiritLevel spiritLevel) {
      this.spiritLevel = spiritLevel;
   }
}
