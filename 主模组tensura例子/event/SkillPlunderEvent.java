package com.github.manasmods.tensura.event;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import javax.annotation.Nullable;
import net.minecraft.world.entity.Entity;
import net.minecraftforge.eventbus.api.Cancelable;
import net.minecraftforge.eventbus.api.Event;

@Cancelable
public class SkillPlunderEvent extends Event {
   @Nullable
   private final Entity target;
   @Nullable
   private final Entity plunderer;
   private final boolean steal;
   private ManasSkill skill;

   public SkillPlunderEvent(@Nullable Entity target, @Nullable Entity entity, boolean steal, ManasSkill skill) {
      this.target = target;
      this.plunderer = entity;
      this.steal = steal;
      this.skill = skill;
   }

   @Nullable
   public Entity getTarget() {
      return this.target;
   }

   @Nullable
   public Entity getPlunderer() {
      return this.plunderer;
   }

   public boolean isSteal() {
      return this.steal;
   }

   public ManasSkill getSkill() {
      return this.skill;
   }

   public void setSkill(ManasSkill skill) {
      this.skill = skill;
   }
}
