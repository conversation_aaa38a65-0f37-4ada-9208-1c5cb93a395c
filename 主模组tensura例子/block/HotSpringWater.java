package com.github.manasmods.tensura.block;

import com.github.manasmods.tensura.registry.blocks.TensuraBlocks;
import com.github.manasmods.tensura.registry.fluids.TensuraFluidTypes;
import java.util.function.Supplier;
import net.minecraft.core.BlockPos;
import net.minecraft.core.Direction;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.tags.BlockTags;
import net.minecraft.tags.FluidTags;
import net.minecraft.util.RandomSource;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.block.LiquidBlock;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.block.state.BlockBehaviour.Properties;
import net.minecraft.world.level.block.state.properties.BlockStateProperties;
import net.minecraft.world.level.material.FlowingFluid;
import net.minecraft.world.level.material.FluidState;
import net.minecraft.world.phys.AABB;

public class HotSpringWater extends LiquidBlock {
   private final float chance = 0.05F;

   public HotSpringWater(Supplier<? extends FlowingFluid> supplier, Properties properties) {
      super(supplier, properties);
   }

   public void m_7892_(BlockState pState, Level pLevel, BlockPos pPos, Entity pEntity) {
      if (pEntity instanceof LivingEntity) {
         LivingEntity living = (LivingEntity)pEntity;
         if (!living.m_21023_(MobEffects.f_19605_)) {
            living.m_7292_(new MobEffectInstance(MobEffects.f_19605_, 50, 1, false, false, false));
         }
      }
   }

   public boolean m_6724_(BlockState pState) {
      return true;
   }

   public void m_213898_(BlockState pState, ServerLevel pLevel, BlockPos pPos, RandomSource pRandom) {
      if (pLevel.isAreaLoaded(pPos, 5)) {
         BlockPos.m_121921_((new AABB(pPos)).m_82377_(5.0D, 0.0D, 5.0D)).forEach((pos) -> {
            if (pLevel.m_8055_(pos).m_204336_(BlockTags.f_144279_)) {
               if (0.05F > pRandom.m_188501_()) {
                  BlockState snow = pLevel.m_8055_(pos).m_60734_() == Blocks.f_50127_ ? (BlockState)Blocks.f_50125_.m_49966_().m_61124_(BlockStateProperties.f_61417_, 8) : pLevel.m_8055_(pos);
                  int layer = (Integer)snow.m_61143_(BlockStateProperties.f_61417_) - 1;
                  if (layer <= 0) {
                     pLevel.m_7471_(pos, false);
                  } else {
                     pLevel.m_7731_(pos, (BlockState)snow.m_61124_(BlockStateProperties.f_61417_, layer), 3);
                  }

                  pLevel.m_7106_(ParticleTypes.f_123803_, (double)pos.m_123341_(), (double)pos.m_123342_(), (double)pos.m_123343_(), 0.0D, 0.02D, 0.0D);
               }

            }
         });
         super.m_213898_(pState, pLevel, pPos, pRandom);
      }
   }

   public void m_214162_(BlockState pState, Level pLevel, BlockPos pPos, RandomSource pRandom) {
      if (pLevel.m_46749_(pPos)) {
         if (!pLevel.m_8055_(pPos.m_7494_()).m_60713_((Block)TensuraBlocks.HOT_SPRING_WATER.get())) {
            if (0.05F > pRandom.m_188501_()) {
               pLevel.m_7106_(ParticleTypes.f_123796_, (double)pPos.m_123341_() + pRandom.m_188500_(), (double)pPos.m_123342_() + 0.5D, (double)pPos.m_123343_() + pRandom.m_188500_(), 0.0D, 0.03D, 0.0D);
            }

            if (0.05F > pRandom.m_188501_()) {
               pLevel.m_7106_(ParticleTypes.f_123796_, (double)pPos.m_123341_() + pRandom.m_188500_(), (double)pPos.m_123342_() + 0.5D, (double)pPos.m_123343_() + pRandom.m_188500_(), 0.0D, 0.03D, 0.0D);
            }

            if (0.05F > pRandom.m_188501_() && 0.05F > pRandom.m_188501_()) {
               pLevel.m_5594_((Player)null, pPos, SoundEvents.f_11773_, SoundSource.BLOCKS, 0.5F, 1.0F);
            }

         } else {
            double d0 = (double)pPos.m_123341_();
            double d1 = (double)pPos.m_123342_();
            double d2 = (double)pPos.m_123343_();
            if (0.05F > pRandom.m_188501_()) {
               pLevel.m_7106_(ParticleTypes.f_123774_, d0 + 0.5D, d1, d2 + 0.5D, 0.0D, 0.04D, 0.0D);
            }

            if (0.05F > pRandom.m_188501_()) {
               pLevel.m_7106_(ParticleTypes.f_123774_, d0 + (double)pRandom.m_188501_(), d1 + (double)pRandom.m_188501_(), d2 + (double)pRandom.m_188501_(), 0.0D, 0.04D, 0.0D);
            }

         }
      }
   }

   public void m_6861_(BlockState pState, Level pLevel, BlockPos origin, Block pBlock, BlockPos neighbor, boolean pIsMoving) {
      if (!pLevel.m_8055_(neighbor).m_60767_().m_76332_()) {
         super.m_6861_(pState, pLevel, origin, pBlock, neighbor, pIsMoving);
      } else {
         FluidState fluidState = pLevel.m_6425_(neighbor);
         if (fluidState.m_205070_(FluidTags.f_13132_)) {
            if (fluidState.m_76170_()) {
               pLevel.m_7731_(neighbor, Blocks.f_50080_.m_49966_(), 3);
            } else {
               Block block = pLevel.m_213780_().m_188499_() ? Blocks.f_152497_ : Blocks.f_152496_;
               pLevel.m_7731_(neighbor, block.m_49966_(), 3);
            }

            pLevel.m_5594_((Player)null, neighbor, SoundEvents.f_11937_, SoundSource.BLOCKS, 0.5F, 0.5F);
            pLevel.m_46796_(1501, neighbor, 0);
         } else if (fluidState.m_205070_(FluidTags.f_13131_) && fluidState.getFluidType() != TensuraFluidTypes.HOT_SPRING_WATER.get()) {
            int i = pLevel.m_213780_().m_216332_(1, 3);
            Block block = i == 1 ? Blocks.f_50228_ : (i == 2 ? Blocks.f_50334_ : Blocks.f_50122_);
            pLevel.m_7731_(origin, block.m_49966_(), 3);
            pLevel.m_5594_((Player)null, origin, SoundEvents.f_11996_, SoundSource.BLOCKS, 0.5F, 0.5F);
         }

         super.m_6861_(pState, pLevel, origin, pBlock, neighbor, pIsMoving);
      }
   }

   public void m_6807_(BlockState pState, Level pLevel, BlockPos pPos, BlockState pOldState, boolean pIsMoving) {
      Direction[] var6 = Direction.values();
      int var7 = var6.length;

      for(int var8 = 0; var8 < var7; ++var8) {
         Direction direction = var6[var8];
         BlockPos pos = pPos.m_121945_(direction);
         if (pLevel.m_6425_(pos).m_205070_(FluidTags.f_13131_) || pLevel.m_6425_(pos).m_205070_(FluidTags.f_13132_)) {
            this.m_6861_(pState, pLevel, pPos, pLevel.m_8055_(pPos).m_60734_(), pos, false);
         }
      }

      super.m_6807_(pState, pLevel, pPos, pOldState, pIsMoving);
   }
}
