package com.github.manasmods.tensura.block;

import com.github.manasmods.tensura.block.state.properties.CookingPotPart;
import com.github.manasmods.tensura.block.state.properties.TensuraBlockStateProperties;
import net.minecraft.core.BlockPos;
import net.minecraft.core.Direction;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.util.RandomSource;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.context.BlockPlaceContext;
import net.minecraft.world.level.BlockGetter;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.block.Mirror;
import net.minecraft.world.level.block.Rotation;
import net.minecraft.world.level.block.SoundType;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.block.state.BlockBehaviour.Properties;
import net.minecraft.world.level.block.state.StateDefinition.Builder;
import net.minecraft.world.level.block.state.properties.BlockStateProperties;
import net.minecraft.world.level.block.state.properties.DirectionProperty;
import net.minecraft.world.level.block.state.properties.EnumProperty;
import net.minecraft.world.level.block.state.properties.Property;
import net.minecraft.world.level.material.Material;
import net.minecraft.world.level.material.MaterialColor;
import net.minecraft.world.level.material.PushReaction;
import net.minecraft.world.phys.shapes.CollisionContext;
import net.minecraft.world.phys.shapes.Shapes;
import net.minecraft.world.phys.shapes.VoxelShape;
import org.jetbrains.annotations.Nullable;

public class CookingPotBlock extends SimpleBlock {
   public static final DirectionProperty FACING;
   public static final EnumProperty<CookingPotPart> PART;
   public static final MutableComponent TITLE;
   public boolean spawnParticles = true;
   private static final VoxelShape TOP_SHAPE;
   private static final VoxelShape BOTTOM_SHAPE;

   public CookingPotBlock() {
      super(Properties.m_60944_(Material.f_76279_, MaterialColor.f_76419_).m_60913_(50.0F, 1200.0F).m_60918_(SoundType.f_56736_).m_60955_().m_60999_());
      this.m_49959_((BlockState)((BlockState)((BlockState)this.m_49965_().m_61090_()).m_61124_(FACING, Direction.NORTH)).m_61124_(PART, CookingPotPart.BOTTOM));
   }

   public void m_6402_(Level pLevel, BlockPos pPos, BlockState pState, @Nullable LivingEntity pPlacer, ItemStack pStack) {
      if (!pLevel.f_46443_) {
         BlockPos blockpos = this.getOtherPartPosition(pPos, (CookingPotPart)pState.m_61143_(PART));
         pLevel.m_7731_(blockpos, (BlockState)pState.m_61124_(PART, CookingPotPart.TOP), 3);
         pLevel.m_6289_(pPos, Blocks.f_50016_);
         pState.m_60701_(pLevel, pPos, 3);
      }

      super.m_6402_(pLevel, pPos, pState, pPlacer, pStack);
   }

   public int getLightEmission(BlockState state, BlockGetter level, BlockPos pos) {
      if (this.spawnParticles) {
         return state.m_61143_(PART) == CookingPotPart.BOTTOM ? 15 : 14;
      } else {
         return 0;
      }
   }

   private BlockPos getOtherPartPosition(BlockPos sourcePos, CookingPotPart part) {
      return part == CookingPotPart.BOTTOM ? sourcePos.m_7494_() : sourcePos.m_7495_();
   }

   protected void m_7926_(Builder<Block, BlockState> pBuilder) {
      pBuilder.m_61104_(new Property[]{FACING, PART});
   }

   public void m_5707_(Level pLevel, BlockPos pPos, BlockState pState, Player pPlayer) {
      if (!pLevel.f_46443_) {
         BlockPos blockpos = this.getOtherPartPosition(pPos, (CookingPotPart)pState.m_61143_(PART));
         pLevel.m_46597_(blockpos, Blocks.f_50016_.m_49966_());
      }

      super.m_5707_(pLevel, pPos, pState, pPlayer);
   }

   @Nullable
   public BlockState m_5573_(BlockPlaceContext pContext) {
      BlockPos blockpos = pContext.m_8083_();
      Level level = pContext.m_43725_();
      return blockpos.m_123342_() < level.m_151558_() - 1 && level.m_8055_(blockpos.m_7494_()).m_60629_(pContext) ? (BlockState)this.m_49966_().m_61124_(FACING, pContext.m_8125_().m_122424_()) : null;
   }

   public VoxelShape m_5940_(BlockState pState, BlockGetter pLevel, BlockPos pPos, CollisionContext pContext) {
      CookingPotPart part = (CookingPotPart)pState.m_61143_(PART);
      return part == CookingPotPart.TOP ? TOP_SHAPE : BOTTOM_SHAPE;
   }

   public BlockState m_6843_(BlockState pState, Rotation pRotation) {
      return (BlockState)pState.m_61124_(FACING, pRotation.m_55954_((Direction)pState.m_61143_(FACING)));
   }

   public BlockState m_6943_(BlockState pState, Mirror pMirror) {
      return pState.m_60717_(pMirror.m_54846_((Direction)pState.m_61143_(FACING)));
   }

   public PushReaction m_5537_(BlockState pState) {
      return PushReaction.BLOCK;
   }

   public void m_214162_(BlockState pState, Level pLevel, BlockPos pPos, RandomSource pRandom) {
      if (pRandom.m_188503_(10) == 0) {
         pLevel.m_7785_((double)pPos.m_123341_() + 0.5D, (double)pPos.m_123342_() + 0.5D, (double)pPos.m_123343_() + 0.5D, SoundEvents.f_11784_, SoundSource.BLOCKS, 0.5F + pRandom.m_188501_(), pRandom.m_188501_() * 0.7F + 0.6F, false);
      }

      if (this.spawnParticles && pRandom.m_188503_(5) == 0) {
         for(int i = 0; i < pRandom.m_188503_(1) + 1; ++i) {
            pLevel.m_7106_(ParticleTypes.f_123756_, (double)pPos.m_123341_() + 0.5D, (double)pPos.m_123342_() + 0.5D, (double)pPos.m_123343_() + 0.5D, (double)(pRandom.m_188501_() / 2.0F), 5.0E-5D, (double)(pRandom.m_188501_() / 2.0F));
         }
      }

   }

   static {
      FACING = BlockStateProperties.f_61374_;
      PART = TensuraBlockStateProperties.COOKING_POT_PART;
      TITLE = Component.m_237115_("block.tensura.cooking_pot");
      TOP_SHAPE = Shapes.m_83124_(Block.m_49796_(2.0D, 0.0D, 2.0D, 14.0D, 6.0D, 14.0D), new VoxelShape[0]);
      BOTTOM_SHAPE = Shapes.m_83124_(Block.m_49796_(0.0D, 0.0D, 0.0D, 16.0D, 16.0D, 16.0D), new VoxelShape[0]);
   }
}
