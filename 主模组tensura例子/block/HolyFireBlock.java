package com.github.manasmods.tensura.block;

import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.race.RaceHelper;
import com.github.manasmods.tensura.registry.blocks.TensuraBlocks;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import net.minecraft.core.BlockPos;
import net.minecraft.core.Direction;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.util.RandomSource;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.context.BlockPlaceContext;
import net.minecraft.world.level.BlockGetter;
import net.minecraft.world.level.GameRules;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.LevelAccessor;
import net.minecraft.world.level.LevelReader;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.block.SoundType;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.block.state.BlockBehaviour.Properties;
import net.minecraft.world.level.block.state.StateDefinition.Builder;
import net.minecraft.world.level.block.state.properties.BlockStateProperties;
import net.minecraft.world.level.block.state.properties.IntegerProperty;
import net.minecraft.world.level.block.state.properties.Property;
import net.minecraft.world.level.gameevent.GameEvent;
import net.minecraft.world.level.gameevent.GameEvent.Context;
import net.minecraft.world.level.material.Material;
import net.minecraft.world.level.material.MaterialColor;
import net.minecraft.world.phys.shapes.CollisionContext;
import net.minecraft.world.phys.shapes.VoxelShape;

public class HolyFireBlock extends Block {
   public static final IntegerProperty AGE;

   public HolyFireBlock() {
      super(Properties.m_60944_(Material.f_76309_, MaterialColor.f_76415_).m_60910_().m_60955_().m_60966_().m_60953_((blockState) -> {
         return 15;
      }).m_60918_(SoundType.f_56745_).m_222994_());
      this.m_49959_((BlockState)((BlockState)this.f_49792_.m_61090_()).m_61124_(AGE, 0));
   }

   protected void m_7926_(Builder<Block, BlockState> pBuilder) {
      pBuilder.m_61104_(new Property[]{AGE});
   }

   public BlockState m_5573_(BlockPlaceContext pContext) {
      return (BlockState)this.m_49966_().m_61124_(AGE, 0);
   }

   public static boolean canBePlacedAt(Level pLevel, BlockPos pPos) {
      BlockState blockstate = pLevel.m_8055_(pPos);
      return !blockstate.m_60795_() ? false : canHolyFireSurvive(pLevel, pPos);
   }

   public void m_6807_(BlockState pState, Level pLevel, BlockPos pPos, BlockState pOldState, boolean pIsMoving) {
      if (!pOldState.m_60713_(pState.m_60734_()) && !pState.m_60710_(pLevel, pPos)) {
         pLevel.m_7471_(pPos, false);
      } else {
         pLevel.m_186460_(pPos, this, getFireTickDelay(pLevel.f_46441_));
      }

   }

   public BlockState m_7417_(BlockState pState, Direction pFacing, BlockState pFacingState, LevelAccessor pLevel, BlockPos pCurrentPos, BlockPos pFacingPos) {
      return canHolyFireSurvive(pLevel, pCurrentPos) ? (BlockState)((Block)TensuraBlocks.HOLY_FIRE.get()).m_49966_().m_61124_(AGE, (Integer)pState.m_61143_(AGE)) : Blocks.f_50016_.m_49966_();
   }

   public VoxelShape m_5940_(BlockState pState, BlockGetter pLevel, BlockPos pPos, CollisionContext pContext) {
      return Block.m_49796_(0.0D, 0.0D, 0.0D, 16.0D, 1.0D, 16.0D);
   }

   public static int getFireTickDelay(RandomSource pRandom) {
      return 30 + pRandom.m_188503_(10);
   }

   public static boolean canHolyFireSurvive(LevelReader pLevel, BlockPos pPos) {
      BlockPos blockpos = pPos.m_7495_();
      if (pLevel.m_8055_(blockpos).m_60767_().m_76336_()) {
         return false;
      } else {
         return isValidFireLocation(pLevel, pPos) ? true : pLevel.m_8055_(blockpos).m_60783_(pLevel, blockpos, Direction.UP);
      }
   }

   private static boolean isValidFireLocation(BlockGetter pLevel, BlockPos pPos) {
      Direction[] var2 = Direction.values();
      int var3 = var2.length;

      for(int var4 = 0; var4 < var3; ++var4) {
         Direction direction = var2[var4];
         if (pLevel.m_8055_(pPos.m_121945_(direction)).isFlammable(pLevel, pPos.m_121945_(direction), direction.m_122424_())) {
            return true;
         }
      }

      return false;
   }

   public void m_5707_(Level pLevel, BlockPos pPos, BlockState pState, Player pPlayer) {
      if (!pLevel.m_5776_()) {
         pLevel.m_5898_((Player)null, 1009, pPos, 0);
      }

      pLevel.m_220407_(GameEvent.f_157794_, pPos, Context.m_223719_(pPlayer, pState));
   }

   public void m_213897_(BlockState pState, ServerLevel pLevel, BlockPos pPos, RandomSource pRandom) {
      pLevel.m_186460_(pPos, this, getFireTickDelay(pLevel.f_46441_));
      if (pLevel.m_46469_().m_46207_(GameRules.f_46131_)) {
         if (!pState.m_60710_(pLevel, pPos)) {
            pLevel.m_7471_(pPos, false);
         }

         int i = (Integer)pState.m_61143_(AGE);
         int j = Math.min(15, i + pRandom.m_188503_(3) / 2);
         if (i != j) {
            pState = (BlockState)pState.m_61124_(AGE, j);
            pLevel.m_7731_(pPos, pState, 4);
         }

         if (!pLevel.m_8055_(pPos.m_7495_()).m_204336_(TensuraTags.Blocks.HOLY_FIRE_SOURCE) && i == 15 && pRandom.m_188503_(4) == 0) {
            pLevel.m_7471_(pPos, false);
         }
      }

   }

   public void m_7892_(BlockState pState, Level pLevel, BlockPos pPos, Entity entity) {
      if (!entity.m_5825_() && RaceHelper.isAffectedByHolyCoat(entity)) {
         int damage = RaceHelper.isUndead(entity) ? 4 : 2;
         entity.m_6469_(TensuraDamageSources.HOLY_DAMAGE, (float)damage);
         entity.m_7311_(entity.m_20094_() + 1);
         if (entity.m_20094_() == 0) {
            entity.m_20254_(3);
         }
      }

      super.m_7892_(pState, pLevel, pPos, entity);
   }

   public void m_214162_(BlockState pState, Level pLevel, BlockPos pPos, RandomSource pRandom) {
      if (pRandom.m_188503_(24) == 0) {
         pLevel.m_7785_((double)pPos.m_123341_() + 0.5D, (double)pPos.m_123342_() + 0.5D, (double)pPos.m_123343_() + 0.5D, SoundEvents.f_11936_, SoundSource.BLOCKS, 1.0F + pRandom.m_188501_(), pRandom.m_188501_() * 0.7F + 0.3F, false);
      }

      for(int i = 0; i < 3; ++i) {
         double d0 = (double)pPos.m_123341_() + pRandom.m_188500_();
         double d1 = (double)pPos.m_123342_() + pRandom.m_188500_() * 0.5D + 0.5D;
         double d2 = (double)pPos.m_123343_() + pRandom.m_188500_();
         pLevel.m_7106_(ParticleTypes.f_123755_, d0, d1, d2, 0.0D, 0.02D, 0.0D);
      }

   }

   static {
      AGE = BlockStateProperties.f_61410_;
   }
}
