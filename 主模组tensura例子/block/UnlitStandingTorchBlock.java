package com.github.manasmods.tensura.block;

import net.minecraft.core.BlockPos;
import net.minecraft.core.Direction;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.util.RandomSource;
import net.minecraft.world.item.context.BlockPlaceContext;
import net.minecraft.world.level.BlockGetter;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.LevelAccessor;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.SimpleWaterloggedBlock;
import net.minecraft.world.level.block.TorchBlock;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.block.state.BlockBehaviour.Properties;
import net.minecraft.world.level.block.state.StateDefinition.Builder;
import net.minecraft.world.level.block.state.properties.BlockStateProperties;
import net.minecraft.world.level.block.state.properties.BooleanProperty;
import net.minecraft.world.level.block.state.properties.Property;
import net.minecraft.world.level.material.Fluid;
import net.minecraft.world.level.material.FluidState;
import net.minecraft.world.level.material.Fluids;
import org.jetbrains.annotations.Nullable;

public class UnlitStandingTorchBlock extends TorchBlock implements SimpleWaterloggedBlock {
   public static final BooleanProperty WATERLOGGED;

   public UnlitStandingTorchBlock(Properties pProperties, ParticleOptions pFlameParticle) {
      super(pProperties, pFlameParticle);
      this.m_49959_((BlockState)((BlockState)this.f_49792_.m_61090_()).m_61124_(WATERLOGGED, Boolean.FALSE));
   }

   public void m_214162_(BlockState pState, Level pLevel, BlockPos pPos, RandomSource pRandom) {
   }

   @Nullable
   public BlockState m_5573_(BlockPlaceContext pContext) {
      FluidState fluidstate = pContext.m_43725_().m_6425_(pContext.m_8083_());
      boolean flag = fluidstate.m_192917_(Fluids.f_76193_);
      return (BlockState)this.m_49966_().m_61124_(WATERLOGGED, flag);
   }

   public boolean m_6044_(BlockGetter pLevel, BlockPos pPos, BlockState pState, Fluid pFluid) {
      return pFluid.m_76145_().m_192917_(Fluids.f_76193_);
   }

   public BlockState m_7417_(BlockState pState, Direction pFacing, BlockState pFacingState, LevelAccessor pLevel, BlockPos pCurrentPos, BlockPos pFacingPos) {
      if ((Boolean)pState.m_61143_(WATERLOGGED)) {
         pLevel.m_186469_(pCurrentPos, Fluids.f_76193_, Fluids.f_76193_.m_6718_(pLevel));
      }

      return super.m_7417_(pState, pFacing, pFacingState, pLevel, pCurrentPos, pFacingPos);
   }

   public FluidState m_5888_(BlockState pState) {
      return (Boolean)pState.m_61143_(WATERLOGGED) ? Fluids.f_76193_.m_76068_(false) : super.m_5888_(pState);
   }

   protected void m_7926_(Builder<Block, BlockState> pBuilder) {
      super.m_7926_(pBuilder);
      pBuilder.m_61104_(new Property[]{WATERLOGGED});
   }

   static {
      WATERLOGGED = BlockStateProperties.f_61362_;
   }
}
