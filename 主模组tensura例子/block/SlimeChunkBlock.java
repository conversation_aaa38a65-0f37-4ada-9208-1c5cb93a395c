package com.github.manasmods.tensura.block;

import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.registry.blocks.TensuraBlocks;
import net.minecraft.core.BlockPos;
import net.minecraft.core.Direction;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.level.BlockGetter;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.block.state.BlockBehaviour.Properties;
import net.minecraft.world.level.pathfinder.PathComputationType;
import net.minecraft.world.phys.Vec3;
import net.minecraft.world.phys.shapes.CollisionContext;
import net.minecraft.world.phys.shapes.EntityCollisionContext;
import net.minecraft.world.phys.shapes.Shapes;
import net.minecraft.world.phys.shapes.VoxelShape;

public class SlimeChunkBlock extends Block {
   private static final VoxelShape FALLING_COLLISION_SHAPE = Shapes.m_83048_(0.0D, 0.0D, 0.0D, 1.0D, 0.8999999761581421D, 1.0D);

   public SlimeChunkBlock(Properties pProperties) {
      super(pProperties);
   }

   public boolean m_6104_(BlockState pState, BlockState pAdjacentBlockState, Direction pDirection) {
      return pAdjacentBlockState.m_60713_(this) || pAdjacentBlockState.m_60713_((Block)TensuraBlocks.CHILLED_SLIME_BLOCK.get());
   }

   public VoxelShape m_7952_(BlockState pState, BlockGetter pLevel, BlockPos pPos) {
      return Shapes.m_83040_();
   }

   public void m_7892_(BlockState pState, Level pLevel, BlockPos pPos, Entity pEntity) {
      if (!pEntity.m_6095_().m_204039_(TensuraTags.EntityTypes.SLIME_WALKABLE_MOBS)) {
         if (!(pEntity instanceof LivingEntity) || pEntity.m_146900_().m_60713_(this)) {
            pEntity.m_7601_(pState, new Vec3(0.699999988079071D, 0.7D, 0.699999988079071D));
         }

      }
   }

   public void m_142072_(Level pLevel, BlockState pState, BlockPos pPos, Entity pEntity, float pFallDistance) {
      if (!(pFallDistance < 4.0F)) {
         pEntity.m_5496_(SoundEvents.f_12390_, 1.0F, 1.0F);
      }
   }

   public VoxelShape m_5939_(BlockState pState, BlockGetter pLevel, BlockPos pPos, CollisionContext pContext) {
      if (pContext instanceof EntityCollisionContext) {
         EntityCollisionContext entitycollisioncontext = (EntityCollisionContext)pContext;
         Entity entity = entitycollisioncontext.m_193113_();
         if (entity == null) {
            return Shapes.m_83040_();
         }

         if (entity.f_19789_ > 2.5F) {
            return FALLING_COLLISION_SHAPE;
         }
      }

      return Shapes.m_83040_();
   }

   public boolean m_7357_(BlockState pState, BlockGetter pLevel, BlockPos pPos, PathComputationType pType) {
      return true;
   }

   public boolean isStickyBlock(BlockState state) {
      return true;
   }

   public boolean canStickTo(BlockState state, BlockState other) {
      if (other.m_60734_() == Blocks.f_50374_) {
         return false;
      } else if (other.m_60734_() == Blocks.f_50719_) {
         return false;
      } else {
         return state.isStickyBlock() || other.isStickyBlock();
      }
   }
}
