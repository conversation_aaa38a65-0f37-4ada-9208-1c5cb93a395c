package com.github.manasmods.tensura.block;

import net.minecraft.core.BlockPos;
import net.minecraft.core.Direction;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.item.DyeColor;
import net.minecraft.world.level.BlockGetter;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.BedBlock;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.block.RenderShape;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.block.state.BlockBehaviour.Properties;
import net.minecraft.world.level.block.state.properties.BedPart;
import net.minecraft.world.phys.shapes.CollisionContext;
import net.minecraft.world.phys.shapes.Shapes;
import net.minecraft.world.phys.shapes.VoxelShape;

public class ThatchBed extends BedBlock {
   protected static final VoxelShape BASE = Block.m_49796_(0.0D, 0.0D, 0.0D, 16.0D, 2.0D, 16.0D);
   protected static final VoxelShape NORTH_SHAPE;
   protected static final VoxelShape SOUTH_SHAPE;
   protected static final VoxelShape WEST_SHAPE;
   protected static final VoxelShape EAST_SHAPE;

   public ThatchBed(Properties pProperties) {
      super(DyeColor.PINK, pProperties);
      this.m_49959_((BlockState)((BlockState)((BlockState)this.f_49792_.m_61090_()).m_61124_(f_49440_, BedPart.FOOT)).m_61124_(f_49441_, false));
   }

   public void m_142072_(Level pLevel, BlockState pState, BlockPos pPos, Entity pEntity, float pFallDistance) {
      pEntity.m_142535_(pEntity.f_19789_, 0.2F, DamageSource.f_19315_);
   }

   public void m_5548_(BlockGetter pLevel, Entity pEntity) {
   }

   public VoxelShape m_5940_(BlockState pState, BlockGetter pLevel, BlockPos pPos, CollisionContext pContext) {
      Direction direction = m_49557_(pState).m_122424_();
      VoxelShape var10000;
      switch(direction) {
      case NORTH:
         var10000 = NORTH_SHAPE;
         break;
      case SOUTH:
         var10000 = SOUTH_SHAPE;
         break;
      case WEST:
         var10000 = WEST_SHAPE;
         break;
      default:
         var10000 = EAST_SHAPE;
      }

      return var10000;
   }

   public RenderShape m_7514_(BlockState pState) {
      return RenderShape.MODEL;
   }

   public void m_6810_(BlockState pState, Level pLevel, BlockPos pPos, BlockState pNewState, boolean pIsMoving) {
      if (pNewState.m_60795_() && !pLevel.f_46443_) {
         BlockPos blockpos = pPos.m_121945_(m_49533_((BedPart)pState.m_61143_(f_49440_), (Direction)pState.m_61143_(f_54117_)));
         BlockState blockstate = pLevel.m_8055_(blockpos);
         if (blockstate.m_60713_(this)) {
            pLevel.m_7731_(blockpos, Blocks.f_50016_.m_49966_(), 35);
         }
      }

      super.m_6810_(pState, pLevel, pPos, pNewState, pIsMoving);
   }

   private static Direction m_49533_(BedPart pPart, Direction pDirection) {
      return pPart == BedPart.FOOT ? pDirection : pDirection.m_122424_();
   }

   static {
      NORTH_SHAPE = Shapes.m_83124_(BASE, new VoxelShape[0]);
      SOUTH_SHAPE = Shapes.m_83124_(BASE, new VoxelShape[0]);
      WEST_SHAPE = Shapes.m_83124_(BASE, new VoxelShape[0]);
      EAST_SHAPE = Shapes.m_83124_(BASE, new VoxelShape[0]);
   }
}
