package com.github.manasmods.tensura.block;

import net.minecraft.core.Direction;
import net.minecraft.world.item.context.BlockPlaceContext;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.DirectionalBlock;
import net.minecraft.world.level.block.Mirror;
import net.minecraft.world.level.block.Rotation;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.block.state.BlockBehaviour.Properties;
import net.minecraft.world.level.block.state.StateDefinition.Builder;
import net.minecraft.world.level.block.state.properties.Property;
import net.minecraft.world.level.material.Material;

public class SidewayDirectionalBlock extends DirectionalBlock {
   public SidewayDirectionalBlock(Material material) {
      this(material, (properties1) -> {
         return properties1;
      });
   }

   public SidewayDirectionalBlock(Material material, SimpleBlock.BlockProperties properties) {
      super(properties.create(Properties.m_60939_(material)));
      this.m_49959_((BlockState)((BlockState)this.f_49792_.m_61090_()).m_61124_(f_52588_, Direction.NORTH));
   }

   public SidewayDirectionalBlock(Properties properties) {
      super(properties);
      this.m_49959_((BlockState)((BlockState)this.f_49792_.m_61090_()).m_61124_(f_52588_, Direction.UP));
   }

   protected void m_7926_(Builder<Block, BlockState> pBuilder) {
      pBuilder.m_61104_(new Property[]{f_52588_});
   }

   public BlockState m_6843_(BlockState pState, Rotation pRotation) {
      return (BlockState)pState.m_61124_(f_52588_, pRotation.m_55954_((Direction)pState.m_61143_(f_52588_)));
   }

   public BlockState m_6943_(BlockState pState, Mirror pMirror) {
      return pState.m_60717_(pMirror.m_54846_((Direction)pState.m_61143_(f_52588_)));
   }

   public BlockState m_5573_(BlockPlaceContext pContext) {
      return (BlockState)this.m_49966_().m_61124_(f_52588_, pContext.m_8125_().m_122424_());
   }
}
