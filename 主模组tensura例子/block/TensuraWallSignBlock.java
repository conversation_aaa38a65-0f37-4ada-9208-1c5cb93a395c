package com.github.manasmods.tensura.block;

import com.github.manasmods.tensura.block.entity.TensuraSignBlockEntity;
import net.minecraft.core.BlockPos;
import net.minecraft.world.level.block.WallSignBlock;
import net.minecraft.world.level.block.entity.BlockEntity;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.block.state.BlockBehaviour.Properties;
import net.minecraft.world.level.block.state.properties.WoodType;

public class TensuraWallSignBlock extends WallSignBlock {
   public TensuraWallSignBlock(Properties p_58068_, WoodType p_58069_) {
      super(p_58068_, p_58069_);
   }

   public BlockEntity m_142194_(BlockPos pPos, BlockState pState) {
      return new TensuraSignBlockEntity(pPos, pState);
   }
}
