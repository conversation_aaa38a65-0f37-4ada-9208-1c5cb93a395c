package com.github.manasmods.tensura.block;

import com.github.manasmods.tensura.client.TensuraGUIHelper;
import net.minecraft.core.BlockPos;
import net.minecraft.core.Direction;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.util.RandomSource;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.Items;
import net.minecraft.world.item.context.BlockPlaceContext;
import net.minecraft.world.level.BlockGetter;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.LevelAccessor;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.RenderShape;
import net.minecraft.world.level.block.SimpleWaterloggedBlock;
import net.minecraft.world.level.block.SoundType;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.block.state.BlockBehaviour.Properties;
import net.minecraft.world.level.block.state.StateDefinition.Builder;
import net.minecraft.world.level.block.state.properties.BlockStateProperties;
import net.minecraft.world.level.block.state.properties.BooleanProperty;
import net.minecraft.world.level.block.state.properties.Property;
import net.minecraft.world.level.material.FluidState;
import net.minecraft.world.level.material.Fluids;
import net.minecraft.world.level.material.Material;
import net.minecraft.world.phys.shapes.CollisionContext;
import net.minecraft.world.phys.shapes.Shapes;
import net.minecraft.world.phys.shapes.VoxelShape;

public class SolidSpaceBlock extends Block implements SimpleWaterloggedBlock {
   public static final BooleanProperty WATERLOGGED;

   public SolidSpaceBlock() {
      super(Properties.m_60939_(Material.f_76296_).m_60918_(SoundType.f_154654_).m_222994_().m_60955_().m_60966_());
      this.m_49959_((BlockState)((BlockState)this.f_49792_.m_61090_()).m_61124_(WATERLOGGED, Boolean.FALSE));
   }

   public VoxelShape m_5940_(BlockState pState, BlockGetter pLevel, BlockPos pPos, CollisionContext pContext) {
      return Shapes.m_83144_();
   }

   public RenderShape m_7514_(BlockState pState) {
      return RenderShape.INVISIBLE;
   }

   public void m_214162_(BlockState pState, Level pLevel, BlockPos pos, RandomSource pRandom) {
      TensuraGUIHelper.spawnMarkerParticle(pLevel, pState, pos, Items.f_42263_);
   }

   public VoxelShape m_5939_(BlockState pState, BlockGetter pLevel, BlockPos pPos, CollisionContext pContext) {
      return pState.m_60808_(pLevel, pPos);
   }

   protected void m_142387_(Level pLevel, Player pPlayer, BlockPos pPos, BlockState pState) {
   }

   public boolean m_180643_(BlockState pState, BlockGetter pLevel, BlockPos pPos) {
      return true;
   }

   public boolean m_7420_(BlockState pState, BlockGetter pLevel, BlockPos pPos) {
      return true;
   }

   public float m_7749_(BlockState pState, BlockGetter pLevel, BlockPos pPos) {
      return 1.0F;
   }

   public void m_213897_(BlockState pState, ServerLevel pLevel, BlockPos pPos, RandomSource pRandom) {
      pLevel.m_46961_(pPos, false);
   }

   public BlockState m_7417_(BlockState pState, Direction pDirection, BlockState pNeighborState, LevelAccessor pLevel, BlockPos pCurrentPos, BlockPos pNeighborPos) {
      if ((Boolean)pState.m_61143_(WATERLOGGED)) {
         pLevel.m_186469_(pCurrentPos, Fluids.f_76193_, Fluids.f_76193_.m_6718_(pLevel));
      }

      return super.m_7417_(pState, pDirection, pNeighborState, pLevel, pCurrentPos, pNeighborPos);
   }

   public FluidState m_5888_(BlockState pState) {
      return (Boolean)pState.m_61143_(WATERLOGGED) ? Fluids.f_76193_.m_76068_(false) : super.m_5888_(pState);
   }

   protected void m_7926_(Builder<Block, BlockState> pBuilder) {
      pBuilder.m_61104_(new Property[]{WATERLOGGED});
   }

   public BlockState m_5573_(BlockPlaceContext pContext) {
      FluidState fluidstate = pContext.m_43725_().m_6425_(pContext.m_8083_());
      return (BlockState)this.m_49966_().m_61124_(WATERLOGGED, fluidstate.m_76152_() == Fluids.f_76193_);
   }

   static {
      WATERLOGGED = BlockStateProperties.f_61362_;
   }
}
