package com.github.manasmods.tensura.loot;

import com.mojang.serialization.Codec;
import net.minecraftforge.common.loot.IGlobalLootModifier;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.registries.DeferredRegister;
import net.minecraftforge.registries.RegistryObject;
import net.minecraftforge.registries.ForgeRegistries.Keys;

public class TensuraLootModifiers {
   public static final DeferredRegister<Codec<? extends IGlobalLootModifier>> LOOT_MODIFIERS;
   public static final RegistryObject<Codec<AddItemModifier>> ADD_ITEM_MODIFIER;

   public static void init(IEventBus bus) {
      LOOT_MODIFIERS.register(bus);
   }

   static {
      LOOT_MODIFIERS = DeferredRegister.create(Keys.GLOBAL_LOOT_MODIFIER_SERIALIZERS, "tensura");
      ADD_ITEM_MODIFIER = LOOT_MODIFIERS.register("add_item_modifier", AddItemModifier.CODEC);
   }
}
