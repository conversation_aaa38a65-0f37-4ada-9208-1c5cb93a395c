package com.github.manasmods.tensura.effect;

import com.github.manasmods.tensura.effect.template.TensuraMobEffect;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import java.util.Collections;
import java.util.List;
import net.minecraft.world.effect.MobEffectCategory;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;

public class MagiculePoisonEffect extends TensuraMobEffect {
   public MagiculePoisonEffect(MobEffectCategory pCategory, int pColor) {
      super(pCategory, pColor);
   }

   public void m_6742_(LivingEntity pLivingEntity, int pAmplifier) {
      int level = pAmplifier + 1;
      pLivingEntity.m_6469_(TensuraDamageSources.MAGICULE_POISON, (float)(level * 2));
      if (pLivingEntity instanceof Player) {
         Player player = (Player)pLivingEntity;
         if (player.m_150110_().f_35934_) {
            return;
         }
      }

      pLivingEntity.m_7292_(new MobEffectInstance(MobEffects.f_19604_, 100, level / 2, false, false, false));
   }

   public boolean m_6584_(int pDuration, int pAmplifier) {
      return pDuration % 20 == 0;
   }

   public List<ItemStack> getCurativeItems() {
      return Collections.emptyList();
   }
}
