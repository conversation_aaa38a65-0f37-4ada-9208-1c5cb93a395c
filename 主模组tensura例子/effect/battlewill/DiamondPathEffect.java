package com.github.manasmods.tensura.effect.battlewill;

import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.effect.template.DamageAction;
import com.github.manasmods.tensura.effect.template.SkillMobEffect;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import java.util.UUID;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectCategory;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.attributes.AttributeModifier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.attributes.AttributeModifier.Operation;
import net.minecraftforge.event.entity.living.LivingHurtEvent;

public class DiamondPathEffect extends SkillMobEffect implements DamageAction {
   protected static final String ARMOR = "21d90e40-99c8-11ee-b9d1-0242ac120002";
   protected static final String KNOCK = "21d913d6-99c8-11ee-b9d1-0242ac120002";

   public DiamondPathEffect(MobEffectCategory pCategory, int pColor) {
      super(pCategory, pColor);
      this.m_19472_(Attributes.f_22284_, "21d90e40-99c8-11ee-b9d1-0242ac120002", 10.0D, Operation.ADDITION);
      this.m_19472_(Attributes.f_22278_, "21d913d6-99c8-11ee-b9d1-0242ac120002", 0.4D, Operation.ADDITION);
   }

   public void m_6742_(LivingEntity pLivingEntity, int pAmplifier) {
      if (pLivingEntity.m_21023_((MobEffect)TensuraMobEffects.CHILL.get())) {
         pLivingEntity.m_21195_((MobEffect)TensuraMobEffects.CHILL.get());
      }

   }

   public boolean m_6584_(int duration, int amplifier) {
      return duration % 20 == 0;
   }

   public void onBeingDamaged(LivingHurtEvent e) {
      if (DamageSourceHelper.isNaturalEffects(e.getSource())) {
         Entity var3 = e.getSource().m_7639_();
         if (var3 instanceof LivingEntity) {
            LivingEntity living = (LivingEntity)var3;
            if (SkillUtils.reducingResistances(living)) {
               return;
            }
         }

         if (e.getAmount() > e.getEntity().m_21223_() / 2.0F) {
            e.setAmount(e.getAmount() / 2.0F);
         }

      }
   }

   public double m_7048_(int pAmplifier, AttributeModifier pModifier) {
      return pModifier.m_22209_() == UUID.fromString("21d913d6-99c8-11ee-b9d1-0242ac120002") ? pModifier.m_22218_() : super.m_7048_(pAmplifier, pModifier);
   }
}
