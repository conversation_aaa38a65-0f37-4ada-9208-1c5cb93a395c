package com.github.manasmods.tensura.effect;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.capability.effects.TensuraEffectsCapability;
import com.github.manasmods.tensura.config.client.TensuraClientConfig;
import com.github.manasmods.tensura.effect.template.TensuraMobEffect;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.skill.ResistanceSkills;
import com.github.manasmods.tensura.registry.sound.TensuraSoundEvents;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Stream;
import net.minecraft.ChatFormatting;
import net.minecraft.core.NonNullList;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundSource;
import net.minecraft.util.RandomSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectCategory;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.attributes.AttributeMap;
import net.minecraft.world.entity.player.Inventory;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;
import net.minecraftforge.registries.ForgeRegistries;
import net.minecraftforge.registries.IForgeRegistry;

public class InsanityEffect extends TensuraMobEffect {
   private static List<SoundEvent> randomSounds;
   public static float ITEM_MOVE_CHANCE = 0.2F;

   public InsanityEffect(MobEffectCategory pCategory, int pColor) {
      super(pCategory, pColor);
   }

   public void m_6385_(LivingEntity pLivingEntity, AttributeMap pAttributeMap, int pAmplifier) {
      super.m_6385_(pLivingEntity, pAttributeMap, pAmplifier);
      if (pLivingEntity instanceof Player) {
         Player player = (Player)pLivingEntity;
         playInsanitySound((SoundEvent)TensuraSoundEvents.MC_ANIMAL1.get(), player, 0.7F);
      }

   }

   public void m_6742_(LivingEntity entity, int pAmplifier) {
      if (SkillUtils.isSkillToggled(entity, (ManasSkill)ResistanceSkills.ABNORMAL_CONDITION_RESISTANCE.get())) {
         pAmplifier -= 2;
      }

      if (pAmplifier >= 0) {
         damageEntity(entity, pAmplifier);
         if (entity instanceof Player) {
            Player player = (Player)entity;
            if (!player.m_5803_()) {
               if (randomSounds != null && !randomSounds.isEmpty()) {
                  MobEffectInstance instance = player.m_21124_(this);
                  if (instance != null && instance.m_19557_() % 80 == 0) {
                     SoundEvent event = null;
                     if ((player.m_21023_(MobEffects.f_19610_) || player.m_21023_(MobEffects.f_216964_)) && player.m_217043_().m_216332_(1, 20) == 3) {
                        event = (SoundEvent)TensuraSoundEvents.MC_DARK4.get();
                     }

                     if (event == null) {
                        event = (SoundEvent)randomSounds.get(player.m_217043_().m_188503_(randomSounds.size()));
                     }

                     if (player.m_217043_().m_188503_(10) == 3) {
                        float volume = event == TensuraSoundEvents.MC_VOICES3.get() ? 0.2F : 0.5F;
                        playInsanitySound(event, player, volume);
                     }

                  }
               }
            }
         }
      }
   }

   public static void damageEntity(LivingEntity entity, int amplifier) {
      if (amplifier > 0) {
         Player source;
         if (entity instanceof Player) {
            source = (Player)entity;
            if (source.m_5803_()) {
               return;
            }
         }

         source = TensuraEffectsCapability.getEffectSource(entity, (MobEffect)TensuraMobEffects.INSANITY.get());
         float spiritualDamage = 2.0F + (float)(amplifier * 2);
         if (source == null) {
            DamageSourceHelper.directSpiritualHurt(entity, (Entity)null, TensuraDamageSources.INSANITY, spiritualDamage);
         } else {
            DamageSourceHelper.directSpiritualHurt(entity, source, TensuraDamageSources.insanity(source), spiritualDamage);
         }

         if (entity.m_9236_().m_46803_(entity.m_20183_()) < 1 + amplifier * 3) {
            if (source == null) {
               entity.m_6469_(TensuraDamageSources.INSANITY, (float)amplifier);
            } else {
               entity.m_6469_(TensuraDamageSources.insanity(source), (float)amplifier);
            }
         }

      }
   }

   public boolean m_6584_(int pDuration, int pAmplifier) {
      return pDuration % 40 == 0;
   }

   public List<ItemStack> getCurativeItems() {
      return Collections.emptyList();
   }

   public static void onServerTick(ServerPlayer player) {
      MobEffectInstance insanity = player.m_21124_((MobEffect)TensuraMobEffects.INSANITY.get());
      if (insanity != null) {
         nightmareTicks(player);
         if (player.f_19797_ % 100 == 0 && player.m_217043_().m_188501_() <= ITEM_MOVE_CHANCE) {
            moveItems(player);
         }

      }
   }

   private static void moveItems(Player player) {
      Inventory inv = player.m_150109_();
      NonNullList<ItemStack> items = inv.f_35974_;
      RandomSource random = player.m_217043_();
      int slot1 = random.m_216339_(9, items.size());
      int slot2 = random.m_216339_(9, items.size());
      ItemStack item1 = inv.m_8020_(slot1).m_41777_();
      ItemStack item2 = inv.m_8020_(slot2).m_41777_();
      if (!item1.m_41619_() || !item2.m_41619_()) {
         inv.m_6836_(slot1, item2);
         inv.m_6836_(slot2, item1);
      }
   }

   private static void nightmareTicks(Player player) {
      TensuraEffectsCapability.getFrom(player).ifPresent((cap) -> {
         if (!havingNightmare(player)) {
            if (cap.getInsanityNightmare() > 0) {
               cap.setInsanityNightmare(0);
            }

            if (cap.getInsanityFOV() > 0) {
               cap.setInsanityFOV(0);
            }

            TensuraEffectsCapability.sync(player);
         } else {
            int nightmareTick = cap.getInsanityNightmare();
            switch(nightmareTick) {
            case 100:
               playInsanitySound((SoundEvent)TensuraSoundEvents.MC_ADDITION6.get(), player, 1.0F);
               break;
            case 140:
               player.m_7292_(new MobEffectInstance(MobEffects.f_216964_, 300, 1, false, false, false));
               break;
            case 160:
               wakeUp(player);
            }

            if (nightmareTick > 100 && nightmareTick < 160) {
               cap.setInsanityFOV(cap.getInsanityFOV() + 1);
            }

            cap.setInsanityNightmare(nightmareTick + 1);
            if (cap.getInsanityNightmare() > 160) {
               cap.setInsanityNightmare(-1);
            }

            TensuraEffectsCapability.sync(player);
         }
      });
   }

   private static void wakeUp(Player player) {
      MobEffectInstance instance = player.m_21124_((MobEffect)TensuraMobEffects.INSANITY.get());
      int amplifier = instance == null ? 1 : instance.m_19564_();
      player.m_5796_();
      damageEntity(player, amplifier + 2);
      playInsanitySound((SoundEvent)TensuraSoundEvents.MC_DARK1.get(), player, 1.0F);
      String key = "effect.tensura.insanity.";
      List<MutableComponent> randomMessages = new ArrayList(List.of(Component.m_237115_(key + "voices"), Component.m_237115_(key + "gaze"), Component.m_237115_(key + "fear"), Component.m_237115_(key + "unknown")));
      int randomMessage = player.m_217043_().m_188503_(randomMessages.size());
      player.m_5661_(((MutableComponent)randomMessages.get(randomMessage)).m_130940_(ChatFormatting.RED), false);
      player.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.FEAR.get(), 160, 0, false, false, false));
      player.m_7292_(new MobEffectInstance(MobEffects.f_19610_, 60, 1, false, false, false));
   }

   public static void loadConfig() {
      Stream var10000 = ((List)TensuraClientConfig.INSTANCE.effectsConfig.sounds.get()).stream().map(ResourceLocation::new);
      IForgeRegistry var10001 = ForgeRegistries.SOUND_EVENTS;
      Objects.requireNonNull(var10001);
      randomSounds = var10000.map(var10001::getValue).filter(Objects::nonNull).toList();
   }

   public static boolean havingNightmare(LivingEntity entity) {
      MobEffectInstance insanity = entity.m_21124_((MobEffect)TensuraMobEffects.INSANITY.get());
      return insanity != null && entity.m_5803_();
   }

   public static void playInsanitySound(SoundEvent event, Player player, float volume) {
      player.m_6330_(event, SoundSource.HOSTILE, volume, 1.0F);
   }
}
