package com.github.manasmods.tensura.effect;

import com.github.manasmods.tensura.effect.template.DamageAction;
import com.github.manasmods.tensura.effect.template.TensuraMobEffect;
import java.util.Objects;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffectCategory;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.attributes.AttributeModifier.Operation;
import net.minecraft.world.entity.player.Player;
import net.minecraftforge.common.ForgeMod;
import net.minecraftforge.event.entity.living.LivingHurtEvent;

public class BurdenEffect extends TensuraMobEffect implements DamageAction {
   protected static final String BURDEN_KNOCKBACK_RESISTANCE_UUID = "3111d3a4-d7b5-11ed-afa1-0242ac120002";
   protected static final String BURDEN_STEP_HEIGHT_UUID = "3111d624-d7b5-11ed-afa1-0242ac120002";

   public BurdenEffect(MobEffectCategory pCategory, int pColor) {
      super(pCategory, pColor);
      this.m_19472_(Attributes.f_22278_, "3111d3a4-d7b5-11ed-afa1-0242ac120002", 0.10000000149011612D, Operation.ADDITION);
      this.m_19472_((Attribute)ForgeMod.STEP_HEIGHT_ADDITION.get(), "3111d624-d7b5-11ed-afa1-0242ac120002", -0.10000000149011612D, Operation.MULTIPLY_TOTAL);
   }

   public void m_6742_(LivingEntity entity, int pAmplifier) {
      boolean var10000;
      label26: {
         if (entity instanceof Player) {
            Player player = (Player)entity;
            if (!player.m_7500_() || !player.m_150110_().f_35935_) {
               var10000 = true;
               break label26;
            }
         }

         var10000 = false;
      }

      boolean fall = var10000;
      if ((fall || !(entity instanceof Player)) && !entity.m_20096_()) {
         entity.m_20256_(entity.m_20184_().m_82520_(0.0D, (double)(-0.1F * (float)(pAmplifier + 1)), 0.0D));
      }

   }

   public boolean m_6584_(int pDuration, int pAmplifier) {
      return pDuration > 0;
   }

   public void onBeingDamaged(LivingHurtEvent e) {
      if (e.getSource().equals(DamageSource.f_19315_)) {
         float effectLevel = (float)(((MobEffectInstance)Objects.requireNonNull(e.getEntity().m_21124_(this))).m_19564_() + 1);
         e.setAmount(e.getAmount() * (1.0F + 0.5F * effectLevel));
      }
   }
}
