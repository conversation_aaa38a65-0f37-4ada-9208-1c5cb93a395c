package com.github.manasmods.tensura.effect.template;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.capability.effects.TensuraEffectsCapability;
import com.github.manasmods.tensura.capability.skill.TensuraSkillCapability;
import com.github.manasmods.tensura.registry.dimensions.TensuraDimensions;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.skill.ResistanceSkills;
import com.github.manasmods.tensura.registry.skill.UniqueSkills;
import net.minecraft.resources.ResourceKey;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;

public class MobEffectHelper {
   public static int getSpiritualResistLevel(LivingEntity entity) {
      if (SkillUtils.isSkillToggled(entity, (ManasSkill)ResistanceSkills.SPIRITUAL_ATTACK_NULLIFICATION.get())) {
         return 2;
      } else {
         return SkillUtils.isSkillToggled(entity, (ManasSkill)ResistanceSkills.SPIRITUAL_ATTACK_RESISTANCE.get()) ? 1 : 0;
      }
   }

   public static boolean inSpiritualWorld(ResourceKey<Level> dimension) {
      return dimension == TensuraDimensions.HELL || dimension == TensuraDimensions.LABYRINTH;
   }

   public static boolean hasTrueInvisibility(LivingEntity user) {
      return TensuraEffectsCapability.getPresenceConcealment(user) > 0;
   }

   public static boolean canSeeInvisibleTarget(Player player, LivingEntity target) {
      int concealment = TensuraEffectsCapability.getPresenceConcealment(target);
      int sense = TensuraEffectsCapability.getPresenceSense(player);
      if (concealment == 2) {
         return sense > concealment ? true : TensuraSkillCapability.isSkillInSlot(player, (ManasSkill)UniqueSkills.FALSIFIER.get());
      } else {
         return sense > concealment;
      }
   }

   public static boolean noTeleportation(LivingEntity target) {
      if (target.m_21023_((MobEffect)TensuraMobEffects.LUST_EMBRACEMENT.get())) {
         return true;
      } else {
         return target.m_21023_((MobEffect)TensuraMobEffects.INFINITE_IMPRISONMENT.get()) ? true : target.m_21023_((MobEffect)TensuraMobEffects.SPATIAL_BLOCKADE.get());
      }
   }

   public static boolean noDimensionChanging(LivingEntity target) {
      if (target.m_21023_((MobEffect)TensuraMobEffects.SHADOW_STEP.get())) {
         return true;
      } else if (target.m_21023_((MobEffect)TensuraMobEffects.LUST_EMBRACEMENT.get())) {
         return true;
      } else {
         return target.m_21023_((MobEffect)TensuraMobEffects.INFINITE_IMPRISONMENT.get()) ? true : target.m_21023_((MobEffect)TensuraMobEffects.SPATIAL_BLOCKADE.get());
      }
   }

   public static boolean shouldCancelHeal(LivingEntity entity) {
      MobEffectInstance instance = entity.m_21124_((MobEffect)TensuraMobEffects.INSTANT_REGENERATION.get());
      if (instance != null && instance.m_19564_() >= 1) {
         return false;
      } else if (entity.m_21023_((MobEffect)TensuraMobEffects.CURSE.get())) {
         return true;
      } else if (entity.m_21023_((MobEffect)TensuraMobEffects.FROST.get())) {
         return true;
      } else {
         MobEffectInstance backBurnt = entity.m_21124_((MobEffect)TensuraMobEffects.BLACK_BURN.get());
         return backBurnt != null && backBurnt.m_19564_() >= 1;
      }
   }
}
