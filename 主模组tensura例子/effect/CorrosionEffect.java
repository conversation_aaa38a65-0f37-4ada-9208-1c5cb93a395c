package com.github.manasmods.tensura.effect;

import com.github.manasmods.tensura.capability.effects.TensuraEffectsCapability;
import com.github.manasmods.tensura.effect.template.TensuraMobEffect;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import net.minecraft.world.effect.MobEffectCategory;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;

public class CorrosionEffect extends TensuraMobEffect {
   public CorrosionEffect(MobEffectCategory pCategory, int pColor) {
      super(pCategory, pColor);
   }

   public void m_6742_(LivingEntity pLivingEntity, int pAmplifier) {
      if (pLivingEntity.m_21223_() > 0.0F) {
         int durabilityCorrosion = 15 * (pAmplifier + 1);
         float damage = 2.0F * (float)(pAmplifier + 1);
         Player source = TensuraEffectsCapability.getEffectSource(pLivingEntity, this);
         if (source == null) {
            pLivingEntity.m_6469_(TensuraDamageSources.CORROSION, damage);
         } else {
            pLivingEntity.m_6469_(TensuraDamageSources.corrosion(source), damage);
         }

         EquipmentSlot[] var6 = EquipmentSlot.values();
         int var7 = var6.length;

         for(int var8 = 0; var8 < var7; ++var8) {
            EquipmentSlot slot = var6[var8];
            ItemStack slotStack = pLivingEntity.m_6844_(slot);
            slotStack.m_41622_(durabilityCorrosion, pLivingEntity, (player) -> {
               player.m_21166_(slot);
            });
         }
      }

   }

   public boolean m_6584_(int pDuration, int pAmplifier) {
      return pDuration % 20 == 0;
   }
}
