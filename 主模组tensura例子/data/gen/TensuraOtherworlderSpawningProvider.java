package com.github.manasmods.tensura.data.gen;

import com.github.manasmods.manascore.api.data.gen.CustomDataProvider;
import com.github.manasmods.tensura.data.pack.OtherworlderSpawning;
import com.google.gson.JsonElement;
import java.util.function.BiConsumer;
import java.util.function.Supplier;
import net.minecraft.resources.ResourceLocation;
import net.minecraftforge.data.event.GatherDataEvent;

public class TensuraOtherworlderSpawningProvider extends CustomDataProvider {
   public TensuraOtherworlderSpawningProvider(GatherDataEvent gatherDataEvent) {
      super("otherworlder_spawning", gatherDataEvent.getGenerator());
   }

   public String m_6055_() {
      return "Otherworlder Spawning";
   }

   protected void run(BiConsumer<ResourceLocation, Supplier<JsonElement>> biConsumer) {
      OtherworlderSpawning.of("tensura:folgen", 0.125F).buildJson(biConsumer);
      OtherworlderSpawning.of("tensura:kyoya_tachibana", 0.126F).buildJson(biConsumer);
      OtherworlderSpawning.of("tensura:kirara_mizutani", 0.126F).buildJson(biConsumer);
      OtherworlderSpawning.of("tensura:mai_furuki", 0.123F).buildJson(biConsumer);
      OtherworlderSpawning.of("tensura:mark_lauren", 0.125F).buildJson(biConsumer);
      OtherworlderSpawning.of("tensura:shin_ryusei", 0.125F).buildJson(biConsumer);
      OtherworlderSpawning.of("tensura:shinji_tanimura", 0.124F).buildJson(biConsumer);
      OtherworlderSpawning.of("tensura:shogo_taguchi", 0.126F).buildJson(biConsumer);
   }
}
