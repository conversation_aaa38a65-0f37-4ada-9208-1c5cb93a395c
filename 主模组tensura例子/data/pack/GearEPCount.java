package com.github.manasmods.tensura.data.pack;

import com.google.gson.JsonElement;
import com.mojang.serialization.Codec;
import com.mojang.serialization.JsonOps;
import com.mojang.serialization.codecs.RecordCodecBuilder;
import java.util.function.BiConsumer;
import java.util.function.Supplier;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.item.Items;

public class GearEPCount {
   public static final Codec<GearEPCount> CODEC = RecordCodecBuilder.create((instance) -> {
      return instance.group(ResourceLocation.f_135803_.fieldOf("item").forGetter(GearEPCount::getItem), Codec.INT.fieldOf("minEP").forGetter(GearEPCount::getMinEP), Codec.INT.fieldOf("maxEP").forGetter(GearEPCount::getMaxEP), Codec.DOUBLE.fieldOf("gainEP").forGetter(GearEPCount::getGainEP), ResourceLocation.f_135803_.fieldOf("evolvingItem").forGetter(GearEPCount::getEvolvingItem)).apply(instance, GearEPCount::new);
   });
   private final ResourceLocation item;
   private final int minEP;
   private final int maxEP;
   private final double gainEP;
   private final ResourceLocation evolvingItem;

   public static GearEPCount of(ResourceLocation item, int minEP, int maxEP, double gainEP, ResourceLocation evolvingItem) {
      return new GearEPCount(item, minEP, maxEP, gainEP, evolvingItem);
   }

   public static GearEPCount of(ResourceLocation item, int minEP, int maxEP, double gainEP) {
      return new GearEPCount(item, minEP, maxEP, gainEP, new ResourceLocation(Items.f_41852_.toString()));
   }

   public void buildJson(BiConsumer<ResourceLocation, Supplier<JsonElement>> consumer) {
      consumer.accept(this.item, () -> {
         return (JsonElement)CODEC.encodeStart(JsonOps.INSTANCE, this).result().orElseThrow(() -> {
            return new IllegalStateException("Could not serialize " + this);
         });
      });
   }

   public GearEPCount(ResourceLocation item, int minEP, int maxEP, double gainEP, ResourceLocation evolvingItem) {
      this.item = item;
      this.minEP = minEP;
      this.maxEP = maxEP;
      this.gainEP = gainEP;
      this.evolvingItem = evolvingItem;
   }

   public String toString() {
      ResourceLocation var10000 = this.getItem();
      return "GearEPCount(item=" + var10000 + ", minEP=" + this.getMinEP() + ", maxEP=" + this.getMaxEP() + ", gainEP=" + this.getGainEP() + ", evolvingItem=" + this.getEvolvingItem() + ")";
   }

   public ResourceLocation getItem() {
      return this.item;
   }

   public int getMinEP() {
      return this.minEP;
   }

   public int getMaxEP() {
      return this.maxEP;
   }

   public double getGainEP() {
      return this.gainEP;
   }

   public ResourceLocation getEvolvingItem() {
      return this.evolvingItem;
   }
}
