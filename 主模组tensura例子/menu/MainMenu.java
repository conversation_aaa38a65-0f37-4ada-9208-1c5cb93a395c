package com.github.manasmods.tensura.menu;

import com.github.manasmods.tensura.registry.menu.TensuraMenuTypes;
import net.minecraft.network.FriendlyByteBuf;
import net.minecraft.world.entity.player.Inventory;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.inventory.AbstractContainerMenu;
import net.minecraft.world.inventory.MenuType;
import net.minecraft.world.item.ItemStack;

public class MainMenu extends AbstractContainerMenu {
   private int soulRequirement;

   public MainMenu(int pContainerId, Inventory inventory, FriendlyByteBuf buf) {
      this(pContainerId, inventory, inventory.f_35978_);
      this.soulRequirement = buf.readInt();
   }

   public MainMenu(int pContainerId, Inventory inventory, Player player) {
      super((MenuType)TensuraMenuTypes.MAIN_MENU.get(), pContainerId);
   }

   public boolean m_6875_(Player pPlayer) {
      return true;
   }

   public ItemStack m_7648_(Player pPlayer, int pIndex) {
      return ItemStack.f_41583_;
   }

   public boolean check() {
      return true;
   }

   public int getSoulRequirement() {
      return this.soulRequirement;
   }
}
