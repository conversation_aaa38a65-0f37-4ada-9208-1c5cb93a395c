package com.github.manasmods.tensura.menu;

import com.github.manasmods.tensura.enchantment.EngravingEnchantment;
import com.github.manasmods.tensura.registry.menu.TensuraMenuTypes;
import java.util.Iterator;
import java.util.Map;
import java.util.Map.Entry;
import java.util.stream.Collectors;
import net.minecraft.network.FriendlyByteBuf;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.Container;
import net.minecraft.world.SimpleContainer;
import net.minecraft.world.entity.player.Inventory;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.inventory.AbstractContainerMenu;
import net.minecraft.world.inventory.ContainerLevelAccess;
import net.minecraft.world.inventory.MenuType;
import net.minecraft.world.inventory.ResultContainer;
import net.minecraft.world.inventory.Slot;
import net.minecraft.world.item.CrossbowItem;
import net.minecraft.world.item.EnchantedBookItem;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;
import net.minecraft.world.item.enchantment.Enchantment;
import net.minecraft.world.item.enchantment.EnchantmentHelper;
import net.minecraft.world.item.enchantment.EnchantmentInstance;
import net.minecraft.world.item.enchantment.Enchantments;

public class DegenerateEnchantmentMenu extends AbstractContainerMenu {
   private static final int HOTBAR_SLOT_COUNT = 9;
   private static final int INVENTORY_ROW_COUNT = 3;
   private static final int INVENTORY_COLUMN_COUNT = 9;
   private static final int PLAYER_INVENTORY_SLOT_COUNT = 27;
   private static final int VANILLA_SLOT_COUNT = 36;
   private final Player player;
   private final ResultContainer anvilResultSlot;
   public final ResultContainer disenchanterItemOutput;
   public final ResultContainer disenchanterBookOutput;
   private final Container anvilInputSlots;
   public final Container disenchanterInputSlots;
   private final ContainerLevelAccess access;
   private int xp;
   Runnable slotUpdateListener;

   public DegenerateEnchantmentMenu(int pContainerId, Inventory inventory, FriendlyByteBuf buf) {
      this(pContainerId, inventory, ContainerLevelAccess.f_39287_);
   }

   public DegenerateEnchantmentMenu(int pContainerId, Inventory inventory, ContainerLevelAccess access) {
      super((MenuType)TensuraMenuTypes.DEGENERATE_ENCHANTMENT_MENU.get(), pContainerId);
      this.anvilResultSlot = new ResultContainer();
      this.disenchanterItemOutput = new ResultContainer();
      this.disenchanterBookOutput = new ResultContainer();
      this.anvilInputSlots = new SimpleContainer(2);
      this.disenchanterInputSlots = new SimpleContainer(2) {
         public void m_6596_() {
            super.m_6596_();
            DegenerateEnchantmentMenu.this.m_6199_(this);
            DegenerateEnchantmentMenu.this.slotUpdateListener.run();
         }
      };
      this.slotUpdateListener = () -> {
      };
      this.access = access;
      this.player = inventory.f_35978_;
      this.addPlayerInventorySlots(inventory);
      this.addGrindstoneSlots();
      this.addAnvilSlots();
   }

   public void createResult() {
      ItemStack inputItem = this.anvilInputSlots.m_8020_(0);
      if (inputItem.m_41619_()) {
         this.anvilResultSlot.m_6836_(0, ItemStack.f_41583_);
      } else {
         ItemStack copyInput = inputItem.m_41777_();
         ItemStack inputBook = this.anvilInputSlots.m_8020_(1);
         Map<Enchantment, Integer> map = EnchantmentHelper.m_44831_(copyInput);
         if (!inputBook.m_41619_()) {
            boolean shouldEnchant = inputBook.m_41720_() == Items.f_42690_ && !EnchantedBookItem.m_41163_(inputBook).isEmpty();
            int repairDamage;
            int repairedDamage;
            if (copyInput.m_41763_() && copyInput.m_41720_().m_6832_(inputItem, inputBook)) {
               repairDamage = Math.min(copyInput.m_41773_(), copyInput.m_41776_() / 4);
               if (repairDamage <= 0) {
                  this.anvilResultSlot.m_6836_(0, ItemStack.f_41583_);
                  return;
               }

               repairedDamage = copyInput.m_41773_() - repairDamage;
               copyInput.m_41721_(repairedDamage);
            } else {
               if (!shouldEnchant && (!copyInput.m_150930_(inputBook.m_41720_()) || !copyInput.m_41763_())) {
                  this.anvilResultSlot.m_6836_(0, ItemStack.f_41583_);
                  return;
               }

               if (copyInput.m_41763_() && !shouldEnchant) {
                  repairDamage = inputItem.m_41776_() - inputItem.m_41773_();
                  int inputCount = inputBook.m_41776_() - inputBook.m_41773_();
                  repairedDamage = inputCount + copyInput.m_41776_() * 12 / 100;
                  int newDurability = copyInput.m_41776_() - (repairDamage + repairedDamage);
                  if (newDurability < 0) {
                     newDurability = 0;
                  }

                  if (newDurability < copyInput.m_41773_()) {
                     copyInput.m_41721_(newDurability);
                  }
               }

               Map<Enchantment, Integer> bookEnchantments = EnchantmentHelper.m_44831_(inputBook);
               boolean doneEnchanted = false;
               boolean stopEnchant = false;
               Iterator iterator = bookEnchantments.keySet().iterator();

               label94:
               while(true) {
                  Enchantment bookEnchant;
                  do {
                     if (!iterator.hasNext()) {
                        if (stopEnchant && !doneEnchanted) {
                           this.anvilResultSlot.m_6836_(0, ItemStack.f_41583_);
                           return;
                        }
                        break label94;
                     }

                     bookEnchant = (Enchantment)iterator.next();
                  } while(bookEnchant == null);

                  int itemLevel = (Integer)map.getOrDefault(bookEnchant, 0);
                  int bookLevel = (Integer)bookEnchantments.get(bookEnchant);
                  bookLevel = itemLevel == bookLevel ? bookLevel + 1 : Math.max(bookLevel, itemLevel);
                  boolean canEnchant = bookEnchant.m_6081_(inputItem);
                  if (inputItem.m_150930_(Items.f_42690_) || inputItem.m_150930_(Items.f_42398_)) {
                     canEnchant = true;
                  }

                  if (!canEnchant) {
                     stopEnchant = true;
                  } else {
                     doneEnchanted = true;
                     int maxLevel = bookEnchant.m_6586_();
                     if (this.canExceedMax(bookEnchant, copyInput)) {
                        maxLevel += 2;
                     }

                     if (bookLevel > maxLevel) {
                        bookLevel = maxLevel;
                     }

                     map.put(bookEnchant, bookLevel);
                  }
               }
            }

            if (shouldEnchant && !copyInput.isBookEnchantable(inputBook)) {
               copyInput = ItemStack.f_41583_;
            }

            if (!copyInput.m_41619_()) {
               EnchantmentHelper.m_44865_(map, copyInput);
            }

            this.anvilResultSlot.m_6836_(0, copyInput);
            this.m_38946_();
         } else {
            this.anvilResultSlot.m_6836_(0, ItemStack.f_41583_);
         }
      }

   }

   public Map<Enchantment, Integer> getInputEnchantments() {
      return (Map)EnchantmentHelper.m_44831_(this.disenchanterInputSlots.m_8020_(0)).entrySet().stream().filter((entry) -> {
         return !(entry.getKey() instanceof EngravingEnchantment);
      }).collect(Collectors.toMap(Entry::getKey, Entry::getValue));
   }

   public void removeEnchantment(Enchantment enchantment, int level) {
      ItemStack outputStack = this.disenchanterInputSlots.m_8020_(0).m_41777_();
      Map<Enchantment, Integer> map = EnchantmentHelper.m_44831_(outputStack);
      outputStack.m_41749_("Enchantments");
      outputStack.m_41749_("StoredEnchantments");
      map.remove(enchantment);
      EnchantmentHelper.m_44865_(map, outputStack);
      ItemStack outputBook = this.disenchanterInputSlots.m_8020_(1).m_41777_();
      if (this.disenchanterBookOutput.m_8020_(0).m_41619_()) {
         if (outputBook.m_41619_()) {
            this.xp += enchantment.m_6183_(level) + this.player.m_217043_().m_216339_(0, enchantment.m_6175_(level) - enchantment.m_6183_(level));
         } else {
            if (outputBook.m_150930_(Items.f_42517_)) {
               outputBook = new ItemStack(Items.f_42690_);
            }

            EnchantedBookItem.m_41153_(outputBook, new EnchantmentInstance(enchantment, level));
            this.disenchanterBookOutput.m_6836_(0, outputBook);
         }

         this.disenchanterInputSlots.m_8020_(1).m_41774_(1);
      } else {
         outputBook = this.disenchanterBookOutput.m_8020_(0).m_41777_();
         EnchantedBookItem.m_41153_(outputBook, new EnchantmentInstance(enchantment, level));
         this.disenchanterBookOutput.m_6836_(0, outputBook);
      }

      if (outputStack.m_150930_(Items.f_42690_) && map.isEmpty()) {
         outputStack = new ItemStack(Items.f_42517_);
      }

      this.disenchanterItemOutput.m_6836_(0, outputStack.m_41777_());
      if (this.xp > 0) {
         this.player.m_6756_(this.xp);
         this.player.m_6330_(SoundEvents.f_11871_, SoundSource.PLAYERS, 1.0F, 1.0F);
         this.xp = 0;
      }

      this.disenchanterInputSlots.m_8020_(0).m_41774_(1);
      this.m_38946_();
   }

   private void addPlayerInventorySlots(Inventory playerInventory) {
      int i;
      for(i = 0; i < 3; ++i) {
         for(int l = 0; l < 9; ++l) {
            this.m_38897_(new Slot(playerInventory, l + i * 9 + 9, 25 + l * 18, 117 + i * 18));
         }
      }

      for(i = 0; i < 9; ++i) {
         this.m_38897_(new Slot(playerInventory, i, 25 + i * 18, 175));
      }

   }

   private void addAnvilSlots() {
      this.m_38897_(new Slot(this.anvilResultSlot, 0, 175, 30) {
         public boolean m_5857_(ItemStack pStack) {
            return false;
         }

         public void m_142406_(Player pPlayer, ItemStack pStack) {
            super.m_142406_(pPlayer, pStack);
            pPlayer.m_216990_(SoundEvents.f_11887_);
            DegenerateEnchantmentMenu.this.anvilInputSlots.m_8020_(0).m_41774_(1);
            DegenerateEnchantmentMenu.this.anvilInputSlots.m_8020_(1).m_41774_(1);
         }
      });
      this.m_38897_(new Slot(this.anvilInputSlots, 0, 69, 30) {
         public int m_6641_() {
            return 1;
         }

         public void m_5852_(ItemStack pStack) {
            super.m_5852_(pStack);
            if (!DegenerateEnchantmentMenu.this.anvilInputSlots.m_8020_(1).m_41619_()) {
               DegenerateEnchantmentMenu.this.createResult();
            }

         }
      });
      this.m_38897_(new Slot(this.anvilInputSlots, 1, 118, 30) {
         public void m_5852_(ItemStack pStack) {
            super.m_5852_(pStack);
            if (!DegenerateEnchantmentMenu.this.anvilInputSlots.m_8020_(0).m_41619_()) {
               DegenerateEnchantmentMenu.this.createResult();
            }

         }
      });
   }

   private void addGrindstoneSlots() {
      this.m_38897_(new Slot(this.disenchanterItemOutput, 0, 175, 65) {
         public boolean m_5857_(ItemStack pStack) {
            return false;
         }
      });
      this.m_38897_(new Slot(this.disenchanterBookOutput, 0, 175, 83) {
         public boolean m_5857_(ItemStack pStack) {
            return false;
         }
      });
      this.m_38897_(new Slot(this.disenchanterInputSlots, 0, 19, 65) {
         public int m_6641_() {
            return 1;
         }

         public boolean m_5857_(ItemStack pStack) {
            return DegenerateEnchantmentMenu.this.disenchanterItemOutput.m_7983_();
         }
      });
      this.m_38897_(new Slot(this.disenchanterInputSlots, 1, 19, 83) {
         public boolean m_5857_(ItemStack pStack) {
            return pStack.m_150930_(Items.f_42517_) || pStack.m_150930_(Items.f_42690_);
         }
      });
   }

   public void registerUpdateListener(Runnable pListener) {
      this.slotUpdateListener = pListener;
   }

   public boolean check() {
      return true;
   }

   public boolean m_6875_(Player player) {
      return true;
   }

   public boolean canExceedMax(Enchantment enchantment, ItemStack stack) {
      if (enchantment instanceof EngravingEnchantment) {
         return false;
      } else if (enchantment.m_6586_() > 1) {
         return true;
      } else if (enchantment.equals(Enchantments.f_44959_)) {
         return !(stack.m_41720_() instanceof CrossbowItem);
      } else if (enchantment.equals(Enchantments.f_44971_)) {
         return false;
      } else if (enchantment.equals(Enchantments.f_44958_)) {
         return false;
      } else if (enchantment.equals(Enchantments.f_44975_)) {
         return false;
      } else if (enchantment.equals(Enchantments.f_44963_)) {
         return false;
      } else if (enchantment.equals(Enchantments.f_44990_)) {
         return false;
      } else if (enchantment.equals(Enchantments.f_44952_)) {
         return false;
      } else if (enchantment.equals(Enchantments.f_44962_)) {
         return false;
      } else {
         return !enchantment.equals(Enchantments.f_44985_);
      }
   }

   public void m_6877_(Player pPlayer) {
      this.access.m_39292_((level, pos) -> {
         this.m_150411_(pPlayer, this.anvilInputSlots);
         this.m_150411_(pPlayer, this.disenchanterInputSlots);
         this.m_150411_(pPlayer, this.disenchanterItemOutput);
         this.m_150411_(pPlayer, this.disenchanterBookOutput);
      });
      super.m_6877_(pPlayer);
   }

   public ItemStack m_7648_(Player pPlayer, int index) {
      Slot sourceSlot = (Slot)this.f_38839_.get(index);
      if (!sourceSlot.m_6657_()) {
         return ItemStack.f_41583_;
      } else {
         ItemStack sourceStack = sourceSlot.m_7993_();
         ItemStack copyStack = sourceStack.m_41777_();
         if (index < 27) {
            return !this.m_38903_(sourceStack, 27, 36, false) ? ItemStack.f_41583_ : TensuraMenuHelper.quickMoveStack(pPlayer, sourceStack, sourceSlot, copyStack);
         } else if (index < 36) {
            return !this.m_38903_(sourceStack, 0, 27, false) ? ItemStack.f_41583_ : TensuraMenuHelper.quickMoveStack(pPlayer, sourceStack, sourceSlot, copyStack);
         } else if (!sourceSlot.m_8010_(pPlayer)) {
            return ItemStack.f_41583_;
         } else if (!this.m_38903_(sourceStack, 0, 36, false)) {
            return ItemStack.f_41583_;
         } else {
            sourceSlot.m_40234_(sourceStack, copyStack);
            return TensuraMenuHelper.quickMoveStack(pPlayer, sourceStack, sourceSlot, copyStack);
         }
      }
   }

   public ContainerLevelAccess getAccess() {
      return this.access;
   }
}
