package com.github.manasmods.tensura.menu;

import com.github.manasmods.tensura.entity.template.HumanoidNPCEntity;
import javax.annotation.Nullable;
import net.minecraft.world.Container;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.entity.EquipmentSlot.Type;
import net.minecraft.world.entity.player.Inventory;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.inventory.AbstractContainerMenu;
import net.minecraft.world.inventory.MenuType;
import net.minecraft.world.inventory.Slot;
import net.minecraft.world.item.ArmorItem;
import net.minecraft.world.item.BlockItem;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;
import net.minecraft.world.level.block.AbstractSkullBlock;
import net.minecraftforge.common.ToolActions;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class HumanoidNPCMenu extends AbstractContainerMenu {
   private static final Logger log = LogManager.getLogger(HumanoidNPCMenu.class);
   private static final int HOTBAR_SLOT_COUNT = 9;
   private static final int PLAYER_INVENTORY_ROW_COUNT = 3;
   private static final int PLAYER_INVENTORY_COLUMN_COUNT = 9;
   private static final int PLAYER_INVENTORY_SLOT_COUNT = 27;
   private static final int VANILLA_SLOT_COUNT = 36;
   private static final int VANILLA_FIRST_SLOT_INDEX = 0;
   private static final int TE_INVENTORY_FIRST_SLOT_INDEX = 36;
   public final HumanoidNPCEntity entity;
   public final double EP;
   private final Container container;

   public HumanoidNPCMenu(int pContainerId, Inventory pPlayerInventory, Container container, HumanoidNPCEntity entity, double EP) {
      super((MenuType)null, pContainerId);
      this.entity = entity;
      this.container = entity.inventory;
      this.EP = EP;
      m_38869_(container, 18);
      container.m_5856_(pPlayerInventory.f_35978_);
      this.addSubordinateInventory(container);
      this.addPlayerInventory(pPlayerInventory);
      this.addPlayerHotbar(pPlayerInventory);
   }

   public ItemStack m_7648_(Player pPlayer, int pIndex) {
      ItemStack quickMoveStack = ItemStack.f_41583_;
      Slot slot = (Slot)this.f_38839_.get(pIndex);
      if (slot.m_6657_()) {
         ItemStack stack = slot.m_7993_();
         quickMoveStack = stack.m_41777_();
         if (pIndex < 18) {
            if (!this.m_38903_(stack, 18, this.f_38839_.size(), true)) {
               return ItemStack.f_41583_;
            }
         } else if (!this.m_38903_(stack, 0, 18, false)) {
            return ItemStack.f_41583_;
         }

         if (stack.m_41619_()) {
            slot.m_5852_(ItemStack.f_41583_);
         } else {
            slot.m_6654_();
         }
      }

      return quickMoveStack;
   }

   public boolean m_6875_(Player player) {
      return this.container.m_6542_(player) && this.entity.m_6084_() && this.entity.m_20270_(player) < 8.0F;
   }

   private void addSubordinateInventory(Container container) {
      if (this.entity.canEquipSlots(EquipmentSlot.HEAD)) {
         this.m_38897_(new Slot(container, 0, 8, 8) {
            public boolean m_5857_(ItemStack pStack) {
               if (!HumanoidNPCMenu.this.entity.canEquipSlots(EquipmentSlot.HEAD, pStack)) {
                  return false;
               } else if (pStack.getEquipmentSlot() == EquipmentSlot.HEAD) {
                  return true;
               } else if (pStack.m_150930_(Items.f_42047_)) {
                  return true;
               } else {
                  Item var3 = pStack.m_41720_();
                  if (var3 instanceof BlockItem) {
                     BlockItem block = (BlockItem)var3;
                     if (block.m_40614_() instanceof AbstractSkullBlock) {
                        return true;
                     }
                  }

                  var3 = pStack.m_41720_();
                  boolean var10000;
                  if (var3 instanceof ArmorItem) {
                     ArmorItem armorItem = (ArmorItem)var3;
                     if (armorItem.m_40402_().equals(EquipmentSlot.HEAD)) {
                        var10000 = true;
                        return var10000;
                     }
                  }

                  var10000 = false;
                  return var10000;
               }
            }

            public void m_5852_(ItemStack pStack) {
               super.m_5852_(pStack);
               HumanoidNPCMenu.this.entity.m_8061_(EquipmentSlot.HEAD, pStack);
            }
         });
      }

      if (this.entity.canEquipSlots(EquipmentSlot.CHEST)) {
         this.m_38897_(new Slot(container, 1, 8, 26) {
            public boolean m_5857_(ItemStack pStack) {
               if (!HumanoidNPCMenu.this.entity.canEquipSlots(EquipmentSlot.CHEST, pStack)) {
                  return false;
               } else if (pStack.getEquipmentSlot() == EquipmentSlot.CHEST) {
                  return true;
               } else {
                  Item var3 = pStack.m_41720_();
                  boolean var10000;
                  if (var3 instanceof ArmorItem) {
                     ArmorItem armorItem = (ArmorItem)var3;
                     if (armorItem.m_40402_().equals(EquipmentSlot.CHEST)) {
                        var10000 = true;
                        return var10000;
                     }
                  }

                  var10000 = false;
                  return var10000;
               }
            }

            public void m_5852_(ItemStack pStack) {
               super.m_5852_(pStack);
               HumanoidNPCMenu.this.entity.m_8061_(EquipmentSlot.CHEST, pStack);
            }
         });
      }

      if (this.entity.canEquipSlots(EquipmentSlot.LEGS)) {
         this.m_38897_(new Slot(container, 2, 8, 44) {
            public boolean m_5857_(ItemStack pStack) {
               if (!HumanoidNPCMenu.this.entity.canEquipSlots(EquipmentSlot.LEGS, pStack)) {
                  return false;
               } else if (pStack.getEquipmentSlot() == EquipmentSlot.LEGS) {
                  return true;
               } else {
                  Item var3 = pStack.m_41720_();
                  boolean var10000;
                  if (var3 instanceof ArmorItem) {
                     ArmorItem armorItem = (ArmorItem)var3;
                     if (armorItem.m_40402_().equals(EquipmentSlot.LEGS)) {
                        var10000 = true;
                        return var10000;
                     }
                  }

                  var10000 = false;
                  return var10000;
               }
            }

            public void m_5852_(ItemStack pStack) {
               super.m_5852_(pStack);
               HumanoidNPCMenu.this.entity.m_8061_(EquipmentSlot.LEGS, pStack);
            }
         });
      }

      if (this.entity.canEquipSlots(EquipmentSlot.FEET)) {
         this.m_38897_(new Slot(container, 3, 8, 62) {
            public boolean m_5857_(ItemStack pStack) {
               if (!HumanoidNPCMenu.this.entity.canEquipSlots(EquipmentSlot.FEET, pStack)) {
                  return false;
               } else if (pStack.getEquipmentSlot() == EquipmentSlot.FEET) {
                  return true;
               } else {
                  Item var3 = pStack.m_41720_();
                  boolean var10000;
                  if (var3 instanceof ArmorItem) {
                     ArmorItem armorItem = (ArmorItem)var3;
                     if (armorItem.m_40402_().equals(EquipmentSlot.FEET)) {
                        var10000 = true;
                        return var10000;
                     }
                  }

                  var10000 = false;
                  return var10000;
               }
            }

            public void m_5852_(ItemStack pStack) {
               super.m_5852_(pStack);
               HumanoidNPCMenu.this.entity.m_8061_(EquipmentSlot.FEET, pStack);
            }
         });
      }

      if (this.entity.canEquipSlots(EquipmentSlot.MAINHAND)) {
         this.m_38897_(new Slot(container, 4, 87, 62) {
            public boolean m_5857_(ItemStack pStack) {
               return HumanoidNPCMenu.this.entity.canEquipSlots(EquipmentSlot.MAINHAND, pStack);
            }

            public void m_5852_(ItemStack pStack) {
               super.m_5852_(pStack);
               HumanoidNPCMenu.this.entity.m_8061_(EquipmentSlot.MAINHAND, pStack);
            }
         });
      }

      if (this.entity.canEquipSlots(EquipmentSlot.OFFHAND)) {
         this.m_38897_(new Slot(container, 5, 105, 62) {
            public boolean m_5857_(ItemStack pStack) {
               return HumanoidNPCMenu.this.entity.canEquipSlots(EquipmentSlot.OFFHAND, pStack);
            }

            public void m_5852_(ItemStack pStack) {
               super.m_5852_(pStack);
               HumanoidNPCMenu.this.entity.m_8061_(EquipmentSlot.OFFHAND, pStack);
            }
         });
      }

      for(int i = 0; i < 4; ++i) {
         for(int l = 0; l < 3; ++l) {
            this.m_38897_(new Slot(container, l + i * 3 + 6, 132 + l * 18, 8 + i * 18));
         }
      }

   }

   private void addPlayerInventory(Inventory playerInventory) {
      for(int i = 0; i < 3; ++i) {
         for(int l = 0; l < 9; ++l) {
            this.m_38897_(new Slot(playerInventory, l + i * 9 + 9, 16 + l * 18, 84 + i * 18));
         }
      }

   }

   private void addPlayerHotbar(Inventory playerInventory) {
      for(int i = 0; i < 9; ++i) {
         this.m_38897_(new Slot(playerInventory, i, 16 + i * 18, 142));
      }

   }

   public static int getEquipmentSlotId(EquipmentSlot slot) {
      if (slot.equals(EquipmentSlot.MAINHAND)) {
         return 4;
      } else {
         return slot.equals(EquipmentSlot.OFFHAND) ? 5 : 3 - slot.m_20749_();
      }
   }

   @Nullable
   public static EquipmentSlot getEquipmentSlotById(int id) {
      EquipmentSlot[] var1 = EquipmentSlot.values();
      int var2 = var1.length;

      for(int var3 = 0; var3 < var2; ++var3) {
         EquipmentSlot slot = var1[var3];
         if (getEquipmentSlotId(slot) == id) {
            return slot;
         }
      }

      return null;
   }

   public static boolean canPlaceInArmorSlot(int id, ItemStack stack, HumanoidNPCEntity npc) {
      EquipmentSlot slot = getEquipmentSlotById(id);
      return slot != null ? canPlaceInArmorSlot(slot, stack, npc) : true;
   }

   public static boolean canPlaceInArmorSlot(EquipmentSlot slot, ItemStack stack, HumanoidNPCEntity npc) {
      if (slot.m_20743_().equals(Type.ARMOR)) {
         Item var4 = stack.m_41720_();
         if (var4 instanceof ArmorItem) {
            ArmorItem armorItem = (ArmorItem)var4;
            return armorItem.m_40402_() != slot ? false : npc.canEquipSlots(slot, stack);
         } else {
            return false;
         }
      } else {
         return true;
      }
   }

   public static EquipmentSlot getEquipmentSlotForStack(ItemStack pItem) {
      EquipmentSlot slot = pItem.getEquipmentSlot();
      if (slot != null) {
         return slot;
      } else {
         Item item = pItem.m_41720_();
         if (!pItem.m_150930_(Items.f_42047_)) {
            if (item instanceof BlockItem) {
               BlockItem block = (BlockItem)item;
               if (block.m_40614_() instanceof AbstractSkullBlock) {
                  return EquipmentSlot.HEAD;
               }
            }

            if (item instanceof ArmorItem) {
               ArmorItem armorItem = (ArmorItem)item;
               return armorItem.m_40402_();
            } else if (pItem.m_150930_(Items.f_42741_)) {
               return EquipmentSlot.CHEST;
            } else if (pItem.canPerformAction(ToolActions.SHIELD_BLOCK)) {
               return EquipmentSlot.OFFHAND;
            } else {
               return EquipmentSlot.MAINHAND;
            }
         } else {
            return EquipmentSlot.HEAD;
         }
      }
   }
}
