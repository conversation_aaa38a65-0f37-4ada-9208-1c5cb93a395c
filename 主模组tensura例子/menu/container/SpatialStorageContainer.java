package com.github.manasmods.tensura.menu.container;

import java.util.Iterator;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.nbt.ListTag;
import net.minecraft.world.SimpleContainer;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;

public class SpatialStorageContainer extends SimpleContainer {
   private final int maxStackSize;

   public SpatialStorageContainer(int size, int maxStackSize) {
      super(size);
      this.maxStackSize = maxStackSize;
   }

   public int m_6893_() {
      return this.maxStackSize;
   }

   public boolean m_6542_(Player pPlayer) {
      return pPlayer.m_6084_();
   }

   public void m_7797_(ListTag pContainerNbt) {
      int k;
      for(k = 0; k < this.m_6643_(); ++k) {
         this.m_6836_(k, ItemStack.f_41583_);
      }

      for(k = 0; k < pContainerNbt.size(); ++k) {
         CompoundTag tag = pContainerNbt.m_128728_(k);
         int j = tag.m_128451_("Slot");
         if (j < this.m_6643_()) {
            ItemStack stack = ItemStack.m_41712_(tag);
            this.m_6836_(j, stack);
         }
      }

   }

   public ListTag m_7927_() {
      ListTag listtag = new ListTag();

      for(int i = 0; i < this.m_6643_(); ++i) {
         ItemStack itemstack = this.m_8020_(i);
         if (!itemstack.m_41619_()) {
            CompoundTag tag = new CompoundTag();
            tag.m_128405_("Slot", i);
            itemstack.m_41739_(tag);
            listtag.add(tag);
         }
      }

      return listtag;
   }

   public boolean m_19183_(ItemStack pStack) {
      boolean flag = false;
      Iterator var3 = this.f_19147_.iterator();

      while(var3.hasNext()) {
         ItemStack itemstack = (ItemStack)var3.next();
         if (itemstack.m_41619_()) {
            flag = true;
            break;
         }

         if (ItemStack.m_150942_(itemstack, pStack) && (itemstack.m_41741_() != 1 || itemstack.m_41613_() < 1) && itemstack.m_41613_() < this.m_6893_()) {
            flag = true;
            break;
         }
      }

      return flag;
   }

   public void m_19185_(ItemStack pStack, ItemStack pOther) {
      int i = pOther.m_41741_() == 1 ? 1 : this.m_6893_();
      int j = Math.min(pStack.m_41613_(), i - pOther.m_41613_());
      if (j > 0) {
         pOther.m_41769_(j);
         pStack.m_41774_(j);
         this.m_6596_();
      }
   }
}
