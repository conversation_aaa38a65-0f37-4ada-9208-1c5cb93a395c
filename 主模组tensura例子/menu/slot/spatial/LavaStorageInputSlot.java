package com.github.manasmods.tensura.menu.slot.spatial;

import com.github.manasmods.tensura.capability.skill.TensuraSkillCapability;
import com.github.manasmods.tensura.menu.SpatialStorageMenu;
import java.util.Arrays;
import java.util.Optional;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.world.Container;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.inventory.Slot;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;
import net.minecraft.world.item.alchemy.PotionUtils;

public class LavaStorageInputSlot extends Slot {
   private final SpatialStorageMenu menu;

   public LavaStorageInputSlot(SpatialStorageMenu menu, Container container, int xPosition, int yPosition) {
      super(container, 1, xPosition, yPosition);
      this.menu = menu;
   }

   public int m_6641_() {
      return 1;
   }

   public boolean m_5857_(ItemStack pStack) {
      if (!PotionUtils.m_43571_(pStack).isEmpty()) {
         return false;
      } else {
         ItemStack output = LavaStorageInputSlot.LavaStorage.getOutputStack(pStack);
         if (output.m_41619_()) {
            return false;
         } else {
            ItemStack currentOutput = this.menu.lavaStorageOutput.m_8020_(0);
            if (currentOutput.m_41619_()) {
               return true;
            } else {
               return currentOutput.m_41613_() >= currentOutput.m_41741_() ? false : ItemStack.m_150942_(output, currentOutput);
            }
         }
      }
   }

   public void m_5852_(ItemStack pStack) {
      super.m_5852_(pStack);
      Player player = this.menu.getPlayer();
      LavaStorageInputSlot.LavaStorage[] var3 = LavaStorageInputSlot.LavaStorage.values();
      int var4 = var3.length;

      for(int var5 = 0; var5 < var4; ++var5) {
         LavaStorageInputSlot.LavaStorage lava = var3[var5];
         if (lava.getInput().equals(pStack.m_41720_())) {
            TensuraSkillCapability.getFrom(player).ifPresent((cap) -> {
               double point = cap.getLavaPoint() + lava.getLavaPoint();
               if (!(point < 0.0D) && (!(cap.getLavaPoint() >= 10000.0D) || !(lava.getLavaPoint() > 0.0D))) {
                  cap.setLavaPoint(point);
                  TensuraSkillCapability.sync(player);
                  player.m_216990_(this.soundEvent(lava.getLavaPoint()));
                  ItemStack currentOutput = this.menu.lavaStorageOutput.m_8020_(0);
                  if (currentOutput.m_41619_()) {
                     this.menu.lavaStorageOutput.m_6836_(0, lava.getOutput().m_7968_());
                  } else {
                     ItemStack newOutput = currentOutput.m_41777_();
                     newOutput.m_41769_(1);
                     this.menu.lavaStorageOutput.m_6836_(0, newOutput);
                  }

                  this.menu.lavaStorageOutput.m_6596_();
                  this.f_40218_.m_6836_(1, ItemStack.f_41583_);
                  this.f_40218_.m_6596_();
               }
            });
         }
      }

   }

   private SoundEvent soundEvent(double point) {
      return point > 0.0D ? SoundEvents.f_11780_ : SoundEvents.f_11783_;
   }

   public static enum LavaStorage {
      MAGMA_BLOCK(Items.f_42258_, 1.0D, Items.f_42594_),
      LAVE_BUCKET(Items.f_42448_, 3.0D, Items.f_42446_),
      BUCKET(Items.f_42446_, -3.0D, Items.f_42448_);

      private final Item input;
      private final double lavaPoint;
      private final Item output;

      public static ItemStack getOutputStack(ItemStack input) {
         Optional<Item> output = Arrays.stream(values()).filter((lavaStorage) -> {
            return lavaStorage.getInput().equals(input.m_41720_());
         }).map(LavaStorageInputSlot.LavaStorage::getOutput).findFirst();
         return (ItemStack)output.map(Item::m_7968_).orElse(ItemStack.f_41583_);
      }

      public Item getInput() {
         return this.input;
      }

      public double getLavaPoint() {
         return this.lavaPoint;
      }

      public Item getOutput() {
         return this.output;
      }

      private LavaStorage(Item input, double lavaPoint, Item output) {
         this.input = input;
         this.lavaPoint = lavaPoint;
         this.output = output;
      }

      // $FF: synthetic method
      private static LavaStorageInputSlot.LavaStorage[] $values() {
         return new LavaStorageInputSlot.LavaStorage[]{MAGMA_BLOCK, LAVE_BUCKET, BUCKET};
      }
   }
}
