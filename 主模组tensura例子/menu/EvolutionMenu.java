package com.github.manasmods.tensura.menu;

import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.network.TensuraNetwork;
import com.github.manasmods.tensura.network.play2server.GUISwitchPacket;
import com.github.manasmods.tensura.race.Race;
import com.github.manasmods.tensura.race.RaceHelper;
import com.github.manasmods.tensura.registry.menu.TensuraMenuTypes;
import java.util.List;
import java.util.Objects;
import net.minecraft.network.FriendlyByteBuf;
import net.minecraft.world.entity.player.Inventory;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.inventory.AbstractContainerMenu;
import net.minecraft.world.inventory.MenuType;
import net.minecraft.world.item.ItemStack;

public class EvolutionMenu extends AbstractContainerMenu {
   public EvolutionMenu(int pContainerId, Inventory inventory, FriendlyByteBuf buf) {
      this(pContainerId, inventory, inventory.f_35978_);
   }

   public EvolutionMenu(int pContainerId, Inventory inventory, Player player) {
      super((MenuType)TensuraMenuTypes.EVOLUTION_MENU.get(), pContainerId);
   }

   public boolean m_6366_(Player pPlayer, int pId) {
      if (pId == -1) {
         TensuraNetwork.INSTANCE.sendToServer(new GUISwitchPacket(0));
         return true;
      } else {
         List<Race> evolutions = ((Race)Objects.requireNonNull(TensuraPlayerCapability.getRace(pPlayer))).getNextEvolutions(pPlayer);
         if (pId < evolutions.size()) {
            Race evolution = (Race)evolutions.get(pId);
            RaceHelper.evolveRace(pPlayer, evolution, true);
            TensuraPlayerCapability.setTrackedRace(pPlayer, (Race)null);
            TensuraPlayerCapability.sync(pPlayer);
            return true;
         } else {
            return super.m_6366_(pPlayer, pId);
         }
      }
   }

   public boolean m_6875_(Player pPlayer) {
      return true;
   }

   public ItemStack m_7648_(Player pPlayer, int pIndex) {
      return ItemStack.f_41583_;
   }

   public boolean check() {
      return true;
   }
}
