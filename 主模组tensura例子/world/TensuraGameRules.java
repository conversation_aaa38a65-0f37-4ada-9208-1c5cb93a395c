package com.github.manasmods.tensura.world;

import com.github.manasmods.tensura.entity.human.CloneEntity;
import com.github.manasmods.tensura.entity.template.TensuraHorseEntity;
import com.github.manasmods.tensura.registry.dimensions.TensuraDimensions;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.TamableAnimal;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.GameRules;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.GameRules.BooleanValue;
import net.minecraft.world.level.GameRules.Category;
import net.minecraft.world.level.GameRules.IntegerValue;
import net.minecraft.world.level.GameRules.Key;
import org.jetbrains.annotations.Nullable;

public class TensuraGameRules {
   public static Key<IntegerValue> MAX_AP;
   public static Key<IntegerValue> MAX_MP;
   public static Key<IntegerValue> MIN_EP;
   public static Key<IntegerValue> DEMON_LORD_SEED;
   public static Key<IntegerValue> DEMON_LORD_AWAKEN;
   public static Key<BooleanValue> LABYRINTH_PVP;
   public static Key<BooleanValue> LABYRINTH_DEATH;
   public static Key<BooleanValue> COLOSSUS_RESPAWN;
   public static Key<IntegerValue> EP_DEATH_PENALTY;
   public static Key<IntegerValue> MP_SKILL_COST;
   public static Key<BooleanValue> NO_UNIQUE_START;
   public static Key<BooleanValue> TRULY_UNIQUE;
   public static Key<IntegerValue> RESET_INCOMPLETE_PENALTY;
   public static Key<IntegerValue> RESET_COUNTER_BONUS_UNIQUE;
   public static Key<BooleanValue> SKILL_BEFORE_RACE;
   public static Key<BooleanValue> HARDCORE_RACE;
   public static Key<BooleanValue> SKILL_GRIEFING;
   public static Key<BooleanValue> SKILL_STEAL;
   public static Key<BooleanValue> EP_STEAL;
   public static Key<BooleanValue> MIND_CONTROL;
   public static Key<BooleanValue> PLAYER_NAME;
   public static Key<BooleanValue> TENSURA_DISPLAY_NAME;
   public static Key<BooleanValue> RIMURU_MODE;
   public static Key<IntegerValue> MAX_MP_GAIN;
   public static Key<IntegerValue> MAX_AP_GAIN;
   public static Key<IntegerValue> EP_GAIN;
   public static Key<IntegerValue> PLAYER_EP;
   public static Key<IntegerValue> VANILLA_EP;
   public static Key<IntegerValue> SPAWNER_EP;
   public static Key<BooleanValue> EXPERIMENTAL_FEATURE;

   public static void registryGameRules() {
      MAX_AP = GameRules.m_46189_("maxAP", Category.PLAYER, IntegerValue.m_46312_(1000000000));
      MAX_MP = GameRules.m_46189_("maxMP", Category.PLAYER, IntegerValue.m_46312_(1000000000));
      MIN_EP = GameRules.m_46189_("minEP", Category.PLAYER, IntegerValue.m_46312_(100));
      LABYRINTH_PVP = GameRules.m_46189_("labyrinthPvp", Category.PLAYER, BooleanValue.m_46250_(true));
      LABYRINTH_DEATH = GameRules.m_46189_("labyrinthDeath", Category.PLAYER, BooleanValue.m_46250_(false));
      COLOSSUS_RESPAWN = GameRules.m_46189_("colossusRespawn", Category.SPAWNING, BooleanValue.m_46250_(false));
      EP_DEATH_PENALTY = GameRules.m_46189_("epDeathPenalty", Category.PLAYER, IntegerValue.m_46312_(5));
      MP_SKILL_COST = GameRules.m_46189_("mpSkillCost", Category.PLAYER, IntegerValue.m_46312_(100));
      PLAYER_NAME = GameRules.m_46189_("playerNaming", Category.PLAYER, BooleanValue.m_46250_(true));
      SKILL_GRIEFING = GameRules.m_46189_("skillGriefing", Category.PLAYER, BooleanValue.m_46250_(true));
      SKILL_STEAL = GameRules.m_46189_("skillSteal", Category.PLAYER, BooleanValue.m_46250_(true));
      EP_STEAL = GameRules.m_46189_("epSteal", Category.PLAYER, BooleanValue.m_46250_(false));
      MIND_CONTROL = GameRules.m_46189_("playerMindControl", Category.PLAYER, BooleanValue.m_46250_(true));
      MAX_MP_GAIN = GameRules.m_46189_("maxMpGain", Category.DROPS, IntegerValue.m_46312_(1000000000));
      MAX_AP_GAIN = GameRules.m_46189_("maxApGain", Category.DROPS, IntegerValue.m_46312_(1000000000));
      EP_GAIN = GameRules.m_46189_("epGain", Category.DROPS, IntegerValue.m_46312_(3));
      PLAYER_EP = GameRules.m_46189_("playerEP", Category.DROPS, IntegerValue.m_46312_(100));
      VANILLA_EP = GameRules.m_46189_("vanillaEP", Category.DROPS, IntegerValue.m_46312_(100));
      SPAWNER_EP = GameRules.m_46189_("spawnerEP", Category.DROPS, IntegerValue.m_46312_(10));
      DEMON_LORD_SEED = GameRules.m_46189_("demonLordSeed", Category.MISC, IntegerValue.m_46312_(200000));
      DEMON_LORD_AWAKEN = GameRules.m_46189_("demonLordAwaken", Category.MISC, IntegerValue.m_46312_(10000));
      RIMURU_MODE = GameRules.m_46189_("rimuruMode", Category.MISC, BooleanValue.m_46250_(false));
      NO_UNIQUE_START = GameRules.m_46189_("noUniqueStart", Category.MISC, BooleanValue.m_46250_(false));
      TRULY_UNIQUE = GameRules.m_46189_("trulyUnique", Category.MISC, BooleanValue.m_46250_(false));
      RESET_COUNTER_BONUS_UNIQUE = GameRules.m_46189_("resetCounterBonusUnique", Category.MISC, IntegerValue.m_46312_(0));
      RESET_INCOMPLETE_PENALTY = GameRules.m_46189_("resetIncompletePenalty", Category.MISC, IntegerValue.m_46312_(0));
      SKILL_BEFORE_RACE = GameRules.m_46189_("skillBeforeRace", Category.MISC, BooleanValue.m_46250_(false));
      HARDCORE_RACE = GameRules.m_46189_("hardcoreRace", Category.MISC, BooleanValue.m_46250_(false));
      TENSURA_DISPLAY_NAME = GameRules.m_46189_("tensuraDisplayName", Category.CHAT, BooleanValue.m_46250_(false));
      EXPERIMENTAL_FEATURE = GameRules.m_46189_("experimentalFeature", Category.MISC, BooleanValue.m_46250_(true));
   }

   public static int getMinEp(Level level) {
      return level.m_46469_().m_46215_(MIN_EP);
   }

   public static float getEPGain(Level level) {
      return (float)level.m_46469_().m_46215_(EP_GAIN) / 100.0F;
   }

   public static boolean canStealSkill(Level level) {
      return level.m_46469_().m_46207_(SKILL_STEAL);
   }

   public static boolean canEpSteal(Level level) {
      return level.m_46469_().m_46207_(EP_STEAL);
   }

   public static boolean noPlayerMindControl(Level level) {
      return !level.m_46469_().m_46207_(MIND_CONTROL);
   }

   public static boolean canSkillGrief(Level level) {
      return level.m_46472_().equals(TensuraDimensions.LABYRINTH) ? false : level.m_46469_().m_46207_(SKILL_GRIEFING);
   }

   public static boolean isLabyrinthPvpOff(Level level) {
      if (!level.m_46472_().equals(TensuraDimensions.LABYRINTH)) {
         return false;
      } else {
         return !level.m_46469_().m_46207_(LABYRINTH_PVP);
      }
   }

   public static boolean isLabyrinthPvpOff(Level level, Entity target, @Nullable Entity attacker) {
      if (!isLabyrinthPvpOff(level)) {
         return false;
      } else if (attacker == target) {
         return false;
      } else if (attacker instanceof Player) {
         Player player = (Player)attacker;
         if (player.m_7500_()) {
            return false;
         } else {
            return target instanceof Player ? true : target instanceof CloneEntity;
         }
      } else {
         boolean var10000;
         if (attacker instanceof TamableAnimal) {
            TamableAnimal animal = (TamableAnimal)attacker;
            if (animal.m_21824_()) {
               if (target instanceof Player) {
                  return true;
               }

               if (target instanceof TamableAnimal) {
                  TamableAnimal entity = (TamableAnimal)target;
                  if (entity.m_21824_()) {
                     var10000 = true;
                     return var10000;
                  }
               }

               var10000 = false;
               return var10000;
            }
         }

         if (attacker instanceof TensuraHorseEntity) {
            TensuraHorseEntity horse = (TensuraHorseEntity)attacker;
            if (horse.m_30614_()) {
               if (target instanceof Player) {
                  return true;
               }

               if (target instanceof TensuraHorseEntity) {
                  TensuraHorseEntity entity = (TensuraHorseEntity)target;
                  if (entity.m_30614_()) {
                     var10000 = true;
                     return var10000;
                  }
               }

               var10000 = false;
               return var10000;
            }
         }

         return false;
      }
   }
}
