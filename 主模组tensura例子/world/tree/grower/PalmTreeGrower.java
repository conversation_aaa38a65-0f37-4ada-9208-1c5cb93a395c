package com.github.manasmods.tensura.world.tree.grower;

import com.github.manasmods.tensura.registry.biome.TensuraConfiguredFeatures;
import net.minecraft.core.Holder;
import net.minecraft.util.RandomSource;
import net.minecraft.world.level.block.grower.AbstractTreeGrower;
import net.minecraft.world.level.levelgen.feature.ConfiguredFeature;
import org.jetbrains.annotations.Nullable;

public class PalmTreeGrower extends AbstractTreeGrower {
   @Nullable
   protected Holder<? extends ConfiguredFeature<?, ?>> m_213888_(RandomSource pRandom, boolean pLargeHive) {
      return (Holder)TensuraConfiguredFeatures.PALM_TREE.getHolder().get();
   }
}
