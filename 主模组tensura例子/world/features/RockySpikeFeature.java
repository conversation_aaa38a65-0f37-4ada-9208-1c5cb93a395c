package com.github.manasmods.tensura.world.features;

import com.google.common.collect.ImmutableList;
import net.minecraft.core.BlockPos;
import net.minecraft.util.Mth;
import net.minecraft.util.RandomSource;
import net.minecraft.world.level.WorldGenLevel;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.levelgen.feature.Feature;
import net.minecraft.world.level.levelgen.feature.FeaturePlaceContext;
import net.minecraft.world.level.levelgen.feature.configurations.NoneFeatureConfiguration;

public class RockySpikeFeature extends Feature<NoneFeatureConfiguration> {
   private final ImmutableList<Block> SET_1;
   private final ImmutableList<Block> SET_2;
   private final ImmutableList<Block> SET_3;

   public RockySpikeFeature() {
      super(NoneFeatureConfiguration.f_67815_);
      this.SET_1 = ImmutableList.of(Blocks.f_50069_, Blocks.f_50652_, Blocks.f_50334_);
      this.SET_2 = ImmutableList.of(Blocks.f_50652_, Blocks.f_50334_, Blocks.f_50069_);
      this.SET_3 = ImmutableList.of(Blocks.f_50334_, Blocks.f_50069_, Blocks.f_50652_);
   }

   public boolean m_142674_(FeaturePlaceContext<NoneFeatureConfiguration> context) {
      BlockPos pos = context.m_159777_();
      RandomSource random = context.m_225041_();
      ImmutableList var10000;
      switch(random.m_216332_(1, 3)) {
      case 1:
         var10000 = this.SET_1;
         break;
      case 2:
         var10000 = this.SET_2;
         break;
      case 3:
         var10000 = this.SET_3;
         break;
      default:
         var10000 = ImmutableList.of();
      }

      ImmutableList<Block> blocks = var10000;
      if (blocks.isEmpty()) {
         return false;
      } else {
         BlockState block1 = ((Block)blocks.get(0)).m_49966_();
         BlockState block2 = ((Block)blocks.get(1)).m_49966_();
         BlockState block3 = ((Block)blocks.get(2)).m_49966_();

         WorldGenLevel level;
         for(level = context.m_159774_(); level.m_46859_(pos) && pos.m_123342_() > level.m_141937_() + 2; pos = pos.m_7495_()) {
         }

         pos = pos.m_6630_(random.m_188503_(4));
         int i = random.m_188503_(4) + 7;
         int i1 = i / 4 + random.m_188503_(2);
         if (i1 > 1 && random.m_188503_(60) == 0) {
            pos = pos.m_6630_(10 + random.m_188503_(30));
         }

         int i2;
         int i3;
         for(i2 = 0; i2 < i; ++i2) {
            float v = (1.0F - (float)i2 / (float)i) * (float)i1;
            i3 = Mth.m_14167_(v);

            for(int i4 = -i3; i4 <= i3; ++i4) {
               float v1 = (float)Mth.m_14040_(i4) - 0.25F;

               for(int i5 = -i3; i5 <= i3; ++i5) {
                  float v2 = (float)Mth.m_14040_(i5) - 0.25F;
                  if ((i4 == 0 && i5 == 0 || !(v1 * v1 + v2 * v2 > v * v)) && (i4 != -i3 && i4 != i3 && i5 != -i3 && i5 != i3 || !(random.m_188501_() > 0.75F))) {
                     this.m_5974_(level, pos.m_7918_(i4, i2, i5), block1);
                     if (i2 != 0 && i3 > 1) {
                        this.m_5974_(level, pos.m_7918_(i4, -i2, i5), block2);
                     }
                  }
               }
            }
         }

         i2 = i1 - 1;
         if (i2 < 0) {
            i2 = 0;
         } else if (i2 > 1) {
            i2 = 1;
         }

         for(int i4 = -i2; i4 <= i2; ++i4) {
            for(i3 = -i2; i3 <= i2; ++i3) {
               BlockPos blockPos = pos.m_7918_(i4, -1, i3);
               int i5 = 50;
               if (Math.abs(i4) == 1 && Math.abs(i3) == 1) {
                  i5 = random.m_188503_(5);
               }

               while(blockPos.m_123342_() > 50) {
                  this.m_5974_(level, blockPos, block3);
                  blockPos = blockPos.m_7495_();
                  --i5;
                  if (i5 <= 0) {
                     blockPos = blockPos.m_6625_(random.m_188503_(5) + 1);
                     i5 = random.m_188503_(5);
                  }
               }
            }
         }

         return true;
      }
   }
}
