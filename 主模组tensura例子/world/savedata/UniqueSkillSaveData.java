package com.github.manasmods.tensura.world.savedata;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.nbt.ListTag;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.world.level.saveddata.SavedData;

public class UniqueSkillSaveData extends SavedData {
   private final Map<ResourceLocation, UUID> skillMap;

   public static UniqueSkillSaveData get(ServerLevel overworld) {
      return (UniqueSkillSaveData)overworld.m_8895_().m_164861_(UniqueSkillSaveData::new, UniqueSkillSaveData::new, "tensura_unique_skills");
   }

   private UniqueSkillSaveData() {
      this.skillMap = new HashMap();
   }

   private UniqueSkillSaveData(CompoundTag tag) {
      this();
      if (tag.m_128441_("entries")) {
         ListTag skillList = tag.m_128437_("entries", 10);
         skillList.forEach((e) -> {
            CompoundTag entry = (CompoundTag)e;
            ResourceLocation skillId = new ResourceLocation(entry.m_128461_("skill"));
            UUID ownerUUID = entry.m_128342_("owner");
            this.skillMap.put(skillId, ownerUUID);
         });
      }

   }

   public void addSkill(ResourceLocation skillId, UUID ownerUUID) {
      this.skillMap.put(skillId, ownerUUID);
      this.m_77762_();
   }

   public boolean hasSkill(ResourceLocation skillId) {
      return this.skillMap.containsKey(skillId);
   }

   public UUID getOwner(ResourceLocation skillId) {
      return (UUID)this.skillMap.get(skillId);
   }

   public void removeSkill(ResourceLocation skillId) {
      this.skillMap.remove(skillId);
      this.m_77762_();
   }

   public CompoundTag m_7176_(CompoundTag tag) {
      ListTag skillList = new ListTag();
      this.skillMap.forEach((skillId, ownerUUID) -> {
         CompoundTag entry = new CompoundTag();
         entry.m_128359_("skill", skillId.toString());
         entry.m_128362_("owner", ownerUUID);
         skillList.add(entry);
      });
      tag.m_128365_("entries", skillList);
      return tag;
   }

   public Map<ResourceLocation, UUID> getSkillMap() {
      return this.skillMap;
   }
}
