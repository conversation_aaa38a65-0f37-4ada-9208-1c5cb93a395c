package com.github.manasmods.tensura.core;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.TensuraSkill;
import com.github.manasmods.tensura.ability.skill.extra.ShadowMotionSkill;
import com.github.manasmods.tensura.capability.effects.TensuraEffectsCapability;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.effect.template.MobEffectHelper;
import com.github.manasmods.tensura.effect.template.SkillMobEffect;
import com.github.manasmods.tensura.race.Race;
import com.github.manasmods.tensura.race.merfolk.MerfolkRace;
import com.github.manasmods.tensura.registry.blocks.TensuraBlocks;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.enchantment.TensuraEnchantments;
import com.github.manasmods.tensura.registry.items.TensuraArmorItems;
import com.github.manasmods.tensura.registry.skill.UniqueSkills;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import com.google.common.collect.ImmutableMultimap;
import com.google.common.collect.Multimap;
import com.llamalad7.mixinextras.sugar.Local;
import java.util.Iterator;
import net.minecraft.core.BlockPos;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Entity.RemovalReason;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.AttributeModifier;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.food.FoodProperties;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.enchantment.Enchantment;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.Block;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.event.entity.living.MobEffectEvent.Remove;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.ModifyArg;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfoReturnable;

@Mixin({LivingEntity.class})
public abstract class MixinLivingEntity {
   @Inject(
      method = {"canBeAffected"},
      at = {@At("RETURN")},
      cancellable = true
   )
   public void canBeAffected(MobEffectInstance pEffectInstance, CallbackInfoReturnable<Boolean> cir) {
      LivingEntity entity = (LivingEntity)this;
      MobEffect mobeffect = pEffectInstance.m_19544_();
      Iterator var5 = SkillAPI.getSkillsFrom(entity).getLearnedSkills().iterator();

      while(var5.hasNext()) {
         ManasSkillInstance instance = (ManasSkillInstance)var5.next();
         if (instance.getMastery() >= 0) {
            ManasSkill var8 = instance.getSkill();
            if (var8 instanceof TensuraSkill) {
               TensuraSkill skill = (TensuraSkill)var8;
               if (skill.getImmuneEffects(instance, entity).contains(mobeffect)) {
                  cir.setReturnValue(false);
                  return;
               }
            }
         }
      }

      if (mobeffect.equals(MobEffects.f_19610_)) {
         if (entity.m_6844_(EquipmentSlot.HEAD).m_150930_((Item)TensuraArmorItems.ANTI_MAGIC_MASK.get()) || entity.m_6844_(EquipmentSlot.HEAD).m_150930_((Item)TensuraArmorItems.CRAZY_PIERROT_MASK.get())) {
            cir.setReturnValue(false);
         }
      } else if (mobeffect.equals(MobEffects.f_216964_)) {
         if (entity.m_6844_(EquipmentSlot.HEAD).m_150930_((Item)TensuraArmorItems.ANTI_MAGIC_MASK.get()) || entity.m_6844_(EquipmentSlot.HEAD).m_150930_((Item)TensuraArmorItems.WONDER_PIERROT_MASK.get())) {
            cir.setReturnValue(false);
         }
      } else if (mobeffect.equals(MobEffects.f_19604_) && (entity.m_6844_(EquipmentSlot.HEAD).m_150930_((Item)TensuraArmorItems.ANTI_MAGIC_MASK.get()) || entity.m_6844_(EquipmentSlot.HEAD).m_150930_((Item)TensuraArmorItems.ANGRY_PIERROT_MASK.get()))) {
         cir.setReturnValue(false);
      }

   }

   @Inject(
      method = {"addEatEffect"},
      at = {@At("RETURN")}
   )
   public void addEatEffect(ItemStack pFood, Level pLevel, LivingEntity pLivingEntity, CallbackInfo ci) {
      LivingEntity entity = (LivingEntity)this;
      FoodProperties food = pFood.getFoodProperties(entity);
      if (food != null) {
         if (pFood.m_204117_(TensuraTags.Items.MONSTER_CONSUMABLES)) {
            if (entity instanceof Player) {
               Player player = (Player)entity;
               double magicule = (double)food.m_38744_() * 100.0D;
               if (pFood.m_204117_(TensuraTags.Items.COOKED_MONSTER_CONSUMABLES)) {
                  magicule /= 2.0D;
               }

               SkillHelper.gainMP(player, magicule, false);
            }
         }
      }
   }

   @Inject(
      method = {"removeAllEffects"},
      at = {@At("HEAD")},
      cancellable = true
   )
   public void removeAllEffects(CallbackInfoReturnable<Boolean> cir) {
      LivingEntity entity = (LivingEntity)this;
      if (!entity.m_9236_().m_5776_()) {
         Iterator<MobEffectInstance> iterator = entity.m_21221_().values().iterator();

         boolean flag;
         for(flag = false; iterator.hasNext(); flag = true) {
            MobEffectInstance effect = (MobEffectInstance)iterator.next();
            if (!(effect.m_19544_() instanceof SkillMobEffect) && !MinecraftForge.EVENT_BUS.post(new Remove(entity, effect))) {
               entity.m_7285_(effect);
               iterator.remove();
            }
         }

         cir.setReturnValue(flag);
      }

   }

   @ModifyArg(
      method = {"updateFallFlying"},
      at = @At(
   value = "INVOKE",
   target = "net/minecraft/world/entity/LivingEntity.setSharedFlag(IZ)V"
)
   )
   private boolean updateFlying(boolean value) {
      LivingEntity entity = (LivingEntity)this;
      if (value) {
         return true;
      } else {
         if (entity instanceof Player) {
            Player player = (Player)entity;
            if (player.m_21255_()) {
               Race race = TensuraPlayerCapability.getRace(player);
               return race != null && race.canFly();
            }
         }

         return false;
      }
   }

   @Inject(
      method = {"canAttack(Lnet/minecraft/world/entity/LivingEntity;)Z"},
      at = {@At("RETURN")},
      cancellable = true
   )
   public void canAttack(LivingEntity pTarget, CallbackInfoReturnable<Boolean> cir) {
      if ((Boolean)cir.getReturnValue()) {
         LivingEntity entity = (LivingEntity)this;
         LivingEntity owner = SkillHelper.getSubordinateOwner(entity);
         if (owner != null) {
            if (owner == pTarget) {
               cir.setReturnValue(Boolean.FALSE);
            } else {
               LivingEntity targetOwner = SkillHelper.getSubordinateOwner(pTarget);
               if (owner == targetOwner) {
                  cir.setReturnValue(false);
               }

            }
         }
      }
   }

   @Inject(
      method = {"hurt"},
      at = {@At(
   value = "INVOKE",
   target = "Lnet/minecraft/world/entity/LivingEntity;knockback(DDD)V"
)}
   )
   public void setKnockBack(DamageSource pSource, float pAmount, CallbackInfoReturnable<Boolean> cir) {
      LivingEntity entity = (LivingEntity)this;
      TensuraEffectsCapability.getFrom(entity).ifPresent((cap) -> {
         if (DamageSourceHelper.noKnockDamage(pSource)) {
            cap.setNoKnockBack(true);
            TensuraEffectsCapability.sync(entity);
         } else if (cap.isNoKnockBack()) {
            cap.setNoKnockBack(false);
            TensuraEffectsCapability.sync(entity);
         }

      });
   }

   @Inject(
      method = {"tickDeath"},
      at = {@At("HEAD")},
      cancellable = true
   )
   public void setNoDyingAnimation(CallbackInfo ci) {
      LivingEntity entity = (LivingEntity)this;
      if (TensuraEffectsCapability.noDyingAnimation(entity)) {
         entity.f_20919_ = 0;
         if (!entity.f_19853_.m_5776_()) {
            entity.f_19853_.m_7605_(entity, (byte)60);
            entity.m_142687_(RemovalReason.KILLED);
            ci.cancel();
         }
      }

   }

   @Inject(
      method = {"playHurtSound"},
      at = {@At("HEAD")},
      cancellable = true
   )
   protected void getHurtSound(DamageSource pSource, CallbackInfo ci) {
      LivingEntity living = (LivingEntity)this;
      if (SkillUtils.hasPainNull(living)) {
         ci.cancel();
      } else {
         if (SkillUtils.isSkillToggled(living, (ManasSkill)UniqueSkills.MURDERER.get())) {
            if (living instanceof Player) {
               Player player = (Player)living;
               player.m_6330_(SoundEvents.f_11915_, SoundSource.PLAYERS, 1.0F, 1.0F);
            }

            ci.cancel();
         }

      }
   }

   @Inject(
      method = {"getArmorCoverPercentage"},
      at = {@At("RETURN")},
      cancellable = true
   )
   public void getArmorCoverPercentage(CallbackInfoReturnable<Float> cir) {
      LivingEntity entity = (LivingEntity)this;
      if (MobEffectHelper.hasTrueInvisibility(entity)) {
         cir.setReturnValue(0.0F);
      }

   }

   @Inject(
      method = {"increaseAirSupply"},
      at = {@At("RETURN")},
      cancellable = true
   )
   public void increaseAirSupply(int pCurrentAir, CallbackInfoReturnable<Integer> cir) {
      LivingEntity entity = (LivingEntity)this;
      boolean shadowStepAir = entity.m_21023_((MobEffect)TensuraMobEffects.SHADOW_STEP.get()) && ShadowMotionSkill.shouldConsumeAir(entity);
      if (shadowStepAir) {
         cir.setReturnValue(pCurrentAir);
      } else {
         Race race = TensuraPlayerCapability.getRace(entity);
         if (MerfolkRace.shouldLoseMoistness(entity, race) && race instanceof MerfolkRace) {
            int air = pCurrentAir - 1;
            cir.setReturnValue(air);
            if (air <= -20) {
               cir.setReturnValue(0);
               entity.m_6469_(TensuraDamageSources.SUFFOCATE, 1.0F);
            }
         }

      }
   }

   @Inject(
      method = {"decreaseAirSupply"},
      at = {@At("HEAD")},
      cancellable = true
   )
   public void decreaseAirSupply(int pCurrentAir, CallbackInfoReturnable<Integer> cir) {
      LivingEntity entity = (LivingEntity)this;
      if (TensuraPlayerCapability.getRace(entity) instanceof MerfolkRace) {
         cir.setReturnValue(Math.min(pCurrentAir + 2, entity.m_6062_()));
      } else if (!ShadowMotionSkill.shouldConsumeAir(entity)) {
         cir.setReturnValue(pCurrentAir);
      }

   }

   @Inject(
      method = {"setPosToBed"},
      at = {@At("HEAD")}
   )
   public void checkBedBlock(BlockPos blockPos, CallbackInfo cir) {
      LivingEntity entity = (LivingEntity)this;
      if (entity.m_9236_().m_8055_(blockPos).m_60713_((Block)TensuraBlocks.THATCH_BED.get())) {
         entity.m_6034_((double)blockPos.m_123341_() + 0.5D, (double)blockPos.m_123342_() + 3.0D, (double)blockPos.m_123343_() + 0.5D);
      }

   }

   @ModifyArg(
      method = {"collectEquipmentChanges"},
      at = @At(
   value = "INVOKE",
   target = "Lnet/minecraft/world/entity/ai/attributes/AttributeMap;addTransientAttributeModifiers(Lcom/google/common/collect/Multimap;)V"
)
   )
   public Multimap<Attribute, AttributeModifier> applyEquipmentAttributes(Multimap<Attribute, AttributeModifier> pMap, @Local(ordinal = 1) ItemStack stack) {
      CompoundTag tag = stack.m_41783_();
      if (tag == null) {
         return pMap;
      } else if (!tag.m_128403_("OwnerUUID")) {
         return pMap;
      } else if (stack.getEnchantmentLevel((Enchantment)TensuraEnchantments.TSUKUMOGAMI.get()) < 1) {
         return pMap;
      } else {
         return (Multimap)(tag.m_128471_("Activated") ? pMap : ImmutableMultimap.of());
      }
   }
}
