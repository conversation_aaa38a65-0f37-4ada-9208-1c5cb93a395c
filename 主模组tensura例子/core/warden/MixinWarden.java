package com.github.manasmods.tensura.core.warden;

import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import java.util.Objects;
import net.minecraft.core.BlockPos;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.monster.warden.Warden;
import net.minecraft.world.level.gameevent.GameEvent;
import net.minecraft.world.level.gameevent.GameEventListener;
import net.minecraft.world.level.gameevent.GameEvent.Context;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfoReturnable;

@Mixin({Warden.class})
public abstract class MixinWarden {
   @Inject(
      method = {"canTargetEntity"},
      at = {@At("RETURN")},
      cancellable = true
   )
   public void canTarget(Entity entity, CallbackInfoReturnable<Boolean> cir) {
      if (entity instanceof LivingEntity) {
         LivingEntity living = (LivingEntity)entity;
         if (SkillUtils.noInteractiveMode(living) || living.m_21023_((MobEffect)TensuraMobEffects.PRESENCE_CONCEALMENT.get())) {
            cir.setReturnValue(false);
            return;
         }

         if (living.m_21023_((MobEffect)TensuraMobEffects.FALSIFIER.get())) {
            int duration = ((MobEffectInstance)Objects.requireNonNull(living.m_21124_((MobEffect)TensuraMobEffects.FALSIFIER.get()))).m_19557_();
            if (duration % 20 == 0) {
               cir.setReturnValue(false);
            }
         }
      }

   }

   @Inject(
      method = {"shouldListen"},
      at = {@At("HEAD")},
      cancellable = true
   )
   public void shouldTarget(ServerLevel pLevel, GameEventListener pListener, BlockPos pPos, GameEvent pGameEvent, Context pContext, CallbackInfoReturnable<Boolean> cir) {
      Entity entity = pContext.f_223711_();
      if (entity != null && SkillUtils.canBlockSoundDetect(entity)) {
         cir.setReturnValue(false);
      }

   }

   @Inject(
      method = {"isInvulnerableTo"},
      at = {@At("HEAD")},
      cancellable = true
   )
   public void isInvulnerableTo(DamageSource pSource, CallbackInfoReturnable<Boolean> cir) {
      if (pSource.m_19384_() && pSource.m_19385_().contains("black")) {
         cir.setReturnValue(false);
      }

   }
}
