package com.github.manasmods.tensura.core;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.api.race.AdvancedHitbox;
import com.github.manasmods.tensura.capability.effects.TensuraEffectsCapability;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.capability.skill.TensuraSkillCapability;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.effect.template.MobEffectHelper;
import com.github.manasmods.tensura.entity.magic.barrier.BarrierPart;
import com.github.manasmods.tensura.race.Race;
import com.github.manasmods.tensura.race.RaceHelper;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.enchantment.TensuraEnchantments;
import com.github.manasmods.tensura.registry.items.TensuraMaterialItems;
import com.github.manasmods.tensura.registry.skill.UniqueSkills;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.InteractionResult;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityDimensions;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Pose;
import net.minecraft.world.entity.item.ItemEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.enchantment.Enchantment;
import net.minecraft.world.phys.AABB;
import net.minecraft.world.phys.Vec3;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.Shadow;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfoReturnable;

@Mixin({Entity.class})
public abstract class MixinEntity {
   @Shadow
   public abstract InteractionResult m_6096_(Player var1, InteractionHand var2);

   @Inject(
      method = {"getBoundingBoxForPose"},
      at = {@At("RETURN")},
      cancellable = true
   )
   public void getBoundingBoxForPose(Pose pPose, CallbackInfoReturnable<AABB> cir) {
      Entity entity = (Entity)this;
      if (entity instanceof Player) {
         Player player = (Player)entity;
         EntityDimensions size = entity.m_6972_(pPose);
         boolean shouldChange = false;
         Race race = TensuraPlayerCapability.getRace(player);
         if (race != null) {
            if (RaceHelper.getRaceSize(race) != 1.0F) {
               size = size.m_20388_(RaceHelper.getRaceSize(race));
               shouldChange = true;
            }

            if (race instanceof AdvancedHitbox) {
               AdvancedHitbox hitbox = (AdvancedHitbox)race;
               size = size.m_20390_(hitbox.getHitboxWidthModifier(), hitbox.getHitboxHeightModifier());
               shouldChange = true;
            }
         }

         float height = TensuraEffectsCapability.getHeight(player);
         if (height != 1.0F && height != 0.0F) {
            size = size.m_20390_(1.0F, height);
            shouldChange = true;
         }

         float skillSize = RaceHelper.getSkillSizeMultiplier(player);
         if (skillSize != 1.0F) {
            size = size.m_20388_(skillSize);
            shouldChange = true;
         }

         if (!shouldChange) {
            return;
         }

         float f = size.f_20377_ / 2.0F;
         Vec3 vec3 = new Vec3(entity.m_20185_() - (double)f, entity.m_20186_(), entity.m_20189_() - (double)f);
         Vec3 vec31 = new Vec3(entity.m_20185_() + (double)f, entity.m_20186_() + (double)size.f_20378_, entity.m_20189_() + (double)f);
         cir.setReturnValue(new AABB(vec3, vec31));
      }

   }

   @Inject(
      method = {"setSharedFlagOnFire"},
      at = {@At("RETURN")},
      cancellable = true
   )
   public void setSharedFlagOnFire(boolean pIsOnFire, CallbackInfo ci) {
      if (!pIsOnFire) {
         Entity entity = (Entity)this;
         if (entity instanceof LivingEntity) {
            LivingEntity living = (LivingEntity)entity;
            if (TensuraEffectsCapability.hasSyncedEffect(living, (MobEffect)TensuraMobEffects.BLACK_BURN.get())) {
               ci.cancel();
               entity.m_20115_(0, true);
            }
         }

      }
   }

   @Inject(
      method = {"isOnFire"},
      at = {@At("RETURN")},
      cancellable = true
   )
   public void isOnFire(CallbackInfoReturnable<Boolean> cir) {
      Entity entity = (Entity)this;
      LivingEntity living;
      if (entity instanceof LivingEntity) {
         living = (LivingEntity)entity;
         if (SkillUtils.noInteractiveMode(living)) {
            cir.setReturnValue(false);
         }
      }

      if (!((Boolean)cir.getReturnValue()).equals(true)) {
         if (entity instanceof LivingEntity) {
            living = (LivingEntity)entity;
            if (TensuraEffectsCapability.hasSyncedEffect(living, (MobEffect)TensuraMobEffects.BLACK_BURN.get())) {
               cir.setReturnValue(true);
            }
         }

      }
   }

   @Inject(
      method = {"canCollideWith"},
      at = {@At("RETURN")},
      cancellable = true
   )
   public void canCollideWith(Entity pEntity, CallbackInfoReturnable<Boolean> cir) {
      if ((Boolean)cir.getReturnValue()) {
         if (pEntity instanceof BarrierPart) {
            BarrierPart part = (BarrierPart)pEntity;
            cir.setReturnValue(part.m_7337_((Entity)this));
         }

      }
   }

   @Inject(
      method = {"isInvulnerableTo"},
      at = {@At("HEAD")},
      cancellable = true
   )
   public void isInvulnerableTo(DamageSource pSource, CallbackInfoReturnable<Boolean> cir) {
      if (!pSource.m_19378_()) {
         Entity entity = (Entity)this;
         if (entity instanceof ItemEntity) {
            ItemEntity item = (ItemEntity)entity;
            boolean sturdy = item.m_32055_().getEnchantmentLevel((Enchantment)TensuraEnchantments.STURDY.get()) > 0 || item.m_32055_().m_150930_((Item)TensuraMaterialItems.SHADOW_STORAGE.get());
            if (sturdy && !pSource.m_19378_()) {
               cir.setReturnValue(true);
            }

            return;
         }

         if (!(entity instanceof LivingEntity)) {
            return;
         }

         LivingEntity living = (LivingEntity)entity;
         if (SkillUtils.noInteractiveMode(living, true)) {
            cir.setReturnValue(true);
         }
      }

   }

   @Inject(
      method = {"isInvisible"},
      at = {@At("RETURN")},
      cancellable = true
   )
   public void isInvisible(CallbackInfoReturnable<Boolean> cir) {
      Entity entity = (Entity)this;
      if (entity instanceof LivingEntity) {
         LivingEntity living = (LivingEntity)entity;
         if (MobEffectHelper.hasTrueInvisibility(living)) {
            cir.setReturnValue(true);
         } else if (living instanceof Player) {
            Player player = (Player)living;
            if (TensuraPlayerCapability.isSpiritualForm(player)) {
               cir.setReturnValue(true);
            }
         }

      }
   }

   @Inject(
      method = {"isInvisibleTo"},
      at = {@At("RETURN")},
      cancellable = true
   )
   public void shouldBeInvisible(Player pPlayer, CallbackInfoReturnable<Boolean> cir) {
      Entity entity = (Entity)this;
      boolean noInvisible = TensuraEffectsCapability.getPresenceSense(pPlayer) > 0 || TensuraSkillCapability.isSkillInSlot(pPlayer, (ManasSkill)UniqueSkills.FALSIFIER.get());
      if (noInvisible && !entity.m_6095_().m_204039_(TensuraTags.EntityTypes.CAN_STAY_INVISIBLE)) {
         cir.setReturnValue(false);
      } else if (entity instanceof LivingEntity) {
         LivingEntity living = (LivingEntity)entity;
         if (MobEffectHelper.hasTrueInvisibility(living)) {
            return;
         }

         if (living instanceof Player) {
            Player player = (Player)living;
            if (TensuraPlayerCapability.isSpiritualForm(player)) {
               cir.setReturnValue(false);
            }
         }
      }

   }

   @Inject(
      method = {"isSilent"},
      at = {@At("HEAD")},
      cancellable = true
   )
   public void shouldBeSilent(CallbackInfoReturnable<Boolean> cir) {
      Entity entity = (Entity)this;
      if (SkillUtils.isSkillToggled(entity, (ManasSkill)UniqueSkills.MURDERER.get())) {
         cir.setReturnValue(true);
      }

   }

   @Inject(
      method = {"isAlliedTo(Lnet/minecraft/world/entity/Entity;)Z"},
      at = {@At("HEAD")},
      cancellable = true
   )
   public void isAlliedTo(Entity pEntity, CallbackInfoReturnable<Boolean> cir) {
      Entity entity = (Entity)this;
      if (entity instanceof LivingEntity) {
         LivingEntity sub = (LivingEntity)entity;
         if (pEntity instanceof LivingEntity) {
            LivingEntity target = (LivingEntity)pEntity;
            if (!SkillHelper.isSubordinate(target, sub) && !SkillHelper.isSubordinate(sub, target)) {
               LivingEntity owner = SkillHelper.getSubordinateOwner(sub);
               if (owner != null) {
                  LivingEntity targetOwner = SkillHelper.getSubordinateOwner(target);
                  if (targetOwner != null) {
                     if (owner == targetOwner) {
                        cir.setReturnValue(true);
                     }

                  }
               }
            } else {
               cir.setReturnValue(true);
            }
         }
      }
   }
}
