package com.github.manasmods.tensura.core;

import com.github.manasmods.tensura.entity.template.ClimbingEntity;
import net.minecraft.world.entity.monster.Spider;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;

@Mixin({Spider.class})
public abstract class MixinSpider {
   @Inject(
      method = {"tick"},
      at = {@At("RETURN")}
   )
   public void tick(CallbackInfo ci) {
      Spider entity = (Spider)this;
      if (!entity.f_19853_.f_46443_) {
         entity.m_33819_(ClimbingEntity.collidingWall(entity));
      }

   }
}
