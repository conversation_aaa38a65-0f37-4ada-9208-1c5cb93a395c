package com.github.manasmods.tensura.core;

import com.github.manasmods.tensura.registry.blocks.TensuraBlocks;
import net.minecraft.world.inventory.SmithingMenu;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.state.BlockState;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfoReturnable;

@Mixin({SmithingMenu.class})
public class MixinSmithingMenu {
   @Inject(
      method = {"isValidBlock"},
      at = {@At("RETURN")},
      cancellable = true
   )
   private void isValidBlock(BlockState pState, CallbackInfoReturnable<Boolean> cir) {
      if (!(Boolean)cir.getReturnValue()) {
         cir.setReturnValue(pState.m_60713_((Block)TensuraBlocks.SMITHING_BENCH.get()));
      }

   }
}
