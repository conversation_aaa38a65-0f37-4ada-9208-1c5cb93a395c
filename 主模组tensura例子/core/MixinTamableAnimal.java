package com.github.manasmods.tensura.core;

import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import java.util.UUID;
import net.minecraft.world.entity.TamableAnimal;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfoReturnable;

@Mixin({TamableAnimal.class})
public class MixinTamableAnimal {
   @Inject(
      method = {"getOwnerUUID"},
      at = {@At("RETURN")},
      cancellable = true
   )
   public void getOwnerUUID(CallbackInfoReturnable<UUID> cir) {
      TamableAnimal animal = (TamableAnimal)this;
      UUID temporary = TensuraEPCapability.getTemporaryOwner(animal);
      if (temporary != null) {
         cir.setReturnValue(temporary);
      } else {
         UUID permanent = TensuraEPCapability.getPermanentOwner(animal);
         if (permanent != null) {
            cir.setReturnValue(permanent);
         }

      }
   }

   @Inject(
      method = {"isTame"},
      at = {@At("RETURN")},
      cancellable = true
   )
   public void isTame(CallbackInfoReturnable<Boolean> cir) {
      if (!(Boolean)cir.getReturnValue()) {
         TamableAnimal animal = (TamableAnimal)this;
         if (TensuraEPCapability.getTemporaryOwner(animal) != null) {
            cir.setReturnValue(true);
         }

         if (TensuraEPCapability.getPermanentOwner(animal) != null) {
            cir.setReturnValue(true);
         }

      }
   }
}
