package com.github.manasmods.tensura.core.client;

import com.github.manasmods.tensura.capability.effects.TensuraEffectsCapability;
import com.github.manasmods.tensura.effect.template.MobEffectHelper;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.mojang.blaze3d.vertex.PoseStack;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.entity.EntityRenderDispatcher;
import net.minecraft.client.renderer.texture.TextureAtlas;
import net.minecraft.client.renderer.texture.TextureAtlasSprite;
import net.minecraft.client.resources.model.Material;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.Unique;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.ModifyVariable;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;

@Mixin({EntityRenderDispatcher.class})
public abstract class MixinEntityRenderDispatcher {
   @Unique
   private static final Material BLACK_FIRE_0;
   @Unique
   private static final Material BLACK_FIRE_1;

   @Inject(
      method = {"renderFlame"},
      at = {@At("HEAD")},
      cancellable = true
   )
   private void renderFlame(PoseStack pMatrixStack, MultiBufferSource pBuffer, Entity entity, CallbackInfo ci) {
      if (entity instanceof LivingEntity) {
         LivingEntity living = (LivingEntity)entity;
         if (MobEffectHelper.hasTrueInvisibility(living)) {
            ci.cancel();
         }
      }

   }

   @ModifyVariable(
      method = {"renderFlame"},
      at = @At(
   value = "INVOKE_ASSIGN",
   target = "Lnet/minecraft/client/resources/model/Material;sprite()Lnet/minecraft/client/renderer/texture/TextureAtlasSprite;",
   ordinal = 0
),
      ordinal = 0
   )
   public TextureAtlasSprite onRenderFlameAtSprite0(TextureAtlasSprite value, PoseStack poseStack, MultiBufferSource multiBufferSource, Entity entity) {
      if (entity instanceof LivingEntity) {
         LivingEntity living = (LivingEntity)entity;
         if (TensuraEffectsCapability.hasSyncedEffect(living, (MobEffect)TensuraMobEffects.BLACK_BURN.get())) {
            return BLACK_FIRE_0.m_119204_();
         }
      }

      return value;
   }

   @ModifyVariable(
      method = {"renderFlame"},
      at = @At(
   value = "INVOKE_ASSIGN",
   target = "Lnet/minecraft/client/resources/model/Material;sprite()Lnet/minecraft/client/renderer/texture/TextureAtlasSprite;",
   ordinal = 1
),
      ordinal = 1
   )
   public TextureAtlasSprite onRenderFlameAtSprite1(TextureAtlasSprite value, PoseStack poseStack, MultiBufferSource multiBufferSource, Entity entity) {
      if (entity instanceof LivingEntity) {
         LivingEntity living = (LivingEntity)entity;
         if (TensuraEffectsCapability.hasSyncedEffect(living, (MobEffect)TensuraMobEffects.BLACK_BURN.get())) {
            return BLACK_FIRE_1.m_119204_();
         }
      }

      return value;
   }

   static {
      BLACK_FIRE_0 = new Material(TextureAtlas.f_118259_, new ResourceLocation("tensura:block/black_fire_0"));
      BLACK_FIRE_1 = new Material(TextureAtlas.f_118259_, new ResourceLocation("tensura:block/black_fire_1"));
   }
}
