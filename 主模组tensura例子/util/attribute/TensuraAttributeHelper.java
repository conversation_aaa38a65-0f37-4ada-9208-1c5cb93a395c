package com.github.manasmods.tensura.util.attribute;

import com.github.manasmods.manascore.api.attribute.AttributeModifierHelper;
import java.util.List;
import java.util.UUID;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.AttributeModifier;
import net.minecraft.world.entity.ai.attributes.AttributeModifier.Operation;
import net.minecraft.world.item.enchantment.Enchantment;
import net.minecraftforge.event.entity.living.LivingEquipmentChangeEvent;

public class TensuraAttributeHelper {
   public static final UUID ENCHANTMENT_ATTACK_REACH_UUID = UUID.fromString("419930ee-7933-11ee-b962-0242ac120002");
   public static final UUID ENCHANTMENT_ATTACK_SPEED_UUID = UUID.fromString("*************-11ee-b962-0242ac120002");
   public static final List<UUID> ARMOR_MODIFIER_UUID_PER_SLOT = List.of(UUID.fromString("845DB27C-C624-495F-8C9F-6020A9A58B6B"), UUID.fromString("D8499B04-0E66-4726-AB29-64469D734E0D"), UUID.fromString("9F3D476D-C118-4544-8365-64846904B48E"), UUID.fromString("2AD3F246-FEE1-4E67-B886-69FD380BB150"));

   public static void addEnchantmentAttribute(LivingEquipmentChangeEvent event, Enchantment enchantment, Attribute attribute, UUID uuid, float amount) {
      LivingEntity entity = event.getEntity();
      int oldLevel = event.getTo().getEnchantmentLevel(enchantment);
      int newLevel = event.getFrom().getEnchantmentLevel(enchantment);
      if (newLevel != oldLevel) {
         if (oldLevel > 0) {
            AttributeModifierHelper.setModifier(entity, attribute, new AttributeModifier(uuid, "Enchantment Attributes", (double)(amount * (float)oldLevel), Operation.ADDITION));
         } else if (newLevel > 0) {
            AttributeModifierHelper.removeModifier(entity, attribute, uuid);
         }

      }
   }
}
