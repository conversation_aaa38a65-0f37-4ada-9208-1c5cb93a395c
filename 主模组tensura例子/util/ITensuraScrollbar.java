package com.github.manasmods.tensura.util;

import com.mojang.blaze3d.systems.RenderSystem;
import com.mojang.blaze3d.vertex.PoseStack;
import net.minecraft.client.gui.GuiComponent;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.util.Mth;

public interface ITensuraScrollbar {
   ResourceLocation SCROLL_BAR = new ResourceLocation("tensura", "textures/gui/scroll_bar.png");
   ITensuraScrollbar.ScrollbarData SCROLLBAR_DATA = new ITensuraScrollbar.ScrollbarData();
   int SB_WIDTH = 10;
   int SB_HEIGHT = 13;
   int SB_TEXTURE_HEIGHT = 26;

   int getStartIndex();

   int getOffscreenRows();

   void setStartIndex(int var1);

   default void initScrollbar(int posX, int posY, int totalSpace, float offset, boolean isScrolling, boolean isActive) {
      SCROLLBAR_DATA.scrollbarX = posX;
      SCROLLBAR_DATA.scrollbarY = posY;
      SCROLLBAR_DATA.scrollbarSpace = totalSpace;
      SCROLLBAR_DATA.scrollbarOffset = offset;
      SCROLLBAR_DATA.isScrolling = isScrolling;
      SCROLLBAR_DATA.isActive = isActive;
   }

   default void renderScrollbar(PoseStack poseStack) {
      int k = (int)((float)(SCROLLBAR_DATA.scrollbarSpace - 13) * SCROLLBAR_DATA.scrollbarOffset);
      RenderSystem.m_157456_(0, SCROLL_BAR);
      GuiComponent.m_93133_(poseStack, SCROLLBAR_DATA.scrollbarX, SCROLLBAR_DATA.scrollbarY + k, 0.0F, SCROLLBAR_DATA.isActive ? 13.0F : 0.0F, 10, 13, 10, 26);
   }

   default void handleScrollbarMouseClicked(double mouseX, double mouseY) {
      int pX = SCROLLBAR_DATA.scrollbarX;
      int pY = SCROLLBAR_DATA.scrollbarY;
      SCROLLBAR_DATA.setScrolling(mouseX > (double)pX && mouseX < (double)(pX + 10) && mouseY > (double)pY && mouseY < (double)(pY + SCROLLBAR_DATA.scrollbarSpace));
   }

   default boolean handleScrollbarMouseDragged(double mouseY, int slots) {
      if (SCROLLBAR_DATA.isScrolling && SCROLLBAR_DATA.isActive) {
         int i = SCROLLBAR_DATA.scrollbarY;
         int j = i + SCROLLBAR_DATA.scrollbarSpace;
         float scrollOffs = ((float)mouseY - (float)SCROLLBAR_DATA.scrollbarY - 7.5F) / ((float)(j - i) - 15.0F);
         scrollOffs = Mth.m_14036_(scrollOffs, 0.0F, 1.0F);
         SCROLLBAR_DATA.setOffset(scrollOffs);
         this.setStartIndex((int)((double)(scrollOffs * (float)this.getOffscreenRows()) + 0.5D) * slots);
         return true;
      } else {
         return false;
      }
   }

   default boolean handleScrollbarMouseScrolled(double delta, int slots) {
      if (!SCROLLBAR_DATA.isActive) {
         return false;
      } else {
         float f = (float)delta / (float)this.getOffscreenRows();
         float scrollOffs = Mth.m_14036_(SCROLLBAR_DATA.scrollbarOffset - f, 0.0F, 1.0F);
         SCROLLBAR_DATA.setOffset(scrollOffs);
         this.setStartIndex((int)((double)(scrollOffs * (float)this.getOffscreenRows()) + 0.5D) * slots);
         return true;
      }
   }

   public static class ScrollbarData {
      private int scrollbarX;
      private int scrollbarY;
      private int scrollbarSpace;
      private float scrollbarOffset;
      private boolean isScrolling;
      private boolean isActive;

      private ScrollbarData() {
      }

      public int getX() {
         return this.scrollbarX;
      }

      public void setX(int x) {
         this.scrollbarX = x;
      }

      public int getY() {
         return this.scrollbarY;
      }

      public void setY(int y) {
         this.scrollbarY = y;
      }

      public int getSpace() {
         return this.scrollbarSpace;
      }

      public void setSpace(int space) {
         this.scrollbarSpace = space;
      }

      public float getOffset() {
         return this.scrollbarOffset;
      }

      public void setOffset(float offset) {
         this.scrollbarOffset = offset;
      }

      public boolean getScrolling() {
         return this.isScrolling;
      }

      public void setScrolling(boolean scrolling) {
         this.isScrolling = scrolling;
      }

      public boolean getActive() {
         return this.isActive;
      }

      public void setActive(boolean active) {
         this.isActive = active;
      }
   }
}
