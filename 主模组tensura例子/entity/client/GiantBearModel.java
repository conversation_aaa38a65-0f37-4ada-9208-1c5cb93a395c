package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.GiantBearEntity;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.processor.IBone;
import software.bernie.geckolib3.model.AnimatedGeoModel;
import software.bernie.geckolib3.model.provider.data.EntityModelData;

public class GiantBearModel extends AnimatedGeoModel<GiantBearEntity> {
   public ResourceLocation getModelResource(GiantBearEntity object) {
      return new ResourceLocation("tensura", "geo/giant_bear.geo.json");
   }

   public ResourceLocation getTextureResource(GiantBearEntity instance) {
      return instance.m_6162_() ? new ResourceLocation("tensura", "textures/entity/bear/giant_bear_baby.png") : new ResourceLocation("tensura", "textures/entity/bear/giant_bear.png");
   }

   public ResourceLocation getAnimationResource(GiantBearEntity bear) {
      return new ResourceLocation("tensura", "animations/giant_bear.animation.json");
   }

   public void setCustomAnimations(GiantBearEntity bear, int instanceId, AnimationEvent customPredicate) {
      super.setCustomAnimations(bear, instanceId, customPredicate);
      if (bear.getMiscAnimation() == 0 && !bear.isPassengerSprint() && !bear.m_21660_()) {
         EntityModelData extraData = (EntityModelData)customPredicate.getExtraDataOfType(EntityModelData.class).get(0);
         IBone head = this.getAnimationProcessor().getBone("Head");
         if (head != null) {
            head.setRotationX(extraData.headPitch * 0.017453292F);
            head.setRotationY(extraData.netHeadYaw * 0.017453292F);
         }

      }
   }
}
