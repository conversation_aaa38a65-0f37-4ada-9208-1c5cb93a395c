package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.FeatheredSerpentEntity;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.processor.IBone;
import software.bernie.geckolib3.model.AnimatedGeoModel;
import software.bernie.geckolib3.model.provider.data.EntityModelData;

public class FeatheredSerpentModel extends AnimatedGeoModel<FeatheredSerpentEntity> {
   public ResourceLocation getModelResource(FeatheredSerpentEntity object) {
      return new ResourceLocation("tensura", "geo/feathered_serpent.geo.json");
   }

   public ResourceLocation getTextureResource(FeatheredSerpentEntity instance) {
      return new ResourceLocation("tensura", "textures/entity/feathered_serpent/feathered_serpent.png");
   }

   public ResourceLocation getAnimationResource(FeatheredSerpentEntity entity) {
      return new ResourceLocation("tensura", "animations/feathered_serpent.animation.json");
   }

   public void setCustomAnimations(FeatheredSerpentEntity entity, int instanceId, AnimationEvent customPredicate) {
      super.setCustomAnimations(entity, instanceId, customPredicate);
      if (!entity.isInFlyingPose()) {
         EntityModelData extraData = (EntityModelData)customPredicate.getExtraDataOfType(EntityModelData.class).get(0);
         IBone head = this.getAnimationProcessor().getBone("RotatingHead");
         if (head != null) {
            head.setRotationX(extraData.headPitch * 0.017453292F);
         }

      }
   }
}
