package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.GiantAntEntity;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.blaze3d.vertex.VertexConsumer;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.RenderType;
import net.minecraft.client.renderer.entity.EntityRendererProvider.Context;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.renderers.geo.GeoEntityRenderer;

public class GiantAntRenderer extends GeoEntityRenderer<GiantAntEntity> {
   public GiantAntRenderer(Context renderManager) {
      super(renderManager, new GiantAntModel());
      this.f_114477_ = 1.0F;
   }

   public ResourceLocation getTextureLocation(GiantAntEntity instance) {
      return new ResourceLocation("tensura", "textures/entity/giant_ant/giant_ant.png");
   }

   public RenderType getRenderType(GiantAntEntity ant, float partialTicks, PoseStack stack, MultiBufferSource renderTypeBuffer, VertexConsumer vertexBuilder, int packedLightIn, ResourceLocation textureLocation) {
      if (ant.m_6162_()) {
         stack.m_85841_(0.5F, 0.5F, 0.5F);
      }

      return RenderType.m_110473_(this.getTextureLocation(ant));
   }
}
