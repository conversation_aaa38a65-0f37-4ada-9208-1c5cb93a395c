package com.github.manasmods.tensura.entity.client.player;

import com.github.manasmods.tensura.entity.client.layer.ChargedLayer;
import com.github.manasmods.tensura.entity.client.layer.WingsLayer;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.math.Vector3f;
import net.minecraft.client.model.PlayerModel;
import net.minecraft.client.model.HumanoidModel.ArmPose;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.entity.LivingEntityRenderer;
import net.minecraft.client.renderer.entity.EntityRendererProvider.Context;
import net.minecraft.client.renderer.entity.layers.ArrowLayer;
import net.minecraft.client.renderer.entity.layers.CustomHeadLayer;
import net.minecraft.client.renderer.entity.layers.ElytraLayer;
import net.minecraft.client.renderer.entity.layers.ItemInHandLayer;
import net.minecraft.client.renderer.entity.layers.SpinAttackEffectLayer;
import net.minecraft.network.chat.Component;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.util.Mth;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.entity.HumanoidArm;
import net.minecraft.world.entity.Pose;
import net.minecraft.world.entity.TamableAnimal;
import net.minecraft.world.item.CrossbowItem;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.client.extensions.common.IClientItemExtensions;

public class PlayerLikeRenderer<T extends TamableAnimal> extends LivingEntityRenderer<T, PlayerLikeModel<T>> {
   public PlayerLikeRenderer(Context pContext, PlayerLikeModel<T> pModel, float pShadowRadius) {
      super(pContext, pModel, pShadowRadius);
      this.m_115326_(new ItemInHandLayer(this, pContext.m_234598_()));
      this.m_115326_(new ArrowLayer(pContext, this));
      this.m_115326_(new CustomHeadLayer(this, pContext.m_174027_(), pContext.m_234598_()));
      this.m_115326_(new ElytraLayer(this, pContext.m_174027_()));
      this.m_115326_(new SpinAttackEffectLayer(this, pContext.m_174027_()));
      this.m_115326_(new ChargedLayer.Humanoid(this));
      this.m_115326_(new ChargedLayer.HumanoidArm(this));
      this.m_115326_(new WingsLayer(this));
   }

   public ResourceLocation getTextureLocation(T pEntity) {
      return new ResourceLocation("tensura", "textures/blank_texture.png");
   }

   protected void scale(T entity, PoseStack pMatrixStack, float pPartialTickTime) {
      float scale = 0.9375F;
      pMatrixStack.m_85841_(scale, scale, scale);
   }

   public void render(T pEntity, float pEntityYaw, float pPartialTicks, PoseStack pMatrixStack, MultiBufferSource pBuffer, int pPackedLight) {
      this.setModelProperties(pEntity);
      super.m_7392_(pEntity, pEntityYaw, pPartialTicks, pMatrixStack, pBuffer, pPackedLight);
   }

   protected void renderNameTag(T pEntity, Component pDisplayName, PoseStack pMatrixStack, MultiBufferSource pBuffer, int pPackedLight) {
      if (this.shouldShowName(pEntity)) {
         super.m_7649_(pEntity, pDisplayName, pMatrixStack, pBuffer, pPackedLight);
      }
   }

   protected boolean shouldShowName(T pEntity) {
      return super.m_6512_(pEntity) && (pEntity.m_6052_() || pEntity.m_8077_() && pEntity == this.f_114476_.f_114359_);
   }

   public Vec3 getRenderOffset(T pEntity, float pPartialTicks) {
      return pEntity.m_6047_() ? new Vec3(0.0D, -0.125D, 0.0D) : super.m_7860_(pEntity, pPartialTicks);
   }

   private void setModelProperties(T entity) {
      PlayerModel<T> model = (PlayerModel)this.m_7200_();
      if (entity.m_5833_()) {
         model.m_8009_(false);
         model.f_102808_.f_104207_ = true;
         model.f_102809_.f_104207_ = true;
      } else {
         model.m_8009_(true);
         model.f_102817_ = entity.m_6047_();
         ArmPose armPose = this.getArmPose(entity, InteractionHand.MAIN_HAND);
         ArmPose armPoseOffhand = this.getArmPose(entity, InteractionHand.OFF_HAND);
         if (armPose.m_102897_()) {
            armPoseOffhand = entity.m_21206_().m_41619_() ? ArmPose.EMPTY : ArmPose.ITEM;
         }

         if (entity.m_5737_() == HumanoidArm.RIGHT) {
            model.f_102816_ = armPose;
            model.f_102815_ = armPoseOffhand;
         } else {
            model.f_102816_ = armPoseOffhand;
            model.f_102815_ = armPose;
         }
      }

   }

   protected ArmPose getArmPose(T entity, InteractionHand pHand) {
      ItemStack itemstack = entity.m_21120_(pHand);
      if (itemstack.m_41619_()) {
         return ArmPose.EMPTY;
      } else if (!entity.f_20911_ && itemstack.m_41720_() instanceof CrossbowItem && CrossbowItem.m_40932_(itemstack)) {
         return ArmPose.CROSSBOW_HOLD;
      } else {
         ArmPose armPose;
         if (entity.m_7655_() == pHand && entity.m_21212_() > 0) {
            ArmPose var10000;
            switch(itemstack.m_41780_()) {
            case BLOCK:
               var10000 = ArmPose.BLOCK;
               break;
            case BOW:
               var10000 = ArmPose.BOW_AND_ARROW;
               break;
            case SPEAR:
               var10000 = ArmPose.THROW_SPEAR;
               break;
            case CROSSBOW:
               var10000 = ArmPose.CROSSBOW_CHARGE;
               break;
            case SPYGLASS:
               var10000 = ArmPose.SPYGLASS;
               break;
            case TOOT_HORN:
               var10000 = ArmPose.TOOT_HORN;
               break;
            default:
               var10000 = null;
            }

            armPose = var10000;
            if (armPose != null) {
               return armPose;
            }
         }

         armPose = IClientItemExtensions.of(itemstack).getArmPose(entity, pHand, itemstack);
         return armPose != null ? armPose : ArmPose.ITEM;
      }
   }

   protected void setupRotations(T entity, PoseStack pMatrixStack, float pAgeInTicks, float pRotationYaw, float pPartialTicks) {
      float f = entity.m_20998_(pPartialTicks);
      float f3;
      float f2;
      if (entity.m_21255_()) {
         super.m_7523_(entity, pMatrixStack, pAgeInTicks, pRotationYaw, pPartialTicks);
         f3 = (float)entity.m_21256_() + pPartialTicks;
         f2 = Mth.m_14036_(f3 * f3 / 100.0F, 0.0F, 1.0F);
         if (!entity.m_21209_()) {
            pMatrixStack.m_85845_(Vector3f.f_122223_.m_122240_(f2 * (-90.0F - entity.m_146909_())));
         }

         Vec3 vec3 = entity.m_20252_(pPartialTicks);
         Vec3 vec31 = entity.m_20184_();
         double d0 = vec31.m_165925_();
         double d1 = vec3.m_165925_();
         if (d0 > 0.0D && d1 > 0.0D) {
            double d2 = (vec31.f_82479_ * vec3.f_82479_ + vec31.f_82481_ * vec3.f_82481_) / Math.sqrt(d0 * d1);
            double d3 = vec31.f_82479_ * vec3.f_82481_ - vec31.f_82481_ * vec3.f_82479_;
            pMatrixStack.m_85845_(Vector3f.f_122225_.m_122270_((float)(Math.signum(d3) * Math.acos(d2))));
         }
      } else if (f > 0.0F) {
         super.m_7523_(entity, pMatrixStack, pAgeInTicks, pRotationYaw, pPartialTicks);
         f3 = this.shouldSwim(entity) ? -90.0F - entity.m_146909_() : -90.0F;
         f2 = Mth.m_14179_(f, 0.0F, f3);
         pMatrixStack.m_85845_(Vector3f.f_122223_.m_122240_(f2));
         pMatrixStack.m_85837_(0.0D, -1.0D, 0.30000001192092896D);
      } else {
         if (entity.m_20089_() == Pose.STANDING && entity.m_21825_()) {
            pMatrixStack.m_85837_(0.0D, this.getSittingYOffset(entity), 0.0D);
         } else if (entity.m_20089_() == Pose.SLEEPING) {
            pMatrixStack.m_85837_(this.getSleepingXOffset(entity), 0.0D, 0.0D);
         }

         super.m_7523_(entity, pMatrixStack, pAgeInTicks, pRotationYaw, pPartialTicks);
      }

   }

   protected double getSittingYOffset(T pEntityLiving) {
      return pEntityLiving.m_6162_() ? -0.25D : -0.5D;
   }

   protected double getSleepingXOffset(T pEntityLiving) {
      return pEntityLiving.m_6162_() ? 0.5D : 1.0D;
   }

   protected boolean shouldSwim(T entity) {
      return false;
   }
}
