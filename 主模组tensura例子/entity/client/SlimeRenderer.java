package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.MetalSlimeEntity;
import com.github.manasmods.tensura.entity.SlimeEntity;
import com.github.manasmods.tensura.entity.variant.SlimeVariant;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.blaze3d.vertex.VertexConsumer;
import javax.annotation.Nullable;
import net.minecraft.client.model.HumanoidModel;
import net.minecraft.client.model.geom.ModelPart;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.RenderType;
import net.minecraft.client.renderer.block.model.ItemTransforms.TransformType;
import net.minecraft.client.renderer.entity.EntityRendererProvider.Context;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.animal.Sheep;
import net.minecraft.world.item.DyeColor;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.level.block.state.BlockState;
import software.bernie.geckolib3.core.processor.IBone;
import software.bernie.geckolib3.geo.render.built.GeoBone;
import software.bernie.geckolib3.geo.render.built.GeoModel;
import software.bernie.geckolib3.renderers.geo.ExtendedGeoEntityRenderer;

public class SlimeRenderer extends ExtendedGeoEntityRenderer<SlimeEntity> {
   protected ItemStack helmetItem;

   public SlimeRenderer(Context renderManager) {
      super(renderManager, new SlimeModel());
      this.f_114477_ = 0.4F;
   }

   public ResourceLocation getTextureLocation(SlimeEntity slime) {
      if (slime.getClass() == MetalSlimeEntity.class) {
         return shouldBeGolden(slime) ? new ResourceLocation("tensura", "textures/entity/slime/slime_metal_golden.png") : new ResourceLocation("tensura", "textures/entity/slime/slime_metal.png");
      } else {
         return slime.m_8077_() && "jeb_".equals(slime.m_7755_().getString()) ? new ResourceLocation("tensura", "textures/entity/slime/slime_no_color.png") : (ResourceLocation)SlimeVariant.LOCATION_BY_VARIANT.get(slime.getVariant());
      }
   }

   public static boolean shouldBeGolden(SlimeEntity slime) {
      if (!slime.m_8077_()) {
         return false;
      } else {
         return "Minh".equalsIgnoreCase(slime.m_7755_().getString()) ? true : "MinhEragon".equalsIgnoreCase(slime.m_7755_().getString());
      }
   }

   public RenderType getRenderType(SlimeEntity slime, float partialTicks, PoseStack stack, MultiBufferSource renderTypeBuffer, VertexConsumer vertexBuilder, int packedLightIn, ResourceLocation textureLocation) {
      return RenderType.m_110473_(this.getTextureLocation(slime));
   }

   public int getPackedOverlay(LivingEntity entity, float u) {
      return this.getOverlay((SlimeEntity)this.animatable, 0.0F);
   }

   public void render(GeoModel model, SlimeEntity slime, float partialTick, RenderType type, PoseStack poseStack, @Nullable MultiBufferSource bufferSource, @Nullable VertexConsumer buffer, int packedLight, int packedOverlay, float red, float green, float blue, float alpha) {
      float newRed = red;
      float newGreen = green;
      float newBlue = blue;
      if (slime.m_8077_() && "jeb_".equals(slime.m_7755_().getString())) {
         int i = slime.f_19797_ / 25 + slime.m_19879_();
         int j = DyeColor.values().length;
         float v = ((float)(slime.f_19797_ % 25) + partialTick) / 25.0F;
         float[] afloat1 = Sheep.m_29829_(DyeColor.m_41053_(i % j));
         float[] afloat2 = Sheep.m_29829_(DyeColor.m_41053_((i + 1) % j));
         float r = afloat1[0] * (1.0F - v) + afloat2[0] * v;
         float g = afloat1[1] * (1.0F - v) + afloat2[1] * v;
         float b = afloat1[2] * (1.0F - v) + afloat2[2] * v;
         newRed = Math.min(1.0F, r + 0.5F);
         newGreen = Math.min(1.0F, g + 0.5F);
         newBlue = Math.min(1.0F, b + 0.5F);
      }

      this.f_114477_ = 0.2F * (float)slime.getSize();
      poseStack.m_85841_(0.175F * (float)slime.getSize(), 0.175F * (float)slime.getSize(), 0.175F * (float)slime.getSize());
      super.render(model, slime, partialTick, type, poseStack, bufferSource, buffer, packedLight, packedOverlay, newRed, newGreen, newBlue, alpha);
   }

   public void renderEarly(SlimeEntity slime, PoseStack poseStack, float partialTick, MultiBufferSource bufferSource, VertexConsumer buffer, int packedLight, int packedOverlay, float red, float green, float blue, float partialTicks) {
      super.renderEarly(slime, poseStack, partialTick, bufferSource, buffer, packedLight, packedOverlay, red, green, blue, partialTicks);
      this.helmetItem = slime.m_6844_(EquipmentSlot.HEAD);
   }

   protected ItemStack getHeldItemForBone(String boneName, SlimeEntity currentEntity) {
      return null;
   }

   protected TransformType getCameraTransformForItemAtBone(ItemStack boneItem, String boneName) {
      return TransformType.NONE;
   }

   protected void preRenderItem(PoseStack stack, ItemStack item, String boneName, SlimeEntity currentEntity, IBone bone) {
   }

   protected void postRenderItem(PoseStack matrixStack, ItemStack item, String boneName, SlimeEntity currentEntity, IBone bone) {
   }

   protected ItemStack getArmorForBone(String boneName, SlimeEntity currentEntity) {
      return boneName.equals("HeadArmor") ? this.helmetItem : null;
   }

   protected EquipmentSlot getEquipmentSlotForArmorBone(String boneName, SlimeEntity currentEntity) {
      return boneName.equals("HeadArmor") ? EquipmentSlot.HEAD : null;
   }

   protected ModelPart getArmorPartForBone(String name, HumanoidModel<?> armorModel) {
      return name.equals("HeadArmor") ? armorModel.f_102808_ : null;
   }

   protected BlockState getHeldBlockForBone(String boneName, SlimeEntity currentEntity) {
      return null;
   }

   protected void preRenderBlock(PoseStack stack, BlockState block, String boneName, SlimeEntity currentEntity) {
   }

   protected void postRenderBlock(PoseStack stack, BlockState block, String boneName, SlimeEntity currentEntity) {
   }

   protected ResourceLocation getTextureForBone(String boneName, SlimeEntity animatable) {
      return null;
   }

   protected boolean isArmorBone(GeoBone bone) {
      return bone.getName().endsWith("Armor");
   }
}
