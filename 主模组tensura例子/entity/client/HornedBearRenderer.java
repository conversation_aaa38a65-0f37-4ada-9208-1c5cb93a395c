package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.HornedBearEntity;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.blaze3d.vertex.VertexConsumer;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.RenderType;
import net.minecraft.client.renderer.entity.EntityRendererProvider.Context;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.renderers.geo.GeoEntityRenderer;

public class HornedBearRenderer extends GeoEntityRenderer<HornedBearEntity> {
   public HornedBearRenderer(Context renderManager) {
      super(renderManager, new HornedBearModel());
      this.f_114477_ = 0.5F;
   }

   public ResourceLocation getTextureLocation(HornedBearEntity instance) {
      return new ResourceLocation("tensura", "textures/entity/bear/horned_bear.png");
   }

   public RenderType getRenderType(HornedBearEntity bear, float partialTicks, PoseStack stack, MultiBufferSource renderTypeBuffer, VertexConsumer vertexBuilder, int packedLightIn, ResourceLocation textureLocation) {
      if (bear.m_6162_()) {
         stack.m_85841_(0.5F, 0.5F, 0.5F);
      }

      return RenderType.m_110473_(this.getTextureLocation(bear));
   }
}
