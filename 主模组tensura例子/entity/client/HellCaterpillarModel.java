package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.HellCaterpillarEntity;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.processor.IBone;
import software.bernie.geckolib3.model.AnimatedGeoModel;
import software.bernie.geckolib3.model.provider.data.EntityModelData;

public class HellCaterpillarModel extends AnimatedGeoModel<HellCaterpillarEntity> {
   public ResourceLocation getModelResource(HellCaterpillarEntity object) {
      return new ResourceLocation("tensura", "geo/hell_caterpillar.geo.json");
   }

   public ResourceLocation getTextureResource(HellCaterpillarEntity instance) {
      return new ResourceLocation("tensura", "textures/entity/hell_moth_line/hell_caterpillar.png");
   }

   public ResourceLocation getAnimationResource(HellCaterpillarEntity animatable) {
      return new ResourceLocation("tensura", "animations/hell_caterpillar.animation.json");
   }

   public void setCustomAnimations(HellCaterpillarEntity caterpillar, int instanceId, AnimationEvent customPredicate) {
      super.setCustomAnimations(caterpillar, instanceId, customPredicate);
      EntityModelData extraData = (EntityModelData)customPredicate.getExtraDataOfType(EntityModelData.class).get(0);
      IBone head = this.getAnimationProcessor().getBone("Head");
      if (head != null) {
         head.setRotationX(extraData.headPitch * 0.017453292F);
         head.setRotationY(extraData.netHeadYaw * 0.017453292F);
      }

   }
}
