package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.config.client.TensuraClientConfig;
import com.github.manasmods.tensura.entity.KnightSpiderEntity;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.blaze3d.vertex.VertexConsumer;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.RenderType;
import net.minecraft.client.renderer.entity.EntityRendererProvider.Context;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.renderers.geo.GeoEntityRenderer;

public class KnightSpiderRenderer extends GeoEntityRenderer<KnightSpiderEntity> {
   public KnightSpiderRenderer(Context renderManager) {
      super(renderManager, new KnightSpiderModel());
      this.f_114477_ = 1.0F;
   }

   public ResourceLocation getTextureLocation(KnightSpiderEntity instance) {
      return getSpiderTexture(instance);
   }

   public static ResourceLocation getSpiderTexture(KnightSpiderEntity instance) {
      return (Boolean)TensuraClientConfig.INSTANCE.displayConfig.arachnophobia.get() ? new ResourceLocation("tensura", "textures/entity/knight_spider/knight_spider_safe.png") : new ResourceLocation("tensura", "textures/entity/knight_spider/knight_spider.png");
   }

   public RenderType getRenderType(KnightSpiderEntity spider, float partialTicks, PoseStack stack, MultiBufferSource renderTypeBuffer, VertexConsumer vertexBuilder, int packedLightIn, ResourceLocation textureLocation) {
      if (spider.m_6162_()) {
         stack.m_85841_(0.5F, 0.5F, 0.5F);
      }

      return RenderType.m_110473_(this.getTextureLocation(spider));
   }
}
