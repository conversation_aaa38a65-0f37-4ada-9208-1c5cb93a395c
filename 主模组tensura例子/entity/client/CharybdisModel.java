package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.CharybdisEntity;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.model.AnimatedGeoModel;

public class CharybdisModel extends AnimatedGeoModel<CharybdisEntity> {
   public ResourceLocation getModelResource(CharybdisEntity object) {
      return new ResourceLocation("tensura", "geo/charybdis.geo.json");
   }

   public ResourceLocation getTextureResource(CharybdisEntity instance) {
      return getLocation(instance);
   }

   public ResourceLocation getAnimationResource(CharybdisEntity entity) {
      return new ResourceLocation("tensura", "animations/charybdis.animation.json");
   }

   public static ResourceLocation getLocation(CharybdisEntity entity) {
      if (entity.m_8077_()) {
         if (entity.m_7755_().getString().compareToIgnoreCase("Orcinus Orta") == 0) {
            return new ResourceLocation("tensura", "textures/entity/charybdis/orcinus_orta.png");
         }

         if (entity.m_7755_().getString().compareToIgnoreCase("Orcinus") == 0) {
            return new ResourceLocation("tensura", "textures/entity/charybdis/orcinus_orta.png");
         }
      }

      return new ResourceLocation("tensura", "textures/entity/charybdis/charybdis.png");
   }
}
