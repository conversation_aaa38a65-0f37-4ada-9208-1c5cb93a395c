package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.DragonPeacockEntity;
import com.github.manasmods.tensura.entity.variant.PeacockVariant;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.blaze3d.vertex.VertexConsumer;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.RenderType;
import net.minecraft.client.renderer.entity.EntityRendererProvider.Context;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.renderers.geo.GeoEntityRenderer;

public class DragonPeacockRenderer extends GeoEntityRenderer<DragonPeacockEntity> {
   public DragonPeacockRenderer(Context renderManager) {
      super(renderManager, new DragonPeacockModel());
      this.f_114477_ = 0.3F;
   }

   public ResourceLocation getTextureLocation(DragonPeacockEntity instance) {
      return instance.m_6162_() ? new ResourceLocation("tensura", "textures/entity/dragon_peacock/dragon_peacock_baby.png") : (ResourceLocation)PeacockVariant.LOCATION_BY_VARIANT.get(instance.getVariant());
   }

   public RenderType getRenderType(DragonPeacockEntity animatable, float partialTicks, PoseStack stack, MultiBufferSource renderTypeBuffer, VertexConsumer vertexBuilder, int packedLightIn, ResourceLocation textureLocation) {
      if (animatable.m_6162_()) {
         stack.m_85841_(0.5F, 0.5F, 0.5F);
      }

      return super.getRenderType(animatable, partialTicks, stack, renderTypeBuffer, vertexBuilder, packedLightIn, textureLocation);
   }
}
