package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.ArmoursaurusEntity;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.processor.IBone;
import software.bernie.geckolib3.model.AnimatedGeoModel;
import software.bernie.geckolib3.model.provider.data.EntityModelData;

public class ArmoursaurusModel extends AnimatedGeoModel<ArmoursaurusEntity> {
   public ResourceLocation getModelResource(ArmoursaurusEntity object) {
      return new ResourceLocation("tensura", "geo/armoursaurus.geo.json");
   }

   public ResourceLocation getTextureResource(ArmoursaurusEntity instance) {
      return new ResourceLocation("tensura", "textures/entity/armoursaurus/armoursaurus.png");
   }

   public ResourceLocation getAnimationResource(ArmoursaurusEntity entity) {
      return new ResourceLocation("tensura", "animations/armoursaurus.animation.json");
   }

   public void setCustomAnimations(ArmoursaurusEntity entity, int instanceId, AnimationEvent customPredicate) {
      super.setCustomAnimations(entity, instanceId, customPredicate);
      IBone chests = this.getAnimationProcessor().getBone("Chests");
      if (entity.isChested() == chests.isHidden()) {
         chests.setHidden(!entity.isChested());
      }

      IBone saddle = this.getAnimationProcessor().getBone("Saddle");
      if (entity.isSaddled() == saddle.isHidden()) {
         saddle.setHidden(!entity.isSaddled());
      }

      IBone saddleSpikes = this.getAnimationProcessor().getBone("SaddleSpikes");
      if (entity.isSaddled() == !saddleSpikes.isHidden()) {
         saddleSpikes.setHidden(entity.isSaddled());
      }

      if (entity.getMiscAnimation() == 0) {
         EntityModelData extraData = (EntityModelData)customPredicate.getExtraDataOfType(EntityModelData.class).get(0);
         IBone head = this.getAnimationProcessor().getBone("Head");
         if (head != null) {
            float pitch = entity.m_21825_() ? extraData.headPitch - 35.0F : extraData.headPitch;
            head.setRotationX(pitch * 0.017453292F);
            head.setRotationY(extraData.netHeadYaw * 0.017453292F);
         }

      }
   }
}
