package com.github.manasmods.tensura.entity.client.multipart;

import com.github.manasmods.tensura.entity.multipart.EvilCentipedeBody;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.blaze3d.vertex.VertexConsumer;
import com.mojang.math.Vector3f;
import net.minecraft.ChatFormatting;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.RenderType;
import net.minecraft.client.renderer.entity.EntityRendererProvider.Context;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.util.Mth;
import net.minecraft.world.entity.Pose;
import software.bernie.geckolib3.renderers.geo.GeoEntityRenderer;

public class EvilCentipedeBodyRenderer extends GeoEntityRenderer<EvilCentipedeBody> {
   public EvilCentipedeBodyRenderer(Context renderManager) {
      super(renderManager, new EvilCentipedeBodyModel());
      this.f_114477_ = 0.5F;
   }

   public ResourceLocation getTextureLocation(EvilCentipedeBody instance) {
      return new ResourceLocation("tensura", "textures/entity/evil_centipede/evil_centipede.png");
   }

   protected float getDeathMaxRotation(EvilCentipedeBody animatable) {
      return 180.0F;
   }

   public RenderType getRenderType(EvilCentipedeBody entity, float partialTicks, PoseStack stack, MultiBufferSource renderTypeBuffer, VertexConsumer vertexBuilder, int packedLightIn, ResourceLocation textureLocation) {
      stack.m_85841_(1.5F, 1.5F, 1.5F);
      return RenderType.m_110473_(this.getTextureLocation(entity));
   }

   protected void applyRotations(EvilCentipedeBody entity, PoseStack stack, float ageInTicks, float rotationYaw, float partialTick) {
      float newYaw = entity.f_20885_;
      Pose pose = entity.m_20089_();
      if (pose != Pose.SLEEPING) {
         stack.m_85845_(Vector3f.f_122225_.m_122240_(180.0F - newYaw));
         stack.m_85845_(Vector3f.f_122223_.m_122240_(entity.m_146909_()));
      }

      if (entity.f_20919_ > 0) {
         float f = ((float)entity.f_20919_ + partialTick - 1.0F) / 20.0F * 1.6F;
         f = Mth.m_14116_(f);
         if (f > 1.0F) {
            f = 1.0F;
         }

         stack.m_85837_(0.0D, 0.1D, 0.0D);
         stack.m_85845_(Vector3f.f_122227_.m_122240_(f * this.getDeathMaxRotation(entity)));
      } else if (entity.m_8077_()) {
         String s = ChatFormatting.m_126649_(entity.m_7755_().getString());
         if ("Dinnerbone".equals(s)) {
            stack.m_85837_(0.0D, (double)(entity.m_20206_() + 0.1F), 0.0D);
            stack.m_85845_(Vector3f.f_122227_.m_122240_(180.0F));
         }
      }

   }
}
