package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.GoblinEntity;
import com.github.manasmods.tensura.entity.client.layer.GoblinLayer;
import com.github.manasmods.tensura.entity.client.player.PlayerLikeModel;
import com.github.manasmods.tensura.entity.client.player.PlayerLikeRenderer;
import com.github.manasmods.tensura.entity.variant.GoblinVariant;
import com.mojang.blaze3d.vertex.PoseStack;
import net.minecraft.client.model.HumanoidModel;
import net.minecraft.client.model.geom.ModelLayers;
import net.minecraft.client.renderer.entity.EntityRendererProvider.Context;
import net.minecraft.client.renderer.entity.layers.HumanoidArmorLayer;
import net.minecraft.resources.ResourceLocation;

public class GoblinRenderer extends PlayerLikeRenderer<GoblinEntity> {
   public GoblinRenderer(Context pContext) {
      super(pContext, new PlayerLikeModel(pContext.m_174023_(ModelLayers.f_171162_), false), 0.5F);
      this.m_115326_(new HumanoidArmorLayer(this, new HumanoidModel(pContext.m_174023_(ModelLayers.f_171164_)), new HumanoidModel(pContext.m_174023_(ModelLayers.f_171165_))));
      this.m_115326_(new GoblinLayer.Face(this));
      this.m_115326_(new GoblinLayer.Hair(this));
      this.m_115326_(new GoblinLayer.HairBody(this));
      this.m_115326_(new GoblinLayer.Clothing(this));
      this.m_115326_(new GoblinLayer.Bandages(this));
      this.m_115326_(new GoblinLayer.Head(this));
      this.m_115326_(new GoblinLayer.Top(this));
      this.m_115326_(new GoblinLayer.Bottom(this));
   }

   public ResourceLocation getTextureLocation(GoblinEntity pEntity) {
      return GoblinVariant.Skin.getTextureLocation(pEntity);
   }

   protected void scale(GoblinEntity pLivingEntity, PoseStack pMatrixStack, float pPartialTickTime) {
      float f = pLivingEntity.isHobgoblin() ? 0.9375F : 0.7F;
      pMatrixStack.m_85841_(f, f, f);
   }

   protected boolean shouldSwim(GoblinEntity entity) {
      return entity.shouldSwim();
   }
}
