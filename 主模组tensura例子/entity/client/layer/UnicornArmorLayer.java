package com.github.manasmods.tensura.entity.client.layer;

import com.github.manasmods.tensura.entity.UnicornEntity;
import com.github.manasmods.tensura.entity.client.UnicornModel;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.blaze3d.vertex.VertexConsumer;
import net.minecraft.client.model.geom.EntityModelSet;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.RenderType;
import net.minecraft.client.renderer.entity.RenderLayerParent;
import net.minecraft.client.renderer.entity.layers.RenderLayer;
import net.minecraft.client.renderer.texture.OverlayTexture;
import net.minecraft.world.item.DyeableHorseArmorItem;
import net.minecraft.world.item.HorseArmorItem;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.api.distmarker.OnlyIn;

@OnlyIn(Dist.CLIENT)
public class UnicornArmorLayer<T extends UnicornEntity> extends RenderLayer<T, UnicornModel<T>> {
   private final UnicornModel<T> model;

   public UnicornArmorLayer(RenderLayerParent<T, UnicornModel<T>> pRenderer, EntityModelSet modelSet) {
      super(pRenderer);
      this.model = new UnicornModel(modelSet.m_171103_(UnicornModel.UNICORN));
   }

   public void render(PoseStack pMatrixStack, MultiBufferSource pBuffer, int pPackedLight, T entity, float pLimbSwing, float pLimbSwingAmount, float pPartialTicks, float pAgeInTicks, float pNetHeadYaw, float pHeadPitch) {
      ItemStack itemstack = entity.getArmor();
      Item var13 = itemstack.m_41720_();
      if (var13 instanceof HorseArmorItem) {
         HorseArmorItem horsearmoritem = (HorseArmorItem)var13;
         ((UnicornModel)this.m_117386_()).m_102624_(this.model);
         this.model.m_6839_(entity, pLimbSwing, pLimbSwingAmount, pPartialTicks);
         this.model.m_6973_(entity, pLimbSwing, pLimbSwingAmount, pAgeInTicks, pNetHeadYaw, pHeadPitch);
         float green;
         float blue;
         float red;
         if (horsearmoritem instanceof DyeableHorseArmorItem) {
            int i = ((DyeableHorseArmorItem)horsearmoritem).m_41121_(itemstack);
            red = (float)(i >> 16 & 255) / 255.0F;
            green = (float)(i >> 8 & 255) / 255.0F;
            blue = (float)(i & 255) / 255.0F;
         } else {
            red = 1.0F;
            green = 1.0F;
            blue = 1.0F;
         }

         VertexConsumer vertexconsumer = pBuffer.m_6299_(RenderType.m_110458_(horsearmoritem.m_41367_()));
         this.model.m_7695_(pMatrixStack, vertexconsumer, pPackedLight, OverlayTexture.f_118083_, red, green, blue, 1.0F);
      }

   }
}
