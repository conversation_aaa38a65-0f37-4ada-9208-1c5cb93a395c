package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.ElementalColossusEntity;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.processor.IBone;
import software.bernie.geckolib3.model.AnimatedGeoModel;
import software.bernie.geckolib3.model.provider.data.EntityModelData;

public class ElementalColossusModel extends AnimatedGeoModel<ElementalColossusEntity> {
   public ResourceLocation getModelResource(ElementalColossusEntity object) {
      return new ResourceLocation("tensura", "geo/elemental_colossus.geo.json");
   }

   public ResourceLocation getTextureResource(ElementalColossusEntity instance) {
      return new ResourceLocation("tensura", "textures/entity/elemental_colossus/elemental_colossus.png");
   }

   public ResourceLocation getAnimationResource(ElementalColossusEntity entity) {
      return new ResourceLocation("tensura", "animations/elemental_colossus.animation.json");
   }

   public void setCustomAnimations(ElementalColossusEntity entity, int instanceId, AnimationEvent customPredicate) {
      super.setCustomAnimations(entity, instanceId, customPredicate);
      if (!entity.m_21825_() && !entity.m_5803_() && entity.getMiscAnimation() != 6 && entity.getMiscAnimation() != 5) {
         EntityModelData extraData = (EntityModelData)customPredicate.getExtraDataOfType(EntityModelData.class).get(0);
         IBone head = this.getAnimationProcessor().getBone("Head");
         if (head != null) {
            head.setRotationX(extraData.headPitch * 0.017453292F);
            head.setRotationY(extraData.netHeadYaw * 0.017453292F);
         }

      }
   }
}
