package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.HornedRabbitEntity;
import net.minecraft.client.renderer.entity.MobRenderer;
import net.minecraft.client.renderer.entity.EntityRendererProvider.Context;
import net.minecraft.resources.ResourceLocation;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.api.distmarker.OnlyIn;

@OnlyIn(Dist.CLIENT)
public class HornedRabbitRenderer<T extends HornedRabbitEntity> extends Mob<PERSON>enderer<T, HornedRabbitModel<T>> {
   public HornedRabbitRenderer(Context context) {
      super(context, new HornedRabbitModel(context.m_174023_(HornedRabbitModel.HORNED_RABBIT)), 0.3F);
   }

   public ResourceLocation getTextureLocation(T entity) {
      return new ResourceLocation("tensura", "textures/entity/horned_rabbit/horned_rabbit.png");
   }
}
