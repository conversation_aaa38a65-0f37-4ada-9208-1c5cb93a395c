package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.OneEyedOwlEntity;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.blaze3d.vertex.VertexConsumer;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.RenderType;
import net.minecraft.client.renderer.entity.EntityRendererProvider.Context;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.renderers.geo.GeoEntityRenderer;

public class OneEyedOwlRenderer extends GeoEntityRenderer<OneEyedOwlEntity> {
   public OneEyedOwlRenderer(Context renderManager) {
      super(renderManager, new OneEyedOwlModel());
      this.f_114477_ = 0.3F;
   }

   public ResourceLocation getTextureLocation(OneEyedOwlEntity instance) {
      return new ResourceLocation("tensura", "textures/entity/one_eyed_owl/one_eyed_owl.png");
   }

   public RenderType getRenderType(OneEyedOwlEntity animatable, float partialTicks, PoseStack stack, MultiBufferSource renderTypeBuffer, VertexConsumer vertexBuilder, int packedLightIn, ResourceLocation textureLocation) {
      stack.m_85841_(0.5F, 0.5F, 0.5F);
      return super.getRenderType(animatable, partialTicks, stack, renderTypeBuffer, vertexBuilder, packedLightIn, textureLocation);
   }
}
