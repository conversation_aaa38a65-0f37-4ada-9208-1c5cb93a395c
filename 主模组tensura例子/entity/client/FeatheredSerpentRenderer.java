package com.github.manasmods.tensura.entity.client;

import com.github.manasmods.tensura.entity.FeatheredSerpentEntity;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.blaze3d.vertex.VertexConsumer;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.RenderType;
import net.minecraft.client.renderer.entity.EntityRendererProvider.Context;
import net.minecraft.resources.ResourceLocation;
import software.bernie.geckolib3.renderers.geo.GeoEntityRenderer;

public class FeatheredSerpentRenderer extends GeoEntityRenderer<FeatheredSerpentEntity> {
   public FeatheredSerpentRenderer(Context renderManager) {
      super(renderManager, new FeatheredSerpentModel());
      this.f_114477_ = 1.0F;
   }

   public ResourceLocation getTextureLocation(FeatheredSerpentEntity instance) {
      return new ResourceLocation("tensura", "textures/entity/feathered_serpent/feathered_serpent.png");
   }

   public RenderType getRenderType(FeatheredSerpentEntity ant, float partialTicks, PoseStack stack, MultiBufferSource renderTypeBuffer, VertexConsumer vertexBuilder, int packedLightIn, ResourceLocation textureLocation) {
      return RenderType.m_110473_(this.getTextureLocation(ant));
   }
}
