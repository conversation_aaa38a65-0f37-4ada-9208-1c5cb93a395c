package com.github.manasmods.tensura.entity.template;

import com.github.manasmods.tensura.ability.magic.spiritual.SpiritualMagic;
import com.github.manasmods.tensura.api.entity.navigator.NoSpinFlightPathNavigator;
import com.github.manasmods.tensura.api.entity.subclass.IElementalSpirit;
import com.github.manasmods.tensura.api.entity.subclass.IFollower;
import com.github.manasmods.tensura.api.entity.subclass.IGiantMob;
import com.github.manasmods.tensura.config.TensuraConfig;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.entity.human.ShizuEntity;
import com.github.manasmods.tensura.entity.magic.barrier.BarrierPart;
import com.github.manasmods.tensura.item.food.HealingPotionItem;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.sound.TensuraSoundEvents;
import java.util.EnumSet;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import javax.annotation.Nullable;
import net.minecraft.core.BlockPos;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.chat.Component;
import net.minecraft.network.syncher.EntityDataAccessor;
import net.minecraft.network.syncher.EntityDataSerializers;
import net.minecraft.network.syncher.SynchedEntityData;
import net.minecraft.server.level.ServerBossEvent;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundSource;
import net.minecraft.util.Mth;
import net.minecraft.world.DifficultyInstance;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.InteractionResult;
import net.minecraft.world.SimpleContainer;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityDimensions;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.MobSpawnType;
import net.minecraft.world.entity.Pose;
import net.minecraft.world.entity.SpawnGroupData;
import net.minecraft.world.entity.TamableAnimal;
import net.minecraft.world.entity.EquipmentSlot.Type;
import net.minecraft.world.entity.ai.control.MoveControl;
import net.minecraft.world.entity.ai.control.MoveControl.Operation;
import net.minecraft.world.entity.ai.goal.Goal;
import net.minecraft.world.entity.ai.goal.Goal.Flag;
import net.minecraft.world.entity.ai.navigation.GroundPathNavigation;
import net.minecraft.world.entity.ai.util.LandRandomPos;
import net.minecraft.world.entity.animal.FlyingAnimal;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.entity.vehicle.AbstractMinecart;
import net.minecraft.world.entity.vehicle.Boat;
import net.minecraft.world.item.AxeItem;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.SwordItem;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.ServerLevelAccessor;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.pathfinder.BlockPathTypes;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.event.ForgeEventFactory;
import software.bernie.geckolib3.core.IAnimatable;
import software.bernie.geckolib3.core.manager.AnimationFactory;
import software.bernie.geckolib3.util.GeckoLibUtil;

public abstract class GreaterSpiritEntity extends HumanoidNPCEntity implements IAnimatable, IElementalSpirit, FlyingAnimal, IFollower, IGiantMob {
   protected static final EntityDataAccessor<Optional<UUID>> SUMMONER_UUID;
   private static final EntityDataAccessor<Integer> MISC_ANIMATION;
   private static final EntityDataAccessor<Integer> SUMMONING_TICK;
   protected static final EntityDataAccessor<Boolean> FLYING;
   private static final EntityDataAccessor<Integer> MAGIC_ID;
   protected ServerBossEvent bossEvent;
   private final AnimationFactory factory = GeckoLibUtil.createFactory(this);
   private CompoundTag shizuNBT = null;
   public float prevFlyProgress;
   public float flyProgress;
   protected boolean wasFlying;
   public int timeFlying = 0;
   public int miscAnimationTicks = 0;

   public GreaterSpiritEntity(EntityType<? extends GreaterSpiritEntity> pEntityType, Level pLevel) {
      super(pEntityType, pLevel);
      this.f_21365_ = new GreaterSpiritEntity.GreaterLookControl();
      this.m_21441_(BlockPathTypes.LAVA, 0.0F);
      this.m_21441_(BlockPathTypes.WATER, 0.0F);
      this.m_21441_(BlockPathTypes.WATER_BORDER, 0.0F);
      this.switchNavigator(false);
      this.f_21364_ = 4000;
      this.f_19793_ = 2.0F;
   }

   protected void switchNavigator(boolean onLand) {
      if (!onLand && !this.m_5803_()) {
         this.f_21342_ = new GreaterSpiritEntity.GreaterMoveControl();
         this.f_21344_ = new NoSpinFlightPathNavigator(this, this.f_19853_);
         this.wasFlying = true;
      } else {
         this.f_21342_ = new TensuraTamableEntity.SleepMoveControl() {
            public void m_8126_() {
               if (GreaterSpiritEntity.this.getMiscAnimation() < 5) {
                  super.m_8126_();
               }
            }
         };
         this.f_21344_ = new GroundPathNavigation(this, this.f_19853_);
         this.wasFlying = false;
      }

   }

   public boolean shouldAttack(LivingEntity entity) {
      if (entity == this) {
         return false;
      } else if (!entity.m_6084_()) {
         return false;
      } else if (this.m_7307_(entity)) {
         return false;
      } else if (this.m_21826_() != null) {
         if (entity.m_7307_(this.m_21826_())) {
            return false;
         } else if (entity instanceof Mob) {
            Mob mob = (Mob)entity;
            return mob.m_5448_() == this.m_21826_();
         } else {
            return this.m_21826_().m_21214_() == entity || this.m_21826_().m_21188_() == entity;
         }
      } else if (this.getSummonerUUID() != null) {
         return false;
      } else {
         boolean var10000;
         if (entity instanceof Player) {
            Player player = (Player)entity;
            if (player.m_7500_() || player.m_5833_()) {
               var10000 = false;
               return var10000;
            }
         }

         var10000 = true;
         return var10000;
      }
   }

   protected void m_8097_() {
      super.m_8097_();
      this.f_19804_.m_135372_(MISC_ANIMATION, 0);
      this.f_19804_.m_135372_(SUMMONING_TICK, -1);
      this.f_19804_.m_135372_(FLYING, false);
      this.f_19804_.m_135372_(MAGIC_ID, 0);
      this.f_19804_.m_135372_(SUMMONER_UUID, Optional.empty());
   }

   public void m_7380_(CompoundTag compound) {
      super.m_7380_(compound);
      if (this.getSummonerUUID() != null) {
         compound.m_128362_("Summoner", this.getSummonerUUID());
      }

      if (this.shizuNBT != null) {
         compound.m_128365_("ShizuNBT", this.shizuNBT);
      }

      compound.m_128379_("Flying", this.m_29443_());
      compound.m_128405_("MiscAnimation", this.getMiscAnimation());
      compound.m_128405_("SummoningTick", this.getSummoningTick());
      compound.m_128405_("MagicID", this.getMagicID());
   }

   public void m_7378_(CompoundTag compound) {
      super.m_7378_(compound);
      if (compound.m_128403_("Summoner")) {
         this.setSummonerUUID(compound.m_128342_("Summoner"));
      }

      if (compound.m_128425_("ShizuNBT", 10)) {
         this.setShizuNBT((CompoundTag)compound.m_128423_("ShizuNBT"));
      }

      this.f_19804_.m_135381_(MISC_ANIMATION, compound.m_128451_("MiscAnimation"));
      this.setSummoningTick(compound.m_128451_("SummoningTick"));
      this.setFlying(compound.m_128471_("Flying"));
      this.setMagicID(compound.m_128451_("MagicID"));
   }

   public int getMiscAnimation() {
      return (Integer)this.f_19804_.m_135370_(MISC_ANIMATION);
   }

   public void setMiscAnimation(int tick) {
      this.f_19804_.m_135381_(MISC_ANIMATION, tick);
   }

   public int getSummoningTick() {
      return (Integer)this.f_19804_.m_135370_(SUMMONING_TICK);
   }

   public void setSummoningTick(int tick) {
      this.f_19804_.m_135381_(SUMMONING_TICK, tick);
   }

   public boolean m_29443_() {
      return (Boolean)this.f_19804_.m_135370_(FLYING);
   }

   public void setFlying(boolean flying) {
      this.f_19804_.m_135381_(FLYING, flying);
   }

   public int getMagicID() {
      return (Integer)this.f_19804_.m_135370_(MAGIC_ID);
   }

   public void setMagicID(int id) {
      this.f_19804_.m_135381_(MAGIC_ID, id);
   }

   @Nullable
   public UUID getSummonerUUID() {
      return (UUID)((Optional)this.f_19804_.m_135370_(SUMMONER_UUID)).orElse((Object)null);
   }

   public void setSummonerUUID(@Nullable UUID pUuid) {
      this.f_19804_.m_135381_(SUMMONER_UUID, Optional.ofNullable(pUuid));
   }

   public void m_21839_(boolean pOrderedToSit) {
      super.m_21839_(pOrderedToSit);
      if (pOrderedToSit && this.m_217043_().m_188503_(4) == 1) {
         this.setMiscAnimation(-1);
      }

   }

   public boolean canSleep() {
      return !this.m_21525_();
   }

   public boolean shouldSwim() {
      return false;
   }

   public boolean m_142535_(float pFallDistance, float pMultiplier, DamageSource pSource) {
      return false;
   }

   public EntityDimensions m_6972_(Pose pPose) {
      EntityDimensions entitydimensions = super.m_6972_(pPose);
      return this.m_5803_() ? entitydimensions.m_20390_(1.0F, 2.0F) : entitydimensions;
   }

   public boolean m_7301_(MobEffectInstance instance) {
      if (instance.m_19544_() == MobEffects.f_19614_) {
         return false;
      } else if (instance.m_19544_() == TensuraMobEffects.FATAL_POISON.get()) {
         return false;
      } else if (instance.m_19544_() == TensuraMobEffects.PARALYSIS.get()) {
         return false;
      } else {
         return instance.m_19544_() == TensuraMobEffects.INFECTION.get() ? false : super.m_7301_(instance);
      }
   }

   public boolean m_6785_(double pDistanceToClosestPlayer) {
      return false;
   }

   protected boolean removeWhenNoAction() {
      return false;
   }

   public boolean m_6779_(LivingEntity pTarget) {
      return this.m_7307_(pTarget) ? false : super.m_6779_(pTarget);
   }

   public boolean m_7307_(Entity entity) {
      if (super.m_7307_(entity)) {
         return true;
      } else if (this.getSummonerUUID() != null) {
         if (entity instanceof IElementalSpirit) {
            IElementalSpirit spirit = (IElementalSpirit)entity;
            return Objects.equals(spirit.getSummonerUUID(), this.getSummonerUUID());
         } else {
            return Objects.equals(entity.m_20148_(), this.getSummonerUUID());
         }
      } else {
         return false;
      }
   }

   @Nullable
   public LivingEntity m_21826_() {
      if (this.getSummonerUUID() != null) {
         Level var2 = this.m_9236_();
         if (var2 instanceof ServerLevel) {
            ServerLevel serverLevel = (ServerLevel)var2;
            Entity entity = serverLevel.m_8791_(this.getSummonerUUID());
            if (entity instanceof Player) {
               Player player = (Player)entity;
               return player;
            } else {
               return null;
            }
         } else {
            return null;
         }
      } else {
         return super.m_21826_();
      }
   }

   public boolean shouldFollow() {
      return !this.m_21827_() && !this.isWandering() && (this.m_5448_() == null || !this.m_5448_().m_6084_());
   }

   public boolean m_7243_(ItemStack pStack) {
      return false;
   }

   public boolean canEquipSlots(EquipmentSlot slot) {
      if (!super.canEquipSlots(slot)) {
         return false;
      } else {
         return !slot.m_20743_().equals(Type.ARMOR);
      }
   }

   public boolean usingMeleeWeapon() {
      Item item = this.m_21205_().m_41720_();
      return item instanceof AxeItem ? true : item instanceof SwordItem;
   }

   public void m_6593_(@Nullable Component pName) {
      super.m_6593_(pName);
      this.bossEvent.m_6456_(this.m_5446_());
   }

   public void m_6457_(ServerPlayer pPlayer) {
      super.m_6457_(pPlayer);
      if (!this.m_21824_()) {
         this.bossEvent.m_6543_(pPlayer);
      }
   }

   public void m_6452_(ServerPlayer pPlayer) {
      super.m_6452_(pPlayer);
      this.bossEvent.m_6539_(pPlayer);
   }

   protected void m_8024_() {
      super.m_8024_();
      this.bossEvent.m_142711_(this.m_21223_() / this.m_21233_());
      if (this.isColliding(this, false)) {
         this.breakBlocks();
         if (this.f_19797_ % 20 == 0) {
            List<BarrierPart> list = this.f_19853_.m_45976_(BarrierPart.class, this.m_20191_().m_82400_(1.0D));
            if (!list.isEmpty()) {
               Iterator var2 = list.iterator();

               while(var2.hasNext()) {
                  BarrierPart barrier = (BarrierPart)var2.next();
                  this.m_7327_(barrier);
               }
            }
         }
      }

   }

   protected void breakBlocks() {
      if (!this.m_21824_() && this.m_29443_() && this.m_5448_() != null) {
         this.breakBlocks(this, 2.0F, true, 0, (SimpleContainer)null, true);
      }

   }

   public boolean breakableBlocks(LivingEntity entity, BlockPos pos, BlockState state) {
      return state.m_204336_(TensuraTags.Blocks.BOSS_IMMUNE) ? false : ForgeEventFactory.onEntityDestroyBlock(entity, pos, state);
   }

   public boolean dropBlockLoot(LivingEntity entity, BlockState state) {
      return !state.m_204336_(TensuraTags.Blocks.SKILL_BREAK_EASY);
   }

   public void m_8119_() {
      super.m_8119_();
      this.handleFlying();
      this.miscAnimationHandler();
      if (!this.m_21824_() && this.f_19797_ % 20 == 0) {
         this.m_5634_(2.0F);
      }

      if (!this.f_19853_.m_5776_()) {
         this.summoningTicking(this);
         if (this.m_20202_() instanceof Boat || this.m_20202_() instanceof AbstractMinecart) {
            this.m_20202_().m_6469_(DamageSource.m_19370_(this), 40.0F);
         }
      }

   }

   protected void handleFlying() {
      this.prevFlyProgress = this.flyProgress;
      if (this.m_29443_()) {
         if (this.flyProgress < 5.0F) {
            ++this.flyProgress;
         }
      } else if (this.flyProgress > 0.0F) {
         --this.flyProgress;
      }

      if (!this.f_19853_.m_5776_()) {
         boolean isFlying = this.m_29443_();
         if (isFlying != this.wasFlying) {
            this.switchNavigator(!isFlying);
         }

         if (isFlying) {
            ++this.timeFlying;
            this.m_20242_(true);
            if (this.m_21827_() || this.m_20159_() || this.m_27593_() || this.m_5803_()) {
               this.setFlying(false);
            }
         } else {
            this.timeFlying = 0;
            this.m_20242_(false);
         }
      }

   }

   protected void miscAnimationHandler() {
   }

   public void m_7023_(Vec3 vec3d) {
      if (this.m_20069_() && this.m_20184_().f_82480_ > 0.0D) {
         this.m_20256_(this.m_20184_().m_82542_(1.2D, 1.2D, 1.2D));
      }

      super.m_7023_(vec3d);
   }

   public void followEntity(TamableAnimal animal, LivingEntity owner, double followSpeed) {
      if (this.m_20270_(owner) > 5.0F) {
         this.setFlying(true);
         this.m_21566_().m_6849_(owner.m_20185_(), owner.m_20186_() + (double)owner.m_20206_(), owner.m_20189_(), followSpeed);
      } else {
         if (this.f_19861_) {
            this.setFlying(false);
         }

         if (this.m_29443_()) {
            BlockPos vec = this.getGround(this, this.m_20183_());
            this.m_21566_().m_6849_((double)vec.m_123341_(), (double)vec.m_123342_(), (double)vec.m_123343_(), followSpeed);
         } else {
            this.m_21573_().m_5624_(owner, followSpeed);
         }
      }

   }

   public boolean m_6898_(ItemStack pStack) {
      return pStack.m_204117_(TensuraTags.Items.SPIRIT_FOOD);
   }

   public SpiritualMagic.SpiritLevel getSpiritLevel() {
      return SpiritualMagic.SpiritLevel.GREATER;
   }

   public abstract Item getElementalCore();

   public InteractionResult m_6071_(Player player, InteractionHand hand) {
      ItemStack itemstack = player.m_21120_(hand);
      if (itemstack.m_41720_() instanceof HealingPotionItem) {
         return super.m_6071_(player, hand);
      } else {
         InteractionResult eating = this.handleEating(player, hand, itemstack);
         if (eating.m_19077_()) {
            return eating;
         } else if (!this.f_19853_.f_46443_) {
            if (this.m_21824_()) {
               if (this.convertElementalCore(this, player, hand, this.getElementalCore())) {
                  return InteractionResult.SUCCESS;
               }

               if (!super.m_6071_(player, hand).m_19077_() && this.m_21830_(player)) {
                  this.commanding(player);
                  return InteractionResult.SUCCESS;
               }
            }

            return InteractionResult.PASS;
         } else {
            boolean flag = this.m_21830_(player) || this.m_21824_();
            return flag ? InteractionResult.CONSUME : InteractionResult.PASS;
         }
      }
   }

   public InteractionResult handleEating(Player player, InteractionHand hand, ItemStack itemstack) {
      if (this.m_6898_(itemstack) && this.m_21223_() < this.m_21233_()) {
         if (!player.m_7500_()) {
            itemstack.m_41774_(1);
         }

         this.m_8035_();
         this.m_9236_().m_6269_((Player)null, this, (SoundEvent)TensuraSoundEvents.EATING.get(), SoundSource.NEUTRAL, 1.0F, 1.0F);
         return InteractionResult.SUCCESS;
      } else {
         return InteractionResult.PASS;
      }
   }

   public void m_8035_() {
      super.m_8035_();
      this.m_5634_(3.0F);
   }

   @Nullable
   public SpawnGroupData m_6518_(ServerLevelAccessor pLevel, DifficultyInstance pDifficulty, MobSpawnType pReason, @Nullable SpawnGroupData pSpawnData, @Nullable CompoundTag pDataTag) {
      if (!this.m_20096_()) {
         this.setFlying(true);
      }

      return super.m_6518_(pLevel, pDifficulty, pReason, pSpawnData, pDataTag);
   }

   public void m_6667_(DamageSource source) {
      super.m_6667_(source);
      if (!this.m_6084_()) {
         if (this.m_20089_() == Pose.DYING) {
            this.setMiscAnimation(8);
         }

         if (!this.f_19853_.m_5776_()) {
            if (this.shizuNBT != null) {
               ShizuEntity entity = new ShizuEntity((EntityType)TensuraEntityTypes.SHIZU.get(), this.m_9236_());
               entity.m_20258_(this.shizuNBT);
               entity.m_20219_(this.m_20182_());
               entity.setTransformTick(200);
               entity.setSleeping(true);
               entity.setDying(true);
               entity.m_21153_(entity.m_21233_());
               this.m_9236_().m_7967_(entity);
               this.spawnDeathParticles();
            }
         }
      }
   }

   protected abstract void spawnDeathParticles();

   protected void m_6668_(DamageSource pDamageSource) {
      if (this.getSummoningTick() >= 0) {
         this.m_5907_();
      } else {
         super.m_6668_(pDamageSource);
      }

   }

   public AnimationFactory getFactory() {
      return this.factory;
   }

   public void setShizuNBT(CompoundTag shizuNBT) {
      this.shizuNBT = shizuNBT;
   }

   static {
      SUMMONER_UUID = SynchedEntityData.m_135353_(GreaterSpiritEntity.class, EntityDataSerializers.f_135041_);
      MISC_ANIMATION = SynchedEntityData.m_135353_(GreaterSpiritEntity.class, EntityDataSerializers.f_135028_);
      SUMMONING_TICK = SynchedEntityData.m_135353_(GreaterSpiritEntity.class, EntityDataSerializers.f_135028_);
      FLYING = SynchedEntityData.m_135353_(GreaterSpiritEntity.class, EntityDataSerializers.f_135035_);
      MAGIC_ID = SynchedEntityData.m_135353_(GreaterSpiritEntity.class, EntityDataSerializers.f_135028_);
   }

   public class GreaterLookControl extends TensuraTamableEntity.SleepLookControl {
      public GreaterLookControl() {
         super();
      }

      public void m_8128_() {
         GreaterSpiritEntity entity = GreaterSpiritEntity.this;
         if (entity.getMiscAnimation() != -1) {
            super.m_8128_();
         }
      }
   }

   public class GreaterMoveControl extends MoveControl {
      private final Mob parentEntity = GreaterSpiritEntity.this;

      public GreaterMoveControl() {
         super(GreaterSpiritEntity.this);
      }

      public void m_8126_() {
         if (GreaterSpiritEntity.this.getMiscAnimation() < 5) {
            if (this.f_24981_ == Operation.MOVE_TO) {
               Vec3 vector3d = new Vec3(this.f_24975_ - this.parentEntity.m_20185_(), this.f_24976_ - this.parentEntity.m_20186_(), this.f_24977_ - this.parentEntity.m_20189_());
               double d0 = vector3d.m_82553_();
               double width = this.parentEntity.m_20191_().m_82309_();
               Vec3 vector3d1 = vector3d.m_82490_(this.f_24978_ * 0.05D / d0);
               this.parentEntity.m_20256_(this.parentEntity.m_20184_().m_82549_(vector3d1).m_82490_(0.95D).m_82520_(0.0D, -0.01D, 0.0D));
               if (d0 < width) {
                  this.f_24981_ = Operation.WAIT;
               } else if (d0 >= width) {
                  float yaw = -((float)Mth.m_14136_(vector3d1.f_82479_, vector3d1.f_82481_)) * 57.295776F;
                  this.parentEntity.m_146922_(Mth.m_14148_(this.parentEntity.m_146908_(), yaw, 8.0F));
               }
            }

         }
      }
   }

   public static class FollowHinataGoal extends IElementalSpirit.FollowGreaterSpiritGoal {
      private final GreaterSpiritEntity greaterSpirit;

      public FollowHinataGoal(GreaterSpiritEntity spirit, double pSpeedModifier, Class<? extends Mob> greaterClass) {
         super(spirit, pSpeedModifier, greaterClass);
         this.greaterSpirit = spirit;
      }

      public boolean m_8036_() {
         if (this.spirit.m_21826_() != null) {
            return false;
         } else if (!this.spirit.m_21523_() && !this.spirit.m_20159_()) {
            if (this.greaterSpirit.getSummonerUUID() != null) {
               Entity var2 = ((ServerLevel)this.greaterSpirit.m_9236_()).m_8791_(this.greaterSpirit.getSummonerUUID());
               if (var2 instanceof Mob) {
                  Mob mob = (Mob)var2;
                  this.greater = mob;
                  return true;
               }
            }

            return false;
         } else {
            return false;
         }
      }

      public boolean m_8045_() {
         if (this.spirit.m_21824_()) {
            return false;
         } else if (!this.spirit.m_21523_() && !this.spirit.m_20159_()) {
            return this.greater == null ? false : this.greater.m_6084_();
         } else {
            return false;
         }
      }
   }

   public class WalkGoal extends Goal {
      protected final GreaterSpiritEntity entity;
      protected double x;
      protected double y;
      protected double z;
      private boolean flightTarget = false;

      public WalkGoal(GreaterSpiritEntity entity) {
         this.m_7021_(EnumSet.of(Flag.MOVE));
         this.entity = entity;
      }

      public boolean m_8036_() {
         if (!this.entity.m_20160_() && (this.entity.m_5448_() == null || !this.entity.m_5448_().m_6084_()) && !this.entity.m_20159_() && !this.entity.m_21827_()) {
            if (this.entity.m_217043_().m_188503_(30) != 0 && !this.entity.m_29443_()) {
               return false;
            } else {
               if (this.entity.m_20096_()) {
                  this.flightTarget = GreaterSpiritEntity.this.f_19796_.m_188499_();
               } else {
                  this.flightTarget = GreaterSpiritEntity.this.f_19796_.m_188503_(5) > 0 && this.entity.timeFlying < 200;
               }

               Vec3 position = this.getPosition();
               if (position == null) {
                  return false;
               } else {
                  this.x = position.f_82479_;
                  this.y = position.f_82480_;
                  this.z = position.f_82481_;
                  return true;
               }
            }
         } else {
            return false;
         }
      }

      public void m_8037_() {
         if (this.flightTarget) {
            this.entity.m_21566_().m_6849_(this.x, this.y, this.z, 1.0D);
         } else {
            this.entity.m_21573_().m_26519_(this.x, this.y, this.z, 1.0D);
            if (GreaterSpiritEntity.this.m_29443_() && this.entity.f_19861_) {
               this.entity.setFlying(false);
            }
         }

         if (GreaterSpiritEntity.this.m_29443_() && this.entity.f_19861_ && this.entity.timeFlying > 10) {
            this.entity.setFlying(false);
         }

      }

      @Nullable
      protected Vec3 getPosition() {
         Vec3 vector3d = this.entity.m_20182_();
         if (GreaterSpiritEntity.this.isOverWater(this.entity)) {
            this.flightTarget = true;
         }

         Vec3 pos = Vec3.m_82512_(this.entity.getWanderPos());
         double distance = (Double)TensuraConfig.INSTANCE.entitiesConfig.tamedWanderRadius.get();
         if (this.entity.isWandering() && this.entity.m_20238_(pos) >= distance * distance) {
            return pos;
         } else if (this.flightTarget) {
            return this.entity.timeFlying < 50 ? GreaterSpiritEntity.this.getBlockInViewAway(this.entity, vector3d, 0.0F) : GreaterSpiritEntity.this.getBlockGrounding(this.entity, vector3d);
         } else {
            return LandRandomPos.m_148488_(this.entity, 10, 7);
         }
      }

      public boolean m_8045_() {
         if (this.entity.m_21827_()) {
            return false;
         } else if (this.flightTarget) {
            return this.entity.m_29443_() && this.entity.m_20275_(this.x, this.y, this.z) > 2.0D;
         } else {
            return !this.entity.m_21573_().m_26571_() && !this.entity.m_20160_();
         }
      }

      public void m_8056_() {
         if (this.flightTarget) {
            this.entity.setFlying(true);
            this.entity.m_21566_().m_6849_(this.x, this.y, this.z, 1.0D);
         } else {
            this.entity.m_21573_().m_26519_(this.x, this.y, this.z, 1.0D);
         }

      }

      public void m_8041_() {
         this.entity.m_21573_().m_26573_();
         super.m_8041_();
      }
   }
}
