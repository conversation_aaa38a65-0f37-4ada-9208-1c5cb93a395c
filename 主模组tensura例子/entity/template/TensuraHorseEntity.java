package com.github.manasmods.tensura.entity.template;

import com.github.manasmods.tensura.api.entity.ai.BetterWanderAroundGoal;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.config.TensuraConfig;
import com.github.manasmods.tensura.entity.HoverLizardEntity;
import java.util.EnumSet;
import java.util.Objects;
import java.util.UUID;
import javax.annotation.Nullable;
import net.minecraft.ChatFormatting;
import net.minecraft.core.BlockPos;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.network.chat.Style;
import net.minecraft.network.syncher.EntityDataAccessor;
import net.minecraft.network.syncher.EntityDataSerializers;
import net.minecraft.network.syncher.SynchedEntityData;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.world.Container;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.InteractionResult;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.OwnableEntity;
import net.minecraft.world.entity.ai.attributes.AttributeInstance;
import net.minecraft.world.entity.ai.attributes.AttributeModifier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.attributes.AttributeModifier.Operation;
import net.minecraft.world.entity.ai.goal.Goal;
import net.minecraft.world.entity.ai.goal.Goal.Flag;
import net.minecraft.world.entity.ai.goal.target.HurtByTargetGoal;
import net.minecraft.world.entity.ai.goal.target.NearestAttackableTargetGoal;
import net.minecraft.world.entity.ai.navigation.FlyingPathNavigation;
import net.minecraft.world.entity.ai.navigation.GroundPathNavigation;
import net.minecraft.world.entity.ai.navigation.PathNavigation;
import net.minecraft.world.entity.animal.Animal;
import net.minecraft.world.entity.animal.horse.AbstractChestedHorse;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.HorseArmorItem;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;
import net.minecraft.world.item.enchantment.EnchantmentHelper;
import net.minecraft.world.level.GameRules;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.LevelReader;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.block.LeavesBlock;
import net.minecraft.world.level.block.SoundType;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.pathfinder.BlockPathTypes;
import net.minecraft.world.level.pathfinder.WalkNodeEvaluator;
import net.minecraft.world.phys.Vec3;
import net.minecraft.world.scores.Team;

public class TensuraHorseEntity extends AbstractChestedHorse implements OwnableEntity {
   private static final EntityDataAccessor<Integer> CUSTOM_OWNER_COMMAND;
   private static final EntityDataAccessor<Integer> CUSTOM_BEHAVIOUR;
   private static final EntityDataAccessor<Boolean> SITTING;
   private static final EntityDataAccessor<BlockPos> WANDER_POS;
   private static final UUID ARMOR_MODIFIER_UUID;
   protected int additionalTemper;

   public TensuraHorseEntity(EntityType<? extends AbstractChestedHorse> pEntityType, Level pLevel) {
      super(pEntityType, pLevel);
      this.f_21346_.m_25352_(7, new TensuraHorseEntity.TargetingBehaviourGoal(this));
   }

   protected void m_8097_() {
      super.m_8097_();
      this.f_19804_.m_135372_(SITTING, false);
      this.f_19804_.m_135372_(CUSTOM_OWNER_COMMAND, 0);
      this.f_19804_.m_135372_(CUSTOM_BEHAVIOUR, 0);
      this.f_19804_.m_135372_(WANDER_POS, BlockPos.f_121853_);
   }

   public void m_7380_(CompoundTag compound) {
      super.m_7380_(compound);
      if (!this.f_30520_.m_8020_(1).m_41619_()) {
         compound.m_128365_("ArmorItem", this.f_30520_.m_8020_(1).m_41739_(new CompoundTag()));
      }

      if (this.m_21805_() == null) {
         compound.m_128473_("Owner");
      }

      compound.m_128379_("Sitting", this.isSitting());
      compound.m_128405_("OwnerCommand", this.getOwnerCommand());
      compound.m_128405_("Behaviour", this.getBehaviour());
      compound.m_128405_("WanderPosX", this.getWanderPos().m_123341_());
      compound.m_128405_("WanderPosY", this.getWanderPos().m_123342_());
      compound.m_128405_("WanderPosZ", this.getWanderPos().m_123343_());
   }

   public void m_7378_(CompoundTag compound) {
      super.m_7378_(compound);
      if (compound.m_128425_("ArmorItem", 10)) {
         ItemStack itemstack = ItemStack.m_41712_(compound.m_128469_("ArmorItem"));
         if (!itemstack.m_41619_() && this.m_6010_(itemstack)) {
            this.f_30520_.m_6836_(1, itemstack);
         }
      }

      this.m_7493_();
      this.setSitting(compound.m_128471_("Sitting"));
      this.setOwnerCommand(compound.m_128451_("OwnerCommand"));
      this.setBehaviour(compound.m_128451_("Behaviour"));
      this.setWanderPos(new BlockPos(compound.m_128451_("WanderPosX"), compound.m_128451_("WanderPosY"), compound.m_128451_("WanderPosZ")));
   }

   public int getOwnerCommand() {
      return (Integer)this.f_19804_.m_135370_(CUSTOM_OWNER_COMMAND);
   }

   public void setOwnerCommand(int command) {
      this.f_19804_.m_135381_(CUSTOM_OWNER_COMMAND, command);
   }

   public int getBehaviour() {
      return (Integer)this.f_19804_.m_135370_(CUSTOM_BEHAVIOUR);
   }

   public void setBehaviour(int behaviour) {
      this.f_19804_.m_135381_(CUSTOM_BEHAVIOUR, behaviour);
   }

   public boolean isWandering() {
      return this.getOwnerCommand() == 1;
   }

   public void setWandering(boolean wandering) {
      int mode = wandering ? 1 : 0;
      this.f_19804_.m_135381_(CUSTOM_OWNER_COMMAND, mode);
   }

   public void setWanderPos(BlockPos pPos) {
      this.f_19804_.m_135381_(WANDER_POS, pPos);
   }

   public BlockPos getWanderPos() {
      return (BlockPos)this.f_19804_.m_135370_(WANDER_POS);
   }

   public void resetOwner(@Nullable UUID ownerUUID) {
      if (ownerUUID == null) {
         this.m_30651_(false);
         this.m_30586_((UUID)null);
      } else {
         this.m_30651_(true);
         this.m_30586_(ownerUUID);
      }

      this.setSitting(false);
      this.setBehaviour(0);
      this.setWandering(false);
      this.m_6710_((LivingEntity)null);
   }

   public boolean m_6573_(Player pPlayer) {
      return !this.m_21523_() && this.isOwnedBy(pPlayer);
   }

   public boolean m_7848_(Animal pOtherAnimal) {
      if (pOtherAnimal == this) {
         return false;
      } else if (!(pOtherAnimal instanceof TensuraHorseEntity)) {
         return false;
      } else {
         TensuraHorseEntity horse = (TensuraHorseEntity)pOtherAnimal;
         return this.m_30628_() && horse.m_30628_();
      }
   }

   protected boolean m_6107_() {
      return super.m_6107_() || this.isSitting();
   }

   public int getAdditionalTemper() {
      return this.additionalTemper;
   }

   public void setAdditionalTemper(int pTemper) {
      this.additionalTemper = pTemper;
   }

   public boolean m_6785_(double pDistanceToClosestPlayer) {
      return !this.m_30614_();
   }

   public boolean m_7482_() {
      return true;
   }

   public boolean m_6010_(ItemStack pStack) {
      return pStack.m_41720_() instanceof HorseArmorItem;
   }

   public ItemStack getArmor() {
      return this.m_6844_(EquipmentSlot.CHEST);
   }

   private void setArmor(ItemStack pStack) {
      this.m_8061_(EquipmentSlot.CHEST, pStack);
      this.m_21409_(EquipmentSlot.CHEST, 0.0F);
   }

   protected void m_7493_() {
      if (!this.f_19853_.f_46443_) {
         super.m_7493_();
         this.setArmorEquipment(this.f_30520_.m_8020_(1));
         this.m_21409_(EquipmentSlot.CHEST, 0.0F);
      }
   }

   private void setArmorEquipment(ItemStack pStack) {
      this.setArmor(pStack);
      if (!this.f_19853_.f_46443_) {
         ((AttributeInstance)Objects.requireNonNull(this.m_21051_(Attributes.f_22284_))).m_22120_(ARMOR_MODIFIER_UUID);
         if (this.m_6010_(pStack)) {
            int i = ((HorseArmorItem)pStack.m_41720_()).m_41368_();
            if (i != 0) {
               ((AttributeInstance)Objects.requireNonNull(this.m_21051_(Attributes.f_22284_))).m_22118_(new AttributeModifier(ARMOR_MODIFIER_UUID, "Horse armor bonus", (double)i, Operation.ADDITION));
            }
         }
      }
   }

   protected int m_7506_() {
      return this.m_30502_() ? 18 : super.m_7506_();
   }

   public void m_5757_(Container pInvBasic) {
      ItemStack itemstack = this.getArmor();
      super.m_5757_(pInvBasic);
      ItemStack armor = this.getArmor();
      if (this.f_19797_ > 20 && this.m_6010_(armor) && itemstack != armor) {
         this.m_5496_(SoundEvents.f_11973_, 0.5F, 1.0F);
      }

   }

   protected void dropChest() {
      if (this.m_30502_()) {
         this.m_19998_(Blocks.f_50087_);

         for(int i = 2; i < this.f_30520_.m_6643_(); ++i) {
            ItemStack itemstack = this.f_30520_.m_8020_(i);
            if (!itemstack.m_41619_() && !EnchantmentHelper.m_44924_(itemstack)) {
               this.m_19983_(itemstack);
            }

            this.f_30520_.m_8016_(i);
         }

      }
   }

   public void m_7564_() {
      int i = this.m_30624_() - 5;
      this.m_30649_(Math.max(i, 0));
      super.m_7564_();
   }

   public void commanding(Player player) {
      MutableComponent message;
      if (this.isSitting()) {
         message = Component.m_237110_("tensura.message.pet.follow", new Object[]{this.m_5446_()});
         this.setSitting(false);
         this.setWandering(false);
      } else if (!this.isWandering()) {
         message = Component.m_237110_("tensura.message.pet.wander", new Object[]{this.m_5446_()});
         this.setSitting(false);
         this.m_6710_((LivingEntity)null);
         this.setWandering(true);
         this.setWanderPos(player.m_20097_().m_7494_());
      } else {
         message = Component.m_237110_("tensura.message.pet.stay", new Object[]{this.m_5446_()});
         this.m_21573_().m_26573_();
         this.setSitting(true);
         this.setWandering(false);
         this.m_6710_((LivingEntity)null);
      }

      player.m_5661_(message.m_6270_(Style.f_131099_.m_131140_(ChatFormatting.AQUA)), true);
   }

   public void cycleBehaviour(LivingEntity entity) {
      int behaviour = this.getBehaviour() + 1;
      MutableComponent message;
      switch(this.getBehaviour()) {
      case 1:
         message = Component.m_237110_("tensura.message.pet.aggressive", new Object[]{this.m_5446_()});
         break;
      case 2:
         this.m_6710_((LivingEntity)null);
         message = Component.m_237110_("tensura.message.pet.protect", new Object[]{this.m_5446_()});
         break;
      case 3:
         behaviour = 0;
         this.m_6710_((LivingEntity)null);
         message = Component.m_237110_("tensura.message.pet.neutral", new Object[]{this.m_5446_()});
         break;
      default:
         this.m_6710_((LivingEntity)null);
         message = Component.m_237110_("tensura.message.pet.passive", new Object[]{this.m_5446_()});
      }

      this.setBehaviour(behaviour);
      if (entity instanceof Player) {
         Player player = (Player)entity;
         player.m_5661_(message.m_6270_(Style.f_131099_.m_131140_(ChatFormatting.AQUA)), true);
      }

   }

   public InteractionResult m_6071_(Player pPlayer, InteractionHand pHand) {
      ItemStack itemstack = pPlayer.m_21120_(pHand);
      if (itemstack.m_41619_()) {
         if (this.m_20160_()) {
            return InteractionResult.PASS;
         }

         if (this.m_30614_() && this.isOwnedBy(pPlayer) && pPlayer.m_36341_() && !this.f_19853_.f_46443_) {
            this.commanding(pPlayer);
            return InteractionResult.SUCCESS;
         }

         if (this.m_30614_() && this.isOwnedBy(pPlayer) || !this.m_30614_()) {
            this.m_6835_(pPlayer);
            return InteractionResult.m_19078_(this.f_19853_.f_46443_);
         }
      } else {
         if (this.m_6898_(itemstack)) {
            return this.m_30580_(pPlayer, itemstack);
         }

         if (!this.m_30614_()) {
            if (this instanceof HoverLizardEntity) {
               HoverLizardEntity lizard = (HoverLizardEntity)this;
               if (lizard.m_5803_()) {
                  return InteractionResult.PASS;
               }
            }

            this.m_7564_();
            return InteractionResult.m_19078_(this.f_19853_.f_46443_);
         }

         if (!this.m_30502_() && itemstack.m_150930_(Blocks.f_50087_.m_5456_())) {
            this.m_30504_(true);
            this.m_5496_(SoundEvents.f_11811_, 1.0F, (this.f_19796_.m_188501_() - this.f_19796_.m_188501_()) * 0.2F + 1.0F);
            if (!pPlayer.m_150110_().f_35937_) {
               itemstack.m_41774_(1);
            }

            this.m_30625_();
            return InteractionResult.m_19078_(this.f_19853_.f_46443_);
         }

         if (this.m_30502_() && itemstack.m_150930_(Items.f_42574_)) {
            this.dropChest();
            this.m_30504_(false);
            this.m_5496_(SoundEvents.f_12344_, 1.0F, (this.f_19796_.m_188501_() - this.f_19796_.m_188501_()) * 0.2F + 1.0F);
            return InteractionResult.m_19078_(this.f_19853_.f_46443_);
         }

         boolean saddling = !this.m_6162_() && !this.m_6254_() && itemstack.m_150930_(Items.f_42450_);
         if (this.m_6010_(itemstack) || saddling) {
            this.m_213583_(pPlayer);
            return InteractionResult.m_19078_(this.f_19853_.f_46443_);
         }
      }

      return InteractionResult.PASS;
   }

   protected void m_5877_(SoundType pSoundType) {
      super.m_5877_(pSoundType);
      if (this.f_19796_.m_188503_(10) == 0) {
         this.m_5496_(SoundEvents.f_11974_, pSoundType.m_56773_() * 0.6F, pSoundType.m_56774_());
      }

      ItemStack stack = this.f_30520_.m_8020_(1);
      if (this.m_6010_(stack)) {
         stack.onHorseArmorTick(this.f_19853_, this);
      }

   }

   protected SoundEvent m_7515_() {
      super.m_7515_();
      return SoundEvents.f_11971_;
   }

   protected SoundEvent m_5592_() {
      super.m_5592_();
      return SoundEvents.f_11975_;
   }

   @Nullable
   protected SoundEvent m_7872_() {
      return SoundEvents.f_11976_;
   }

   protected SoundEvent m_7975_(DamageSource pDamageSource) {
      super.m_7975_(pDamageSource);
      return SoundEvents.f_11978_;
   }

   protected SoundEvent m_7871_() {
      super.m_7871_();
      return SoundEvents.f_11972_;
   }

   @Nullable
   public UUID m_21805_() {
      UUID temporary = TensuraEPCapability.getTemporaryOwner(this);
      if (temporary != null) {
         return temporary;
      } else {
         UUID permanent = TensuraEPCapability.getPermanentOwner(this);
         return permanent != null ? permanent : super.m_30615_();
      }
   }

   @Nullable
   public LivingEntity getOwner() {
      if (!this.m_30614_()) {
         return null;
      } else {
         UUID uuid = this.m_21805_();
         return uuid == null ? null : this.f_19853_.m_46003_(uuid);
      }
   }

   public boolean isOwnedBy(LivingEntity pEntity) {
      if (this.getOwner() == null) {
         return false;
      } else {
         return pEntity == this.getOwner();
      }
   }

   public Team m_5647_() {
      if (this.m_30614_()) {
         LivingEntity livingentity = this.getOwner();
         if (livingentity != null) {
            return livingentity.m_5647_();
         }
      }

      return super.m_5647_();
   }

   public boolean m_7307_(Entity pEntity) {
      if (this.m_30614_()) {
         LivingEntity livingentity = this.getOwner();
         if (pEntity == livingentity) {
            return true;
         }

         if (livingentity != null) {
            return livingentity.m_7307_(pEntity);
         }
      }

      return super.m_7307_(pEntity);
   }

   public void m_6667_(DamageSource pCause) {
      Component deathMessage = this.m_21231_().m_19293_();
      super.m_6667_(pCause);
      if (this.f_20890_ && !this.f_19853_.f_46443_ && this.getOwner() instanceof ServerPlayer && this.f_19853_.m_46469_().m_46207_(GameRules.f_46142_)) {
         this.getOwner().m_213846_(deathMessage);
      }

   }

   public boolean isSitting() {
      return (Boolean)this.f_19804_.m_135370_(SITTING);
   }

   public void setSitting(boolean sitting) {
      this.f_19804_.m_135381_(SITTING, sitting);
   }

   static {
      CUSTOM_OWNER_COMMAND = SynchedEntityData.m_135353_(TensuraHorseEntity.class, EntityDataSerializers.f_135028_);
      CUSTOM_BEHAVIOUR = SynchedEntityData.m_135353_(TensuraHorseEntity.class, EntityDataSerializers.f_135028_);
      SITTING = SynchedEntityData.m_135353_(TensuraHorseEntity.class, EntityDataSerializers.f_135035_);
      WANDER_POS = SynchedEntityData.m_135353_(TensuraHorseEntity.class, EntityDataSerializers.f_135038_);
      ARMOR_MODIFIER_UUID = UUID.fromString("556E1665-8B10-40C8-8F9D-CF9B1667F295");
   }

   public static class TargetingBehaviourGoal extends NearestAttackableTargetGoal<LivingEntity> {
      private final TensuraHorseEntity entity;
      private boolean shouldStop;

      public TargetingBehaviourGoal(TensuraHorseEntity entity) {
         super(entity, LivingEntity.class, true, (target) -> {
            if (target instanceof Player) {
               Player player = (Player)target;
               if (player.m_7500_() || player.m_5833_()) {
                  return false;
               }
            }

            if (entity.isWandering()) {
               double distance = (Double)TensuraConfig.INSTANCE.entitiesConfig.tamedWanderRadius.get();
               if (target.m_20238_(Vec3.m_82512_(entity.getWanderPos())) > distance * distance) {
                  return false;
               }
            }

            if (entity.getBehaviour() == 2) {
               if (entity.getOwner() != null && target.m_7307_(entity.getOwner())) {
                  return false;
               } else {
                  return target != entity && !entity.m_7307_(target);
               }
            } else if (entity.getBehaviour() != 3) {
               return false;
            } else if (target.m_7307_(entity)) {
               return false;
            } else {
               if (target instanceof Mob) {
                  Mob mobTarget = (Mob)target;
                  if (mobTarget.m_5448_() != null) {
                     return mobTarget.m_5448_().m_7307_(entity);
                  }
               }

               if (entity.getOwner() != null) {
                  if (target.m_7307_(entity.getOwner())) {
                     return false;
                  } else {
                     return entity.getOwner().m_21214_() == target || entity.getOwner().m_21188_() == target;
                  }
               } else {
                  return false;
               }
            }
         });
         this.entity = entity;
      }

      public boolean m_8036_() {
         if (!this.entity.m_30614_()) {
            return false;
         } else if (this.entity.getBehaviour() < 2) {
            return false;
         } else if (super.m_8036_()) {
            this.shouldStop = false;
            return true;
         } else {
            return false;
         }
      }

      public boolean m_8045_() {
         if (!this.entity.m_30614_()) {
            return false;
         } else if (this.entity.getBehaviour() < 2) {
            return false;
         } else {
            return !this.shouldStop;
         }
      }

      public boolean m_183429_() {
         return true;
      }

      public void m_8037_() {
         if (!super.m_8045_()) {
            this.shouldStop = true;
         } else if (this.isWanderingTooFar()) {
            this.entity.m_6710_((LivingEntity)null);
            Vec3 pos = Vec3.m_82512_(this.entity.getWanderPos());
            this.entity.m_21566_().m_6849_(pos.f_82479_, pos.f_82480_, pos.f_82481_, 2.0D);
            this.shouldStop = true;
         }

      }

      private boolean isWanderingTooFar() {
         Vec3 pos = Vec3.m_82512_(this.entity.getWanderPos());
         double distance = (Double)TensuraConfig.INSTANCE.entitiesConfig.tamedWanderRadius.get();
         return this.entity.isWandering() && this.entity.m_20238_(pos) >= distance * distance;
      }
   }

   public static class WanderAroundPosGoal extends BetterWanderAroundGoal {
      private final TensuraHorseEntity entity;

      public WanderAroundPosGoal(TensuraHorseEntity mob, int interval, double speed, int xzRange, int yRange) {
         super(mob, interval, speed, xzRange, yRange);
         this.entity = mob;
      }

      public WanderAroundPosGoal(TensuraHorseEntity mob, double speed, int xzRange, int yRange) {
         super(mob, speed, xzRange, yRange);
         this.entity = mob;
      }

      public WanderAroundPosGoal(TensuraHorseEntity mob, double speed) {
         super(mob, speed);
         this.entity = mob;
      }

      public WanderAroundPosGoal(TensuraHorseEntity mob) {
         super(mob);
         this.entity = mob;
      }

      public boolean m_8045_() {
         return this.isWanderingTooFar() && !this.entity.m_20160_() ? true : super.m_8045_();
      }

      private boolean isWanderingTooFar() {
         Vec3 pos = Vec3.m_82512_(this.entity.getWanderPos());
         double distance = (Double)TensuraConfig.INSTANCE.entitiesConfig.tamedWanderRadius.get();
         return this.entity.isWandering() && this.entity.m_20238_(pos) >= distance * distance;
      }

      @Nullable
      protected Vec3 m_7037_() {
         return this.isWanderingTooFar() && !this.entity.m_20160_() ? Vec3.m_82512_(this.entity.getWanderPos()) : super.m_7037_();
      }
   }

   public static class TensuraHurtByTargetGoal extends HurtByTargetGoal {
      private final TensuraHorseEntity entity;

      public TensuraHurtByTargetGoal(TensuraHorseEntity entity, Class<?>... pToIgnoreDamage) {
         super(entity, pToIgnoreDamage);
         this.entity = entity;
      }

      public TensuraHurtByTargetGoal(TensuraHorseEntity entity) {
         super(entity, new Class[0]);
         this.entity = entity;
      }

      public boolean m_8036_() {
         return this.entity.getBehaviour() == 1 ? false : super.m_8036_();
      }
   }

   public static class HorseFollowOwnerGoal extends Goal {
      private final TensuraHorseEntity horse;
      private LivingEntity owner;
      private final LevelReader level;
      private final double speedModifier;
      private final PathNavigation navigation;
      private int timeToRecalcPath;
      private final float stopDistance;
      private final float startDistance;
      private float oldWaterCost;
      private final boolean canFly;

      public HorseFollowOwnerGoal(TensuraHorseEntity unicorn, double pSpeedModifier, float pStartDistance, float pStopDistance, boolean pCanFly) {
         this.horse = unicorn;
         this.level = unicorn.f_19853_;
         this.speedModifier = pSpeedModifier;
         this.navigation = unicorn.m_21573_();
         this.startDistance = pStartDistance;
         this.stopDistance = pStopDistance;
         this.canFly = pCanFly;
         this.m_7021_(EnumSet.of(Flag.MOVE, Flag.LOOK));
         if (!(unicorn.m_21573_() instanceof GroundPathNavigation) && !(unicorn.m_21573_() instanceof FlyingPathNavigation)) {
            throw new IllegalArgumentException("Unsupported Path Navigation for HorseFollowOwnerGoal");
         }
      }

      public boolean m_8036_() {
         if (!this.horse.m_30614_()) {
            return false;
         } else {
            LivingEntity livingentity = this.horse.getOwner();
            if (livingentity == null) {
               return false;
            } else if (livingentity.m_5833_()) {
               return false;
            } else if (this.horse.isSitting()) {
               return false;
            } else if (this.horse.isWandering()) {
               return false;
            } else if (this.horse.m_6162_()) {
               return false;
            } else if (this.horse.m_20280_(livingentity) < (double)(this.startDistance * this.startDistance)) {
               return false;
            } else {
               this.owner = livingentity;
               return true;
            }
         }
      }

      public boolean m_8045_() {
         if (this.navigation.m_26571_()) {
            return false;
         } else if (this.horse.isSitting()) {
            return false;
         } else if (this.horse.isWandering()) {
            return false;
         } else {
            return !(this.horse.m_20280_(this.owner) <= (double)(this.stopDistance * this.stopDistance));
         }
      }

      public void m_8056_() {
         this.timeToRecalcPath = 0;
         this.oldWaterCost = this.horse.m_21439_(BlockPathTypes.WATER);
         this.horse.m_21441_(BlockPathTypes.WATER, 0.0F);
      }

      public void m_8041_() {
         this.owner = null;
         this.navigation.m_26573_();
         this.horse.m_21441_(BlockPathTypes.WATER, this.oldWaterCost);
      }

      public void m_8037_() {
         this.horse.m_21563_().m_24960_(this.owner, 10.0F, (float)this.horse.m_8132_());
         if (--this.timeToRecalcPath <= 0) {
            this.timeToRecalcPath = this.m_183277_(10);
            if (!this.horse.m_21523_() && !this.horse.m_20159_()) {
               if (this.horse.m_20280_(this.owner) >= 400.0D) {
                  this.teleportToOwner();
               } else {
                  this.navigation.m_5624_(this.owner, this.speedModifier);
               }

            }
         }
      }

      private void teleportToOwner() {
         BlockPos blockpos = this.owner.m_20183_();

         for(int i = 0; i < 10; ++i) {
            int j = this.randomIntInclusive(-3, 3);
            int k = this.randomIntInclusive(-1, 1);
            int l = this.randomIntInclusive(-3, 3);
            boolean flag = this.maybeTeleportTo(blockpos.m_123341_() + j, blockpos.m_123342_() + k, blockpos.m_123343_() + l);
            if (flag) {
               return;
            }
         }

      }

      private boolean maybeTeleportTo(int pX, int pY, int pZ) {
         if (Math.abs((double)pX - this.owner.m_20185_()) < 2.0D && Math.abs((double)pZ - this.owner.m_20189_()) < 2.0D) {
            return false;
         } else if (!this.canTeleportTo(new BlockPos(pX, pY, pZ))) {
            return false;
         } else {
            this.horse.m_7678_((double)pX + 0.5D, (double)pY, (double)pZ + 0.5D, this.horse.m_146908_(), this.horse.m_146909_());
            this.navigation.m_26573_();
            return true;
         }
      }

      private boolean canTeleportTo(BlockPos pPos) {
         BlockPathTypes blockpathtypes = WalkNodeEvaluator.m_77604_(this.level, pPos.m_122032_());
         if (blockpathtypes != BlockPathTypes.WALKABLE) {
            return false;
         } else {
            BlockState blockstate = this.level.m_8055_(pPos.m_7495_());
            if (!this.canFly && blockstate.m_60734_() instanceof LeavesBlock) {
               return false;
            } else {
               BlockPos blockpos = pPos.m_121996_(this.horse.m_20183_());
               return this.level.m_45756_(this.horse, this.horse.m_20191_().m_82338_(blockpos));
            }
         }
      }

      private int randomIntInclusive(int pMin, int pMax) {
         return this.horse.m_217043_().m_188503_(pMax - pMin + 1) + pMin;
      }
   }

   public static class HorseSitWhenOrderedToGoal extends Goal {
      private final TensuraHorseEntity mob;

      public HorseSitWhenOrderedToGoal(TensuraHorseEntity pMob) {
         this.mob = pMob;
         this.m_7021_(EnumSet.of(Flag.JUMP, Flag.MOVE));
      }

      public boolean m_8045_() {
         return this.mob.isSitting();
      }

      public boolean m_8036_() {
         if (!this.mob.m_30614_()) {
            return false;
         } else if (this.mob.m_20072_()) {
            return false;
         } else if (!this.mob.m_20096_()) {
            return false;
         } else {
            LivingEntity livingentity = this.mob.getOwner();
            if (livingentity == null) {
               return false;
            } else {
               return (!(this.mob.m_20280_(livingentity) < 144.0D) || livingentity.m_21188_() == null) && this.mob.isSitting();
            }
         }
      }

      public void m_8056_() {
         this.mob.m_21573_().m_26573_();
      }
   }
}
