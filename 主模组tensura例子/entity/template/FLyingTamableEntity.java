package com.github.manasmods.tensura.entity.template;

import com.github.manasmods.tensura.api.entity.controller.FlightMoveController;
import com.github.manasmods.tensura.api.entity.navigator.StraightFlightNavigator;
import com.github.manasmods.tensura.api.entity.subclass.IFollower;
import com.github.manasmods.tensura.config.TensuraConfig;
import java.util.EnumSet;
import javax.annotation.Nullable;
import net.minecraft.core.BlockPos;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.syncher.EntityDataAccessor;
import net.minecraft.network.syncher.EntityDataSerializers;
import net.minecraft.network.syncher.SynchedEntityData;
import net.minecraft.util.RandomSource;
import net.minecraft.world.DifficultyInstance;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.MobSpawnType;
import net.minecraft.world.entity.SpawnGroupData;
import net.minecraft.world.entity.TamableAnimal;
import net.minecraft.world.entity.ai.goal.Goal;
import net.minecraft.world.entity.ai.goal.Goal.Flag;
import net.minecraft.world.entity.ai.navigation.GroundPathNavigation;
import net.minecraft.world.entity.ai.util.LandRandomPos;
import net.minecraft.world.entity.animal.FlyingAnimal;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.LevelAccessor;
import net.minecraft.world.level.ServerLevelAccessor;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.pathfinder.BlockPathTypes;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.common.ForgeMod;
import net.minecraftforge.fluids.FluidType;

public class FLyingTamableEntity extends TensuraTamableEntity implements FlyingAnimal, IFollower {
   protected static final EntityDataAccessor<Boolean> FLYING;
   protected boolean wasFlying;
   public int timeFlying = 0;
   public int miscAnimationTicks = 0;

   public FLyingTamableEntity(EntityType<? extends TamableAnimal> type, Level worldIn) {
      super(type, worldIn);
      this.m_21441_(BlockPathTypes.DANGER_FIRE, -1.0F);
      this.m_21441_(BlockPathTypes.WATER, -1.0F);
      this.m_21441_(BlockPathTypes.WATER_BORDER, 16.0F);
      this.m_21441_(BlockPathTypes.FENCE, -1.0F);
      this.switchNavigator(false);
   }

   protected void switchNavigator(boolean onLand) {
      if (!onLand && !this.m_5803_()) {
         this.f_21342_ = new FlightMoveController(this, 0.7F, true);
         this.f_21344_ = new StraightFlightNavigator(this, this.f_19853_);
         this.wasFlying = true;
      } else {
         this.f_21342_ = new TensuraTamableEntity.SleepMoveControl();
         this.f_21344_ = new GroundPathNavigation(this, this.f_19853_);
         this.wasFlying = false;
      }

   }

   protected void m_8097_() {
      super.m_8097_();
      this.f_19804_.m_135372_(FLYING, false);
   }

   public void m_7380_(CompoundTag compound) {
      super.m_7380_(compound);
      compound.m_128379_("Flying", this.m_29443_());
   }

   public void m_7378_(CompoundTag compound) {
      super.m_7378_(compound);
      this.setFlying(compound.m_128471_("Flying"));
   }

   public boolean m_29443_() {
      return (Boolean)this.f_19804_.m_135370_(FLYING);
   }

   public void setFlying(boolean flying) {
      this.f_19804_.m_135381_(FLYING, flying);
   }

   public boolean canTrample(BlockState state, BlockPos pos, float fallDistance) {
      return false;
   }

   public boolean m_142535_(float pFallDistance, float pMultiplier, DamageSource pSource) {
      return false;
   }

   protected void m_7840_(double y, boolean onGroundIn, BlockState state, BlockPos pos) {
   }

   public boolean shouldFollow() {
      return !this.m_21827_() && !this.isWandering() && (this.m_5448_() == null || !this.m_5448_().m_6084_());
   }

   public void m_8119_() {
      super.m_8119_();
      this.handleFlying();
   }

   protected void handleFlying() {
      if (!this.f_19853_.m_5776_()) {
         boolean isFlying = this.m_29443_();
         if (isFlying != this.wasFlying) {
            this.switchNavigator(!isFlying);
         }

         if (isFlying) {
            ++this.timeFlying;
            this.m_20242_(true);
            if (this.m_21827_() || this.m_5803_() || this.m_20159_() || this.m_27593_()) {
               this.setFlying(false);
            }
         } else {
            this.timeFlying = 0;
            this.m_20242_(false);
         }

      }
   }

   protected boolean shouldContinueFlying() {
      return this.timeFlying < 200;
   }

   public void m_7023_(Vec3 vec3d) {
      if (this.m_20069_() && this.m_20184_().f_82480_ > 0.0D && this.isPushedByFluid((FluidType)ForgeMod.WATER_TYPE.get())) {
         this.m_20256_(this.m_20184_().m_82542_(1.0D, 0.5D, 1.0D));
      }

      super.m_7023_(vec3d);
   }

   public void followEntity(TamableAnimal animal, LivingEntity owner, double followSpeed) {
      if (this.m_20270_(owner) > 5.0F) {
         this.setFlying(true);
         this.m_21566_().m_6849_(owner.m_20185_(), owner.m_20186_() + (double)owner.m_20206_(), owner.m_20189_(), followSpeed);
      } else {
         if (this.f_19861_) {
            this.setFlying(false);
         }

         if (this.m_29443_() && !this.isOverWater(this)) {
            BlockPos vec = this.getGround(this, this.m_20183_());
            this.m_21566_().m_6849_((double)vec.m_123341_(), (double)vec.m_123342_(), (double)vec.m_123343_(), followSpeed);
         } else {
            this.m_21573_().m_5624_(owner, followSpeed);
         }
      }

   }

   public static boolean checkFlyingSpawnRules(EntityType<? extends Mob> pType, LevelAccessor pLevel, MobSpawnType pSpawnType, BlockPos pPos, RandomSource pRandom) {
      return true;
   }

   @Nullable
   public SpawnGroupData m_6518_(ServerLevelAccessor pLevel, DifficultyInstance pDifficulty, MobSpawnType pReason, @Nullable SpawnGroupData pSpawnData, @Nullable CompoundTag pDataTag) {
      if (!this.m_20096_()) {
         this.setFlying(true);
      }

      return super.m_6518_(pLevel, pDifficulty, pReason, pSpawnData, pDataTag);
   }

   static {
      FLYING = SynchedEntityData.m_135353_(FLyingTamableEntity.class, EntityDataSerializers.f_135035_);
   }

   public class WalkGoal extends Goal {
      protected final FLyingTamableEntity entity;
      protected double x;
      protected double y;
      protected double z;
      private boolean flightTarget = false;

      public WalkGoal(FLyingTamableEntity entity) {
         this.m_7021_(EnumSet.of(Flag.MOVE));
         this.entity = entity;
      }

      public boolean m_8036_() {
         if (!this.entity.m_20160_() && (this.entity.m_5448_() == null || !this.entity.m_5448_().m_6084_()) && !this.entity.m_20159_() && !this.entity.m_21827_()) {
            if (this.entity.m_217043_().m_188503_(30) != 0 && !this.entity.m_29443_()) {
               return false;
            } else {
               if (this.entity.m_20096_()) {
                  this.flightTarget = FLyingTamableEntity.this.f_19796_.m_188499_();
               } else {
                  this.flightTarget = FLyingTamableEntity.this.f_19796_.m_188503_(5) > 0 && this.entity.shouldContinueFlying();
               }

               Vec3 position = this.getPosition();
               if (position == null) {
                  return false;
               } else {
                  this.x = position.f_82479_;
                  this.y = position.f_82480_;
                  this.z = position.f_82481_;
                  return true;
               }
            }
         } else {
            return false;
         }
      }

      public void m_8037_() {
         if (this.flightTarget) {
            this.entity.m_21566_().m_6849_(this.x, this.y, this.z, 1.0D);
         } else {
            this.entity.m_21573_().m_26519_(this.x, this.y, this.z, 1.0D);
            if (FLyingTamableEntity.this.m_29443_() && this.entity.f_19861_) {
               this.entity.setFlying(false);
            }
         }

         if (FLyingTamableEntity.this.m_29443_() && this.entity.f_19861_ && this.entity.timeFlying > 10) {
            this.entity.setFlying(false);
         }

      }

      @Nullable
      protected Vec3 getPosition() {
         Vec3 vector3d = this.entity.m_20182_();
         if (FLyingTamableEntity.this.isOverWater(this.entity) || vector3d.m_7098_() <= (double)(this.entity.m_9236_().m_141937_() + 10)) {
            this.flightTarget = true;
         }

         Vec3 pos = Vec3.m_82512_(this.entity.getWanderPos());
         double distance = (Double)TensuraConfig.INSTANCE.entitiesConfig.tamedWanderRadius.get();
         if (this.entity.isWandering() && this.entity.m_20238_(pos) >= distance * distance) {
            return pos;
         } else if (this.flightTarget) {
            return this.entity.timeFlying >= 50 && !FLyingTamableEntity.this.isOverWater(this.entity) ? FLyingTamableEntity.this.getBlockGrounding(this.entity, vector3d) : FLyingTamableEntity.this.getBlockInViewAway(this.entity, vector3d, 0.0F);
         } else {
            return LandRandomPos.m_148488_(this.entity, 10, 7);
         }
      }

      public boolean m_8045_() {
         if (this.entity.m_21827_()) {
            return false;
         } else if (this.flightTarget) {
            return this.entity.m_29443_() && this.entity.m_20275_(this.x, this.y, this.z) > 2.0D;
         } else {
            return !this.entity.m_21573_().m_26571_() && !this.entity.m_20160_();
         }
      }

      public void m_8056_() {
         if (this.flightTarget) {
            this.entity.setFlying(true);
            this.entity.m_21566_().m_6849_(this.x, this.y, this.z, 1.0D);
         } else {
            this.entity.m_21573_().m_26519_(this.x, this.y, this.z, 1.0D);
         }

      }

      public void m_8041_() {
         this.entity.m_21573_().m_26573_();
         super.m_8041_();
      }
   }
}
