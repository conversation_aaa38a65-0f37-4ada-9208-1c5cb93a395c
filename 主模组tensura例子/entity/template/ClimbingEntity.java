package com.github.manasmods.tensura.entity.template;

import com.github.manasmods.tensura.api.entity.navigator.ClimbingNavigator;
import com.github.manasmods.tensura.entity.magic.barrier.BarrierPart;
import net.minecraft.network.syncher.EntityDataAccessor;
import net.minecraft.network.syncher.EntityDataSerializers;
import net.minecraft.network.syncher.SynchedEntityData;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.ai.navigation.PathNavigation;
import net.minecraft.world.level.Level;

public class ClimbingEntity extends TensuraTamableEntity {
   private static final EntityDataAccessor<Byte> FLAG;

   protected ClimbingEntity(EntityType<? extends ClimbingEntity> entityType, Level level) {
      super(entityType, level);
   }

   protected void m_8097_() {
      super.m_8097_();
      this.f_19804_.m_135372_(FLAG, (byte)0);
   }

   protected PathNavigation m_6037_(Level pLevel) {
      return new ClimbingNavigator(this, pLevel);
   }

   public void m_8119_() {
      super.m_8119_();
      if (!this.f_19853_.f_46443_) {
         this.setBesideClimbableBlock(collidingWall(this));
      }

      if (collidingWall(this) && this.m_6147_()) {
         this.m_20334_(this.m_20184_().f_82479_, this.m_20184_().f_82480_ * (double)this.getClimbSpeedMultiplier(), this.m_20184_().f_82481_);
      }

   }

   public static boolean collidingWall(Entity entity) {
      return !entity.f_19862_ ? false : entity.f_19853_.m_45976_(BarrierPart.class, entity.m_20191_().m_82400_(0.5D)).isEmpty();
   }

   public boolean m_6147_() {
      return this.isBesideClimbableBlock();
   }

   public boolean isBesideClimbableBlock() {
      return ((Byte)this.f_19804_.m_135370_(FLAG) & 1) != 0;
   }

   public void setBesideClimbableBlock(boolean pClimbing) {
      byte flag = (Byte)this.f_19804_.m_135370_(FLAG);
      if (pClimbing) {
         flag = (byte)(flag | 1);
      } else {
         flag &= -2;
      }

      this.f_19804_.m_135381_(FLAG, flag);
   }

   protected float m_6118_() {
      return 0.0F;
   }

   protected float getClimbSpeedMultiplier() {
      return 1.0F;
   }

   static {
      FLAG = SynchedEntityData.m_135353_(ClimbingEntity.class, EntityDataSerializers.f_135027_);
   }
}
