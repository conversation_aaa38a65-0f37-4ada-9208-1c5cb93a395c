package com.github.manasmods.tensura.entity.template;

import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.items.TensuraMaterialItems;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.syncher.EntityDataAccessor;
import net.minecraft.network.syncher.EntityDataSerializers;
import net.minecraft.network.syncher.SynchedEntityData;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.vehicle.Boat;
import net.minecraft.world.item.Item;
import net.minecraft.world.level.Level;

public class TensuraBoatEntity extends Boat {
   private static final EntityDataAccessor<Integer> DATA_ID_TYPE;

   public TensuraBoatEntity(EntityType<? extends TensuraBoatEntity> entityType, Level level) {
      super(entityType, level);
      this.f_19850_ = true;
   }

   public TensuraBoatEntity(Level worldIn, double x, double y, double z) {
      this((EntityType)TensuraEntityTypes.BOAT_ENTITY.get(), worldIn);
      this.m_6034_(x, y, z);
      this.f_19854_ = x;
      this.f_19855_ = y;
      this.f_19856_ = z;
   }

   protected void m_7380_(CompoundTag compound) {
      compound.m_128359_("Type", this.getTensuraBoatType().getName());
   }

   protected void m_7378_(CompoundTag compound) {
      if (compound.m_128425_("Type", 8)) {
         this.setBoatType(TensuraBoatEntity.Type.byName(compound.m_128461_("Type")));
      }

   }

   protected void m_8097_() {
      super.m_8097_();
      this.f_19804_.m_135372_(DATA_ID_TYPE, 0);
   }

   public Item m_38369_() {
      Item var10000;
      switch(this.getTensuraBoatType()) {
      case PALM:
         var10000 = (Item)TensuraMaterialItems.PALM_BOAT.get();
         break;
      case SAKURA:
         var10000 = (Item)TensuraMaterialItems.SAKURA_BOAT.get();
         break;
      default:
         throw new IncompatibleClassChangeError();
      }

      return var10000;
   }

   public void setBoatType(TensuraBoatEntity.Type boatType) {
      this.f_19804_.m_135381_(DATA_ID_TYPE, boatType.ordinal());
   }

   public TensuraBoatEntity.Type getTensuraBoatType() {
      return TensuraBoatEntity.Type.byId((Integer)this.f_19804_.m_135370_(DATA_ID_TYPE));
   }

   static {
      DATA_ID_TYPE = SynchedEntityData.m_135353_(TensuraBoatEntity.class, EntityDataSerializers.f_135028_);
   }

   public static enum Type {
      SAKURA("sakura"),
      PALM("palm");

      private final String name;

      private Type(String name) {
         this.name = name;
      }

      public String getName() {
         return this.name;
      }

      public static TensuraBoatEntity.Type byId(int idType) {
         TensuraBoatEntity.Type[] types = values();
         if (idType < 0 || idType >= types.length) {
            idType = 0;
         }

         return types[idType];
      }

      public static TensuraBoatEntity.Type byName(String nameIn) {
         TensuraBoatEntity.Type[] types = values();
         TensuraBoatEntity.Type[] var2 = types;
         int var3 = types.length;

         for(int var4 = 0; var4 < var3; ++var4) {
            TensuraBoatEntity.Type type = var2[var4];
            if (type.getName().equals(nameIn)) {
               return type;
            }
         }

         return types[0];
      }

      // $FF: synthetic method
      private static TensuraBoatEntity.Type[] $values() {
         return new TensuraBoatEntity.Type[]{SAKURA, PALM};
      }
   }
}
