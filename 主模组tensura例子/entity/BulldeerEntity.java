package com.github.manasmods.tensura.entity;

import com.github.manasmods.tensura.api.entity.ai.TamableFollowParentGoal;
import com.github.manasmods.tensura.api.entity.ai.WanderingFollowOwnerGoal;
import com.github.manasmods.tensura.config.SpawnRateConfig;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.entity.template.TensuraTamableEntity;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.items.TensuraConsumableItems;
import java.util.UUID;
import net.minecraft.core.BlockPos;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.syncher.EntityDataAccessor;
import net.minecraft.network.syncher.EntityDataSerializers;
import net.minecraft.network.syncher.SynchedEntityData;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.InteractionResult;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.AgeableMob;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.MobSpawnType;
import net.minecraft.world.entity.ai.attributes.AttributeSupplier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.goal.BreedGoal;
import net.minecraft.world.entity.ai.goal.EatBlockGoal;
import net.minecraft.world.entity.ai.goal.FloatGoal;
import net.minecraft.world.entity.ai.goal.LookAtPlayerGoal;
import net.minecraft.world.entity.ai.goal.PanicGoal;
import net.minecraft.world.entity.ai.goal.RandomLookAroundGoal;
import net.minecraft.world.entity.ai.goal.SitWhenOrderedToGoal;
import net.minecraft.world.entity.ai.goal.TemptGoal;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.ItemUtils;
import net.minecraft.world.item.Items;
import net.minecraft.world.item.crafting.Ingredient;
import net.minecraft.world.level.ItemLike;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.LevelAccessor;
import net.minecraft.world.level.block.state.BlockState;
import software.bernie.geckolib3.core.IAnimatable;
import software.bernie.geckolib3.core.PlayState;
import software.bernie.geckolib3.core.builder.AnimationBuilder;
import software.bernie.geckolib3.core.builder.ILoopType.EDefaultLoopTypes;
import software.bernie.geckolib3.core.controller.AnimationController;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.manager.AnimationData;
import software.bernie.geckolib3.core.manager.AnimationFactory;
import software.bernie.geckolib3.util.GeckoLibUtil;

public class BulldeerEntity extends TensuraTamableEntity implements IAnimatable {
   private static final EntityDataAccessor<Integer> MISC_ANIMATION;
   public int itchingCountDown = 0;
   public int miscAnimationTicks = 0;
   private final AnimationFactory factory = GeckoLibUtil.createFactory(this);

   public BulldeerEntity(EntityType<? extends BulldeerEntity> pEntityType, Level pLevel) {
      super(pEntityType, pLevel);
      this.f_21364_ = 10;
   }

   protected void m_8099_() {
      this.f_21345_.m_25352_(0, new FloatGoal(this));
      this.f_21345_.m_25352_(1, new SitWhenOrderedToGoal(this));
      this.f_21345_.m_25352_(2, new PanicGoal(this, 2.0D));
      this.f_21345_.m_25352_(3, new EatBlockGoal(this));
      this.f_21345_.m_25352_(3, new WanderingFollowOwnerGoal(this, 1.2D, 20.0F, 5.0F, false));
      this.f_21345_.m_25352_(4, new BreedGoal(this, 1.0D));
      this.f_21345_.m_25352_(5, new TemptGoal(this, 1.25D, Ingredient.m_43929_(new ItemLike[]{Items.f_42405_}), false));
      this.f_21345_.m_25352_(6, new TamableFollowParentGoal(this, 1.0D));
      this.f_21345_.m_25352_(7, new TensuraTamableEntity.WanderAroundPosGoal(this));
      this.f_21345_.m_25352_(8, new LookAtPlayerGoal(this, Player.class, 6.0F));
      this.f_21345_.m_25352_(9, new RandomLookAroundGoal(this));
   }

   public static AttributeSupplier setAttributes() {
      return Mob.m_21552_().m_22268_(Attributes.f_22276_, 30.0D).m_22268_(Attributes.f_22281_, 5.0D).m_22268_(Attributes.f_22279_, 0.25D).m_22268_(Attributes.f_22278_, 0.009999999776482582D).m_22268_(Attributes.f_22277_, 16.0D).m_22265_();
   }

   protected void m_8097_() {
      super.m_8097_();
      this.f_19804_.m_135372_(MISC_ANIMATION, 0);
   }

   public void m_7380_(CompoundTag compound) {
      super.m_7380_(compound);
      compound.m_128405_("MiscAnimation", this.getMiscAnimation());
   }

   public void m_7378_(CompoundTag compound) {
      super.m_7378_(compound);
      this.f_19804_.m_135381_(MISC_ANIMATION, compound.m_128451_("MiscAnimation"));
   }

   public int getMiscAnimation() {
      return (Integer)this.f_19804_.m_135370_(MISC_ANIMATION);
   }

   public void setMiscAnimation(int animation) {
      if (this.getMiscAnimation() == 0 || animation == 0) {
         this.f_19804_.m_135381_(MISC_ANIMATION, animation);
      }
   }

   public void m_8119_() {
      super.m_8119_();
      if (this.getMiscAnimation() != 0) {
         ++this.miscAnimationTicks;
         if (this.miscAnimationTicks >= 30 && this.getMiscAnimation() == 1 || this.miscAnimationTicks >= 45 && this.getMiscAnimation() == 2) {
            this.setMiscAnimation(0);
            this.miscAnimationTicks = 0;
         }
      }

      if (!this.f_19853_.f_46443_) {
         if (this.itchingCountDown++ > 300 && this.m_217043_().m_188503_(10) <= 2) {
            this.itchingCountDown = 0;
            this.setMiscAnimation(2);
         }

      }
   }

   public InteractionResult m_6071_(Player pPlayer, InteractionHand pHand) {
      ItemStack itemstack = pPlayer.m_21120_(pHand);
      if (itemstack.m_150930_(Items.f_42446_)) {
         pPlayer.m_5496_(SoundEvents.f_11833_, 1.0F, 1.0F);
         ItemStack milk = ItemUtils.m_41813_(itemstack, pPlayer, ((Item)TensuraConsumableItems.BUCKET_OF_BULLDEER_MILK.get()).m_7968_());
         pPlayer.m_21008_(pHand, milk);
         return InteractionResult.m_19078_(this.f_19853_.f_46443_);
      } else {
         return super.m_6071_(pPlayer, pHand);
      }
   }

   public AgeableMob m_142606_(ServerLevel pLevel, AgeableMob pOtherParent) {
      BulldeerEntity entity = (BulldeerEntity)((EntityType)TensuraEntityTypes.BULLDEER.get()).m_20615_(pLevel);
      if (entity == null) {
         return null;
      } else {
         UUID uuid = this.m_21805_();
         if (uuid != null) {
            entity.m_21816_(uuid);
            entity.m_7105_(true);
         }

         return entity;
      }
   }

   public void m_8035_() {
      super.m_8035_();
      this.setMiscAnimation(1);
   }

   public boolean m_6898_(ItemStack pStack) {
      return pStack.m_204117_(TensuraTags.Items.BULLDEER_FOOD);
   }

   public boolean m_5545_(LevelAccessor pLevel, MobSpawnType pSpawnReason) {
      return SpawnRateConfig.rollSpawn((Integer)SpawnRateConfig.INSTANCE.bulldeerSpawnRate.get(), this.m_217043_(), pSpawnReason) && super.m_5545_(pLevel, pSpawnReason);
   }

   protected SoundEvent m_7515_() {
      return SoundEvents.f_11830_;
   }

   protected SoundEvent m_7975_(DamageSource pDamageSource) {
      return SoundEvents.f_11832_;
   }

   protected SoundEvent m_5592_() {
      return SoundEvents.f_11831_;
   }

   protected void m_7355_(BlockPos pPos, BlockState pBlock) {
      this.m_5496_(SoundEvents.f_11834_, 0.15F, 1.0F);
   }

   protected float m_6121_() {
      return 0.4F;
   }

   private <E extends IAnimatable> PlayState predicate(AnimationEvent<E> event) {
      if (event.isMoving()) {
         if (this.m_21188_() == null && !this.m_203117_() && !this.m_6060_()) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.bulldeer.walk", EDefaultLoopTypes.LOOP));
         } else {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.bulldeer.run", EDefaultLoopTypes.LOOP));
         }
      } else {
         event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.bulldeer.idle", EDefaultLoopTypes.LOOP));
      }

      return PlayState.CONTINUE;
   }

   private <E extends IAnimatable> PlayState playOncePredicate(AnimationEvent<E> event) {
      if (this.getMiscAnimation() == 1) {
         event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.bulldeer.eat", EDefaultLoopTypes.PLAY_ONCE));
      } else if (!event.isMoving() && this.getMiscAnimation() == 2) {
         event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.bulldeer.look_around", EDefaultLoopTypes.PLAY_ONCE));
      }

      return PlayState.CONTINUE;
   }

   public void registerControllers(AnimationData data) {
      data.addAnimationController(new AnimationController(this, "controller", 0.0F, this::predicate));
      data.addAnimationController(new AnimationController(this, "playOnceController", 0.0F, this::playOncePredicate));
   }

   public AnimationFactory getFactory() {
      return this.factory;
   }

   static {
      MISC_ANIMATION = SynchedEntityData.m_135353_(BulldeerEntity.class, EntityDataSerializers.f_135028_);
   }
}
