package com.github.manasmods.tensura.entity;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.magic.MagicElemental;
import com.github.manasmods.tensura.ability.magic.spiritual.SpiritualMagic;
import com.github.manasmods.tensura.api.entity.ai.DynamicMeleeAttackGoal;
import com.github.manasmods.tensura.api.entity.ai.FlyingFollowOwnerGoal;
import com.github.manasmods.tensura.api.entity.subclass.DynamicMeleeAttackAction;
import com.github.manasmods.tensura.api.entity.subclass.IElementalSpirit;
import com.github.manasmods.tensura.api.entity.subclass.ITeleportation;
import com.github.manasmods.tensura.api.entity.subclass.ITensuraMount;
import com.github.manasmods.tensura.client.keybind.TensuraKeybinds;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.config.SpawnRateConfig;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.entity.magic.projectile.SpaceCutProjectile;
import com.github.manasmods.tensura.entity.template.FLyingTamableEntity;
import com.github.manasmods.tensura.entity.template.TensuraTamableEntity;
import com.github.manasmods.tensura.item.food.HealingPotionItem;
import com.github.manasmods.tensura.registry.items.TensuraMaterialItems;
import com.github.manasmods.tensura.registry.items.TensuraMobDropItems;
import com.github.manasmods.tensura.registry.skill.ExtraSkills;
import com.github.manasmods.tensura.registry.sound.TensuraSoundEvents;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.mojang.math.Vector3f;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import javax.annotation.Nullable;
import net.minecraft.commands.arguments.EntityAnchorArgument.Anchor;
import net.minecraft.core.BlockPos;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.syncher.EntityDataAccessor;
import net.minecraft.network.syncher.EntityDataSerializers;
import net.minecraft.network.syncher.SynchedEntityData;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.util.Mth;
import net.minecraft.util.RandomSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.InteractionResult;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.AgeableMob;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.MobSpawnType;
import net.minecraft.world.entity.PlayerRideable;
import net.minecraft.world.entity.ai.attributes.AttributeSupplier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.goal.FloatGoal;
import net.minecraft.world.entity.ai.goal.LookAtPlayerGoal;
import net.minecraft.world.entity.ai.goal.RandomLookAroundGoal;
import net.minecraft.world.entity.ai.goal.SitWhenOrderedToGoal;
import net.minecraft.world.entity.ai.goal.target.NearestAttackableTargetGoal;
import net.minecraft.world.entity.ai.goal.target.ResetUniversalAngerTargetGoal;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.level.ItemLike;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.LevelAccessor;
import net.minecraft.world.phys.AABB;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.event.ForgeEventFactory;
import software.bernie.geckolib3.core.AnimationState;
import software.bernie.geckolib3.core.IAnimatable;
import software.bernie.geckolib3.core.PlayState;
import software.bernie.geckolib3.core.builder.AnimationBuilder;
import software.bernie.geckolib3.core.builder.ILoopType.EDefaultLoopTypes;
import software.bernie.geckolib3.core.controller.AnimationController;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.manager.AnimationData;
import software.bernie.geckolib3.core.manager.AnimationFactory;
import software.bernie.geckolib3.util.GeckoLibUtil;

public class WingedCatEntity extends FLyingTamableEntity implements IAnimatable, IElementalSpirit, ITensuraMount, PlayerRideable, ITeleportation {
   private static final EntityDataAccessor<Integer> MISC_ANIMATION;
   private static final EntityDataAccessor<Integer> SUMMONING_TICK;
   protected static final EntityDataAccessor<Optional<UUID>> SUMMONER_UUID;
   private final AnimationFactory factory = GeckoLibUtil.createFactory(this);
   public int miscAnimationTicks = 0;

   public WingedCatEntity(EntityType<? extends WingedCatEntity> type, Level level) {
      super(type, level);
      this.f_21364_ = 40;
      this.f_19793_ = 1.0F;
   }

   public static AttributeSupplier setAttributes() {
      return Mob.m_21552_().m_22268_(Attributes.f_22276_, 50.0D).m_22268_(Attributes.f_22281_, 20.0D).m_22268_(Attributes.f_22284_, 5.0D).m_22268_(Attributes.f_22277_, 32.0D).m_22268_(Attributes.f_22279_, 0.30000001192092896D).m_22268_(Attributes.f_22280_, 0.30000001192092896D).m_22268_(Attributes.f_22278_, 0.2D).m_22265_();
   }

   protected void m_8099_() {
      this.f_21345_.m_25352_(0, new FloatGoal(this));
      this.f_21345_.m_25352_(1, new SitWhenOrderedToGoal(this));
      this.f_21345_.m_25352_(2, new WingedCatEntity.CatAttackGoal(this));
      this.f_21345_.m_25352_(3, new FlyingFollowOwnerGoal(this, 0.7D, 10.0F, 4.0F, true, false));
      this.f_21345_.m_25352_(6, new IElementalSpirit.FollowGreaterSpiritGoal(this, 1.0D, AkashEntity.class));
      this.f_21345_.m_25352_(7, new FLyingTamableEntity.WalkGoal(this));
      this.f_21345_.m_25352_(8, new TensuraTamableEntity.FlyingWanderAroundPosGoal(this));
      this.f_21345_.m_25352_(9, new LookAtPlayerGoal(this, Player.class, 6.0F));
      this.f_21345_.m_25352_(10, new RandomLookAroundGoal(this));
      this.f_21346_.m_25352_(2, new TensuraTamableEntity.TensuraOwnerHurtByTargetGoal(this));
      this.f_21346_.m_25352_(3, new TensuraTamableEntity.TensuraOwnerHurtTargetGoal(this));
      this.f_21346_.m_25352_(5, (new TensuraTamableEntity.TensuraHurtByTargetGoal(this)).m_26044_(new Class[0]));
      this.f_21346_.m_25352_(4, new NearestAttackableTargetGoal(this, Player.class, 10, true, false, this::m_21674_));
      this.f_21346_.m_25352_(8, new ResetUniversalAngerTargetGoal(this, true));
   }

   protected void m_8097_() {
      super.m_8097_();
      this.f_19804_.m_135372_(MISC_ANIMATION, 0);
      this.f_19804_.m_135372_(SUMMONING_TICK, -1);
      this.f_19804_.m_135372_(SUMMONER_UUID, Optional.empty());
   }

   public void m_7380_(CompoundTag compound) {
      super.m_7380_(compound);
      if (this.getSummonerUUID() != null) {
         compound.m_128362_("Summoner", this.getSummonerUUID());
      }

      compound.m_128405_("MiscAnimation", this.getMiscAnimation());
      compound.m_128405_("SummoningTick", this.getSummoningTick());
   }

   public void m_7378_(CompoundTag compound) {
      super.m_7378_(compound);
      if (compound.m_128403_("Summoner")) {
         this.setSummonerUUID(compound.m_128342_("Summoner"));
      }

      this.f_19804_.m_135381_(MISC_ANIMATION, compound.m_128451_("MiscAnimation"));
      this.setSummoningTick(compound.m_128451_("SummoningTick"));
   }

   public int getMiscAnimation() {
      return (Integer)this.f_19804_.m_135370_(MISC_ANIMATION);
   }

   public void setMiscAnimation(int animation) {
      if (this.getMiscAnimation() == 0 || animation == 0) {
         this.f_19804_.m_135381_(MISC_ANIMATION, animation);
      }
   }

   public int getSummoningTick() {
      return (Integer)this.f_19804_.m_135370_(SUMMONING_TICK);
   }

   public void setSummoningTick(int tick) {
      this.f_19804_.m_135381_(SUMMONING_TICK, tick);
   }

   @Nullable
   public UUID getSummonerUUID() {
      return (UUID)((Optional)this.f_19804_.m_135370_(SUMMONER_UUID)).orElse((Object)null);
   }

   public void setSummonerUUID(@Nullable UUID pUuid) {
      this.f_19804_.m_135381_(SUMMONER_UUID, Optional.ofNullable(pUuid));
   }

   @Nullable
   public LivingEntity m_21826_() {
      return this.getSummonerUUID() != null ? null : super.m_21826_();
   }

   public boolean m_6469_(DamageSource pSource, float pAmount) {
      if (this.m_6673_(pSource)) {
         return false;
      } else {
         if (!this.m_21824_()) {
            Entity var4 = pSource.m_7639_();
            if (var4 instanceof WingedCatEntity) {
               WingedCatEntity cat = (WingedCatEntity)var4;
               if (!cat.m_21824_()) {
                  return false;
               }
            }

            var4 = pSource.m_7639_();
            if (var4 instanceof AkashEntity) {
               AkashEntity akash = (AkashEntity)var4;
               if (!akash.m_21824_()) {
                  return false;
               }
            }
         }

         pAmount *= this.getPhysicalAttackInput(pSource);
         return super.m_6469_(pSource, pAmount);
      }
   }

   protected boolean m_8028_() {
      return false;
   }

   public boolean canSleep() {
      return !this.m_21525_();
   }

   public boolean shouldCountMotionBlock() {
      return false;
   }

   public boolean m_7307_(Entity entity) {
      if (super.m_7307_(entity)) {
         return true;
      } else if (this.getSummonerUUID() != null) {
         if (entity instanceof IElementalSpirit) {
            IElementalSpirit spirit = (IElementalSpirit)entity;
            return Objects.equals(spirit.getSummonerUUID(), this.getSummonerUUID());
         } else {
            return Objects.equals(entity.m_20148_(), this.getSummonerUUID());
         }
      } else if (entity instanceof WingedCatEntity) {
         WingedCatEntity cat = (WingedCatEntity)entity;
         return cat.m_21824_() == this.m_21824_();
      } else if (entity instanceof AkashEntity) {
         AkashEntity akash = (AkashEntity)entity;
         return akash.m_21824_() == this.m_21824_();
      } else {
         return false;
      }
   }

   public boolean m_6779_(LivingEntity pTarget) {
      return this.m_7307_(pTarget) ? false : super.m_6779_(pTarget);
   }

   @Nullable
   public AgeableMob m_142606_(ServerLevel pLevel, AgeableMob pOtherParent) {
      return null;
   }

   public boolean m_5545_(LevelAccessor pLevel, MobSpawnType pSpawnReason) {
      int rate = (Integer)SpawnRateConfig.INSTANCE.wingedCatSpawnRate.get();
      if (this.m_9236_().m_46472_().equals(Level.f_46430_)) {
         rate *= 2;
      }

      return SpawnRateConfig.rollSpawn(rate, this.m_217043_(), pSpawnReason);
   }

   public static boolean checkWingedCatSpawnRules(EntityType<? extends WingedCatEntity> pType, LevelAccessor pLevel, MobSpawnType pSpawnType, BlockPos pPos, RandomSource pRandom) {
      if (pSpawnType == MobSpawnType.SPAWNER) {
         return true;
      } else {
         return pPos.m_123342_() >= 70;
      }
   }

   public void m_8119_() {
      super.m_8119_();
      LivingEntity target;
      if (this.getMiscAnimation() != 0) {
         ++this.miscAnimationTicks;
         if (!this.m_6084_()) {
            return;
         }

         if (this.getMiscAnimation() == 2 && this.miscAnimationTicks == 10) {
            target = this.m_5448_();
            if (target != null) {
               this.m_7618_(Anchor.EYES, target.m_146892_());
            }

            this.spaceCutAttack(0.1D, false);
            this.spaceCutAttack(0.6D, false);
            this.spaceCutAttack(0.6D, true);
            this.f_19853_.m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_12317_, SoundSource.NEUTRAL, 1.0F, 1.0F);
         } else if (this.getMiscAnimation() == 3 && this.miscAnimationTicks == 10) {
            this.doubleStrike();
            TensuraParticleHelper.addServerParticlesAroundSelf(this, ParticleTypes.f_123789_, 2.0D);
            this.m_9236_().m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_12317_, SoundSource.NEUTRAL, 1.5F, 1.0F);
         }

         if (this.miscAnimationTicks >= this.getAnimationTick(this.getMiscAnimation())) {
            this.setMiscAnimation(0);
            this.miscAnimationTicks = 0;
         }
      }

      if (!this.f_19853_.m_5776_()) {
         this.summoningTicking(this);
         target = this.m_5448_();
         if (target != null) {
            if (this.m_6084_()) {
               if (target.m_6084_() && this.m_20280_(target) > 3.0D) {
                  this.setFlying(true);
               }

               if (this.f_19797_ % 20 == 0) {
                  if (target.m_20270_(this) >= 20.0F) {
                     this.teleportTowards(this, target, 10.0D);
                  }

                  if (this.f_19797_ % 200 == 0) {
                     if (this.m_217043_().m_188503_(10) == 1) {
                        this.teleportTowards(this, target, 10.0D);
                     }

                  }
               }
            }
         }
      }
   }

   private int getAnimationTick(int miscAnimation) {
      return miscAnimation == 1 ? 30 : 20;
   }

   public void spaceCutAttack(double multishot, boolean negativeAngle) {
      SpaceCutProjectile spaceCut = new SpaceCutProjectile(this.f_19853_, this);
      spaceCut.setInvis(false);
      spaceCut.setSkill(SkillUtils.getSkillOrNull(this, (ManasSkill)ExtraSkills.SPATIAL_MANIPULATION.get()));
      float radius = 2.0F;
      float angle = 0.017453292F * this.f_20883_;
      double extraX = (double)(radius * Mth.m_14031_((float)(3.141592653589793D + (double)angle)));
      double extraZ = (double)(radius * Mth.m_14089_(angle));
      spaceCut.m_6034_(this.m_20185_() + extraX, this.m_20188_() - 0.5D, this.m_20189_() + extraZ);
      if (multishot > 0.0D) {
         int rot = negativeAngle ? -145 : 145;
         float yaw = this.m_146908_() * 0.017453292F;
         float f3 = Mth.m_14031_((float)((double)yaw + Math.toRadians((double)rot)));
         float f = -Mth.m_14089_((float)((double)yaw + Math.toRadians((double)rot)));
         spaceCut.m_6034_(spaceCut.m_20185_() + (double)f3 * multishot, spaceCut.m_20186_(), spaceCut.m_20189_() + (double)f * multishot);
      }

      spaceCut.setDamage((float)this.m_21133_(Attributes.f_22281_));
      spaceCut.m_20242_(true);
      spaceCut.setSpiritAttack(true);
      Vector3f vector3f = new Vector3f(this.m_146895_() != null ? this.m_146895_().m_20252_(2.0F) : this.m_20252_(2.0F));
      spaceCut.m_6686_((double)vector3f.m_122239_(), (double)vector3f.m_122260_(), (double)vector3f.m_122269_(), 1.0F, 0.0F);
      this.f_19853_.m_7967_(spaceCut);
   }

   public void doubleStrike() {
      AABB aabb = this.m_20191_().m_82400_(3.0D);
      List<LivingEntity> livingEntityList = this.f_19853_.m_6443_(LivingEntity.class, aabb, (entity) -> {
         return !entity.m_7307_(this) && entity != this.m_21826_() && entity != this && (!(entity instanceof WingedCatEntity) || entity == this.m_5448_());
      });
      if (!livingEntityList.isEmpty()) {
         float damageMultiplier = this.hasSpaceManipulation() ? 1.5F : 1.0F;
         DamageSource damageSource = DamageSourceHelper.addSkillAndCost(DamageSource.m_19370_(this).m_19389_(), 20.0D, SkillUtils.getSkillOrNull(this, (ManasSkill)ExtraSkills.SPATIAL_MANIPULATION.get())).setSpatial();
         Iterator var5 = livingEntityList.iterator();

         while(var5.hasNext()) {
            LivingEntity target = (LivingEntity)var5.next();
            target.m_6469_(damageSource, (float)this.m_21133_(Attributes.f_22281_) * damageMultiplier);
            SkillHelper.knockBack(this, target, 0.5F);
         }

      }
   }

   public boolean hasSpaceManipulation() {
      return SkillAPI.getSkillsFrom(this).getSkill((ManasSkill)ExtraSkills.SPATIAL_MANIPULATION.get()).isPresent();
   }

   public void mountAbility(Player rider) {
      if (this.getMiscAnimation() != 3) {
         this.setMiscAnimation(this.m_20096_() ? 3 : 2);
      }
   }

   public boolean m_6898_(ItemStack pStack) {
      return pStack.m_204117_(TensuraTags.Items.SPIRIT_FOOD);
   }

   public boolean isTamingFood(ItemStack pStack) {
      return pStack.m_150930_((Item)TensuraMaterialItems.ELEMENT_CORE_SPACE.get());
   }

   public MagicElemental getElemental() {
      return MagicElemental.SPACE;
   }

   public SpiritualMagic.SpiritLevel getSpiritLevel() {
      return SpiritualMagic.SpiritLevel.MEDIUM;
   }

   public InteractionResult m_6071_(Player player, InteractionHand hand) {
      ItemStack itemstack = player.m_21120_(hand);
      if (itemstack.m_41720_() instanceof HealingPotionItem) {
         return super.m_6071_(player, hand);
      } else {
         InteractionResult eating = this.handleEating(player, hand, itemstack);
         if (eating.m_19077_()) {
            return eating;
         } else if (this.f_19853_.f_46443_) {
            boolean flag = this.m_21830_(player) || this.m_21824_() || this.isTamingFood(itemstack);
            return flag ? InteractionResult.CONSUME : InteractionResult.PASS;
         } else {
            if (this.m_21824_()) {
               if (this.m_21830_(player)) {
                  if (player.m_36341_()) {
                     this.commanding(player);
                  } else if (!this.convertElementalCore(this, player, hand, (Item)TensuraMaterialItems.ELEMENT_CORE_SPACE.get()) && player.m_146895_() == null) {
                     this.m_21839_(false);
                     this.setWandering(false);
                     player.m_7998_(this, false);
                  }

                  return InteractionResult.m_19078_(this.f_19853_.f_46443_);
               }
            } else if (this.isTamingFood(itemstack)) {
               if (!player.m_7500_()) {
                  itemstack.m_41774_(1);
               }

               if (this.f_19796_.m_188503_(10) == 7 && !ForgeEventFactory.onAnimalTame(this, player)) {
                  this.m_21828_(player);
                  this.f_21344_.m_26573_();
                  this.m_6710_((LivingEntity)null);
                  this.m_21839_(true);
                  this.f_19853_.m_7605_(this, (byte)7);
               } else {
                  if (this.f_19796_.m_188503_(20) == 0) {
                     this.m_6710_(player);
                  }

                  this.f_19853_.m_7605_(this, (byte)6);
               }

               return InteractionResult.SUCCESS;
            }

            return InteractionResult.PASS;
         }
      }
   }

   public InteractionResult handleEating(Player player, InteractionHand hand, ItemStack itemstack) {
      if (this.m_6898_(itemstack) && this.m_21223_() < this.m_21233_()) {
         if (!player.m_7500_()) {
            itemstack.m_41774_(1);
         }

         this.m_8035_();
         this.m_9236_().m_6269_((Player)null, this, (SoundEvent)TensuraSoundEvents.EATING.get(), SoundSource.NEUTRAL, 1.0F, 1.0F);
         return InteractionResult.SUCCESS;
      } else {
         return InteractionResult.PASS;
      }
   }

   public void m_8035_() {
      super.m_8035_();
      this.m_5634_(3.0F);
      this.setMiscAnimation(1);
   }

   protected void m_6668_(DamageSource pDamageSource) {
      if (this.getSummoningTick() >= 0) {
         this.m_5907_();
      } else {
         super.m_6668_(pDamageSource);
      }

   }

   protected void m_7472_(DamageSource pSource, int pLooting, boolean pRecentlyHit) {
      super.m_7472_(pSource, pLooting, pRecentlyHit);
      if (!((double)this.f_19796_.m_188501_() > 0.1D)) {
         this.m_19998_((ItemLike)TensuraMobDropItems.ELEMENTAL_ESSENCE.get());
      }
   }

   public boolean m_20068_() {
      if (super.m_20068_()) {
         return true;
      } else {
         return this.getControllingPassenger() != null && !this.m_20096_();
      }
   }

   public boolean m_6146_() {
      return true;
   }

   @Nullable
   public LivingEntity getControllingPassenger() {
      Iterator var1 = this.m_20197_().iterator();

      while(var1.hasNext()) {
         Entity passenger = (Entity)var1.next();
         if (passenger instanceof Player) {
            Player player = (Player)passenger;
            if (player.equals(this.m_21826_())) {
               return player;
            }
         }
      }

      return null;
   }

   public void m_7332_(Entity passenger) {
      if (this.m_20363_(passenger)) {
         passenger.m_183634_();
         float radius = -0.25F;
         float angle = 0.017453292F * this.f_20883_;
         double extraX = (double)(radius * Mth.m_14031_((float)(3.141592653589793D + (double)angle)));
         double extraZ = (double)(radius * Mth.m_14089_(angle));
         double yOffset = this.m_20186_() + this.m_6048_() + passenger.m_6049_();
         passenger.m_6034_(this.m_20185_() + extraX, yOffset, this.m_20189_() + extraZ);
      }
   }

   public void m_7023_(Vec3 pTravelVector) {
      if (this.m_6084_()) {
         LivingEntity controller = this.getControllingPassenger();
         if (this.m_20160_() && controller != null) {
            this.m_146922_(controller.m_146908_());
            this.f_19859_ = this.m_146908_();
            this.m_146926_(controller.m_146909_() * 0.5F);
            this.m_19915_(this.m_146908_(), this.m_146909_());
            this.f_20883_ = this.m_146908_();
            this.f_20885_ = this.f_20883_;
            float f = controller.f_20900_ * 0.5F;
            float f1 = controller.f_20902_;
            if (f1 <= 0.0F) {
               f1 *= 0.25F;
            }

            this.f_20887_ = this.m_6113_() * 0.5F;
            if (this.m_6109_()) {
               float speed = (float)this.m_21133_(Attributes.f_22279_) / 2.0F;
               if (controller.m_20142_()) {
                  speed = (float)((double)speed * 1.5D);
               }

               this.m_7910_(speed);
               if (controller.f_20899_) {
                  this.m_20256_(this.m_20184_().m_82520_(0.0D, 0.1D, 0.0D));
               } else if (TensuraKeybinds.MOUNT_DESCENDING.m_90857_()) {
                  this.descending(this, controller);
               }

               super.m_7023_(new Vec3((double)f, pTravelVector.f_82480_, (double)f1));
            } else if (controller instanceof Player) {
               this.m_20256_(Vec3.f_82478_);
            }

            this.m_146872_();
         } else {
            this.f_20887_ = 0.02F;
            super.m_7023_(pTravelVector);
         }
      }

   }

   protected SoundEvent m_7515_() {
      return SoundEvents.f_12173_;
   }

   protected SoundEvent m_7975_(DamageSource source) {
      return SoundEvents.f_12172_;
   }

   protected SoundEvent m_5592_() {
      return SoundEvents.f_12173_;
   }

   public boolean isInFlyingPose() {
      if (!this.m_20096_() && !this.m_21525_()) {
         if (!this.m_9236_().m_8055_(this.m_20097_().m_7495_()).m_60767_().m_76336_()) {
            return false;
         } else {
            return !this.m_21825_() && !this.m_21827_();
         }
      } else {
         return false;
      }
   }

   private <E extends IAnimatable> PlayState predicate(AnimationEvent<E> event) {
      if (this.m_5803_()) {
         event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.winged_cat.loaf", EDefaultLoopTypes.LOOP));
      } else if (this.m_21825_()) {
         event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.winged_cat.sit", EDefaultLoopTypes.LOOP));
      } else if (this.isInFlyingPose()) {
         if (event.isMoving()) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.winged_cat.fly", EDefaultLoopTypes.LOOP));
         } else {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.winged_cat.idle_fly", EDefaultLoopTypes.LOOP));
         }
      } else if (event.isMoving() && !this.m_21525_()) {
         event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.winged_cat.walk", EDefaultLoopTypes.LOOP));
      } else {
         event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.winged_cat.idle", EDefaultLoopTypes.LOOP));
      }

      return PlayState.CONTINUE;
   }

   private <E extends IAnimatable> PlayState playOncePredicate(AnimationEvent<E> event) {
      if (event.getController().getAnimationState().equals(AnimationState.Stopped)) {
         event.getController().markNeedsReload();
         if (this.getMiscAnimation() == 1) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.winged_cat.eat", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 2) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.winged_cat.single_strike", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 3) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.winged_cat.double_strike", EDefaultLoopTypes.PLAY_ONCE));
         }
      }

      return PlayState.CONTINUE;
   }

   public void registerControllers(AnimationData data) {
      data.addAnimationController(new AnimationController(this, "controller", 0.0F, this::predicate));
      data.addAnimationController(new AnimationController(this, "playOnceController", 0.0F, this::playOncePredicate));
   }

   public AnimationFactory getFactory() {
      return this.factory;
   }

   static {
      MISC_ANIMATION = SynchedEntityData.m_135353_(WingedCatEntity.class, EntityDataSerializers.f_135028_);
      SUMMONING_TICK = SynchedEntityData.m_135353_(WingedCatEntity.class, EntityDataSerializers.f_135028_);
      SUMMONER_UUID = SynchedEntityData.m_135353_(WingedCatEntity.class, EntityDataSerializers.f_135041_);
   }

   static class CatAttackGoal extends DynamicMeleeAttackGoal {
      public final WingedCatEntity serpent;

      public CatAttackGoal(WingedCatEntity serpent) {
         super(serpent, List.of((self, target, goal) -> {
            return 1.0F;
         }));
         this.serpent = serpent;
      }

      public boolean m_8036_() {
         return this.serpent.m_21827_() ? false : super.m_8036_();
      }

      protected List<DynamicMeleeAttackAction> getActions() {
         List<DynamicMeleeAttackAction> list = new ArrayList();
         list.add((self, target, goal) -> {
            float speed = 1.5F;
            double distanceSqr = self.m_20280_(target);
            if (distanceSqr > 400.0D) {
               speed = 2.0F;
            } else {
               this.checkAndPerformAttack(distanceSqr);
               self.m_21391_(target, 70.0F, 70.0F);
            }

            return speed;
         });
         return list;
      }

      protected void checkAndPerformAttack(double pDistToEnemySqr) {
         if (this.serpent.getMiscAnimation() == 0) {
            int randomAttack = this.randomAttack(pDistToEnemySqr);
            double attackRange = randomAttack == 2 ? 900.0D : 9.0D;
            if (pDistToEnemySqr <= attackRange) {
               this.serpent.setMiscAnimation(randomAttack);
               if (randomAttack == 1) {
                  this.serpent.m_21573_().m_26573_();
               }
            }
         }

      }

      protected int randomAttack(double distance) {
         return distance >= 9.0D ? 2 : 3;
      }
   }
}
