package com.github.manasmods.tensura.entity;

import com.github.manasmods.tensura.api.entity.ai.BetterHorseRunAroundGoal;
import com.github.manasmods.tensura.api.entity.ai.TamableFollowParentGoal;
import com.github.manasmods.tensura.config.SpawnRateConfig;
import com.github.manasmods.tensura.entity.template.TensuraHorseEntity;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.items.TensuraMaterialItems;
import java.util.Iterator;
import java.util.UUID;
import net.minecraft.core.BlockPos;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.syncher.EntityDataAccessor;
import net.minecraft.network.syncher.EntityDataSerializers;
import net.minecraft.network.syncher.SynchedEntityData;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.util.RandomSource;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.AgeableMob;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.MobSpawnType;
import net.minecraft.world.entity.ai.attributes.AttributeSupplier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.goal.AvoidEntityGoal;
import net.minecraft.world.entity.ai.goal.BreedGoal;
import net.minecraft.world.entity.ai.goal.LookAtPlayerGoal;
import net.minecraft.world.entity.ai.goal.PanicGoal;
import net.minecraft.world.entity.ai.goal.RandomLookAroundGoal;
import net.minecraft.world.entity.animal.Animal;
import net.minecraft.world.entity.animal.horse.AbstractChestedHorse;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.LevelAccessor;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.gameevent.GameEvent;

public class UnicornEntity extends TensuraHorseEntity {
   private static final EntityDataAccessor<Integer> RAINBOW_HORN_TICKS;

   public UnicornEntity(EntityType<? extends AbstractChestedHorse> pEntityType, Level pLevel) {
      super(pEntityType, pLevel);
      this.f_19793_ = 1.5F;
      this.f_21364_ = 40;
   }

   public static AttributeSupplier setAttributes() {
      return Mob.m_21552_().m_22268_(Attributes.f_22288_, 1.0D).m_22268_(Attributes.f_22276_, 40.0D).m_22268_(Attributes.f_22279_, 0.3799999952316284D).m_22268_(Attributes.f_22278_, 0.009999999776482582D).m_22268_(Attributes.f_22277_, 32.0D).m_22265_();
   }

   public void m_8099_() {
      this.f_21345_.m_25352_(1, new PanicGoal(this, 1.5D));
      this.f_21345_.m_25352_(1, new BetterHorseRunAroundGoal(this, 1.5D, 0));
      this.f_21345_.m_25352_(2, new TensuraHorseEntity.HorseSitWhenOrderedToGoal(this));
      this.f_21345_.m_25352_(2, new TensuraHorseEntity.HorseFollowOwnerGoal(this, 1.0D, 10.0F, 2.0F, true));
      this.f_21345_.m_25352_(3, new BreedGoal(this, 0.8D, TensuraHorseEntity.class));
      this.f_21345_.m_25352_(4, new TamableFollowParentGoal(this, 1.0D));
      this.f_21345_.m_25352_(5, new AvoidEntityGoal(this, Player.class, 16.0F, 1.5D, 0.95D, (entity) -> {
         return !entity.m_20163_() && !this.m_20160_() && !this.m_30614_();
      }));
      this.f_21345_.m_25352_(6, new TensuraHorseEntity.WanderAroundPosGoal(this, 0.7D));
      this.f_21345_.m_25352_(7, new LookAtPlayerGoal(this, Player.class, 6.0F));
      this.f_21345_.m_25352_(8, new RandomLookAroundGoal(this));
   }

   public void m_8119_() {
      super.m_8119_();
      if (this.getRainbowHornTicks() > 0) {
         this.setRainbowHornTicks(this.getRainbowHornTicks() - 1);
      }

      if (this.f_19853_.f_46443_) {
         if (!(this.f_19796_.m_188501_() >= 0.3F) && !this.m_6162_()) {
            float radius = this.m_20205_();
            this.f_19853_.m_7106_(ParticleTypes.f_123751_, this.m_20185_() + (this.f_19853_.f_46441_.m_188500_() - 0.5D) * (double)radius, this.m_20186_() + (this.f_19853_.f_46441_.m_188500_() - 0.5D) * (double)radius * 0.75D, this.m_20189_() + (this.f_19853_.f_46441_.m_188500_() - 0.5D) * (double)radius, 0.0D, 0.0D, 0.0D);
         }
      }
   }

   protected void m_8097_() {
      super.m_8097_();
      this.f_19804_.m_135372_(RAINBOW_HORN_TICKS, -1);
   }

   public void m_7380_(CompoundTag compound) {
      super.m_7380_(compound);
      compound.m_128405_("RainbowHorn", this.getRainbowHornTicks());
   }

   public void m_7378_(CompoundTag compound) {
      super.m_7378_(compound);
      this.setRainbowHornTicks(compound.m_128451_("RainbowHorn"));
   }

   public int getRainbowHornTicks() {
      return (Integer)this.f_19804_.m_135370_(RAINBOW_HORN_TICKS);
   }

   public void setRainbowHornTicks(int tick) {
      this.f_19804_.m_135381_(RAINBOW_HORN_TICKS, tick);
   }

   public boolean m_6785_(double pDistanceToClosestPlayer) {
      return false;
   }

   public boolean m_6673_(DamageSource source) {
      return source == DamageSource.f_19309_ || source == DamageSource.f_19314_ || source == DamageSource.f_19325_ || super.m_6673_(source);
   }

   public double m_6048_() {
      return 1.2D;
   }

   public int m_5792_() {
      return 2;
   }

   public int m_7555_() {
      return 300;
   }

   public boolean m_142535_(float pFallDistance, float pMultiplier, DamageSource pSource) {
      if (pFallDistance > 1.0F) {
         this.m_5496_(SoundEvents.f_11980_, 0.4F, 1.0F);
      }

      if (pFallDistance < 5.0F) {
         return false;
      } else {
         int i = this.m_5639_(pFallDistance - 5.0F, pMultiplier);
         if (i <= 0) {
            return false;
         } else {
            this.m_6469_(pSource, (float)i);
            if (this.m_20160_()) {
               Iterator var5 = this.m_146897_().iterator();

               while(var5.hasNext()) {
                  Entity entity = (Entity)var5.next();
                  entity.m_6469_(pSource, (float)i);
               }
            }

            this.m_21229_();
            return true;
         }
      }
   }

   public AgeableMob m_142606_(ServerLevel pLevel, AgeableMob pOtherParent) {
      UnicornEntity unicorn = (UnicornEntity)((EntityType)TensuraEntityTypes.UNICORN.get()).m_20615_(pLevel);
      if (unicorn == null) {
         return null;
      } else {
         UUID uuid = this.m_21805_();
         if (uuid != null) {
            unicorn.m_30586_(uuid);
            unicorn.m_30651_(true);
         }

         return unicorn;
      }
   }

   public void m_30651_(boolean pTamed) {
      super.m_30651_(pTamed);
      this.setRainbowHornTicks(80);
   }

   protected boolean m_5994_(Player pPlayer, ItemStack pStack) {
      int newAdditionalTemper = 1;
      if (pStack.m_150930_(Items.f_42501_)) {
         newAdditionalTemper = 2;
      } else if (!pStack.m_150930_(Items.f_42405_) && !pStack.m_150930_((Item)TensuraMaterialItems.THATCH.get())) {
         if (pStack.m_150930_(Blocks.f_50335_.m_5456_())) {
            newAdditionalTemper = 6;
            if (!this.m_30614_()) {
               this.m_30653_(4);
               this.m_146850_(GameEvent.f_157806_);
               this.setAdditionalTemper(newAdditionalTemper);
            }

            this.setRainbowHornTicks(this.getRainbowHornTicks() + newAdditionalTemper * 10);
            return true;
         }

         if (pStack.m_150930_(Items.f_42410_)) {
            newAdditionalTemper = 5;
         } else if (pStack.m_150930_(Items.f_42677_)) {
            newAdditionalTemper = 8;
         } else if (pStack.m_150930_(Items.f_42436_)) {
            newAdditionalTemper = 10;
         } else if (pStack.m_150930_(Items.f_42437_)) {
            newAdditionalTemper = 15;
            if (!this.m_30614_()) {
               this.m_30649_(this.m_7555_() - 10);
            }
         }
      } else {
         newAdditionalTemper = 3;
      }

      if (!this.m_30614_()) {
         this.setAdditionalTemper(newAdditionalTemper);
      }

      this.setRainbowHornTicks(this.getRainbowHornTicks() + newAdditionalTemper * 15);
      return super.m_5994_(pPlayer, pStack);
   }

   protected SoundEvent m_7975_(DamageSource pDamageSource) {
      super.m_7975_(pDamageSource);
      this.setAdditionalTemper(1);
      int i = this.m_30624_() - 5;
      this.m_30649_(Math.max(i, 0));
      return SoundEvents.f_11978_;
   }

   public static boolean checkUnicornSpawnRules(EntityType<UnicornEntity> pOwl, LevelAccessor pLevel, MobSpawnType pSpawnType, BlockPos pPos, RandomSource pRandom) {
      return Animal.m_218104_(pOwl, pLevel, pSpawnType, pPos, pRandom);
   }

   public boolean m_5545_(LevelAccessor pLevel, MobSpawnType pSpawnReason) {
      return SpawnRateConfig.rollSpawn((Integer)SpawnRateConfig.INSTANCE.unicornSpawnRate.get(), this.m_217043_(), pSpawnReason) && super.m_5545_(pLevel, pSpawnReason);
   }

   static {
      RAINBOW_HORN_TICKS = SynchedEntityData.m_135353_(UnicornEntity.class, EntityDataSerializers.f_135028_);
   }
}
