package com.github.manasmods.tensura.entity.multipart;

import com.github.manasmods.tensura.item.food.HealingPotionItem;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.sound.TensuraSoundEvents;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.nbt.ListTag;
import net.minecraft.network.chat.Component;
import net.minecraft.network.syncher.EntityDataAccessor;
import net.minecraft.network.syncher.EntityDataSerializers;
import net.minecraft.network.syncher.SynchedEntityData;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.InteractionResult;
import net.minecraft.world.MenuProvider;
import net.minecraft.world.SimpleContainer;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityDimensions;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.MobType;
import net.minecraft.world.entity.PlayerRideable;
import net.minecraft.world.entity.Pose;
import net.minecraft.world.entity.player.Inventory;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.inventory.AbstractContainerMenu;
import net.minecraft.world.inventory.ChestMenu;
import net.minecraft.world.inventory.MenuType;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.gameevent.GameEvent;
import net.minecraftforge.common.Tags.Items;
import org.jetbrains.annotations.NotNull;
import software.bernie.geckolib3.core.IAnimatable;
import software.bernie.geckolib3.core.PlayState;
import software.bernie.geckolib3.core.builder.AnimationBuilder;
import software.bernie.geckolib3.core.builder.ILoopType.EDefaultLoopTypes;
import software.bernie.geckolib3.core.controller.AnimationController;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.manager.AnimationData;
import software.bernie.geckolib3.core.manager.AnimationFactory;
import software.bernie.geckolib3.util.GeckoLibUtil;

public class EvilCentipedeBody extends LivingMultipartBody implements IAnimatable, PlayerRideable {
   private static final EntityDataAccessor<Boolean> CHESTED;
   private final AnimationFactory factory = GeckoLibUtil.createFactory(this);
   public SimpleContainer inventory;
   public MenuProvider inventoryMenu;
   private boolean hasChestVarChanged = false;

   public EvilCentipedeBody(EntityType<? extends EvilCentipedeBody> type, Level worldIn) {
      super(type, worldIn);
      this.initInventory();
   }

   public EvilCentipedeBody(EntityType<? extends EvilCentipedeBody> type, LivingEntity parent) {
      super(type, parent);
      this.initInventory();
   }

   @NotNull
   public MobType m_6336_() {
      return MobType.f_21642_;
   }

   protected void m_8097_() {
      super.m_8097_();
      this.f_19804_.m_135372_(CHESTED, Boolean.FALSE);
   }

   public void m_7380_(CompoundTag compound) {
      super.m_7380_(compound);
      compound.m_128379_("Chested", this.isChested());
      if (this.inventory != null) {
         ListTag listTag = new ListTag();

         for(int i = 0; i < this.inventory.m_6643_(); ++i) {
            ItemStack itemstack = this.inventory.m_8020_(i);
            if (!itemstack.m_41619_()) {
               CompoundTag CompoundNBT = new CompoundTag();
               CompoundNBT.m_128344_("Slot", (byte)i);
               itemstack.m_41739_(CompoundNBT);
               listTag.add(CompoundNBT);
            }
         }

         compound.m_128365_("Items", listTag);
      }

   }

   public void m_7378_(CompoundTag compound) {
      super.m_7378_(compound);
      this.setChested(compound.m_128471_("Chested"));
      ListTag listTag;
      int i;
      CompoundTag CompoundNBT;
      int j;
      if (this.inventory != null) {
         listTag = compound.m_128437_("Items", 10);
         this.initInventory();

         for(i = 0; i < listTag.size(); ++i) {
            CompoundNBT = listTag.m_128728_(i);
            j = CompoundNBT.m_128445_("Slot") & 255;
            this.inventory.m_6836_(j, ItemStack.m_41712_(CompoundNBT));
         }
      } else {
         listTag = compound.m_128437_("Items", 10);
         this.initInventory();

         for(i = 0; i < listTag.size(); ++i) {
            CompoundNBT = listTag.m_128728_(i);
            j = CompoundNBT.m_128445_("Slot") & 255;
            this.initInventory();
            this.inventory.m_6836_(j, ItemStack.m_41712_(CompoundNBT));
         }
      }

   }

   public boolean isChested() {
      return (Boolean)this.f_19804_.m_135370_(CHESTED);
   }

   public void setChested(boolean chested) {
      this.f_19804_.m_135381_(CHESTED, chested);
      this.hasChestVarChanged = true;
   }

   public void m_7350_(EntityDataAccessor<?> pKey) {
      if (CHESTED.equals(pKey)) {
         this.m_6210_();
      }

      super.m_7350_(pKey);
   }

   public EntityDimensions m_6972_(Pose pPose) {
      EntityDimensions dimensions = super.m_6972_(pPose);
      return this.isChested() ? dimensions.m_20390_(1.0F, 1.25F) : dimensions;
   }

   public boolean m_7301_(MobEffectInstance pEffectInstance) {
      return pEffectInstance.m_19544_().equals(TensuraMobEffects.PARALYSIS.get()) ? false : super.m_7301_(pEffectInstance);
   }

   public void m_8119_() {
      super.m_8119_();
      if (this.hasChestVarChanged && this.inventory != null && !this.isChested()) {
         for(int i = 3; i < 18; ++i) {
            if (!this.inventory.m_8020_(i).m_41619_()) {
               if (!this.f_19853_.f_46443_) {
                  this.m_5552_(this.inventory.m_8020_(i), 1.0F);
               }

               this.inventory.m_8016_(i);
            }
         }

         this.hasChestVarChanged = false;
      }

   }

   private void initInventory() {
      SimpleContainer chest = this.inventory;
      this.inventory = new SimpleContainer(18) {
         public boolean m_6542_(Player player) {
            return EvilCentipedeBody.this.m_6084_() && !EvilCentipedeBody.this.f_19817_;
         }
      };
      if (chest != null) {
         int i = Math.min(chest.m_6643_(), this.inventory.m_6643_());

         for(int j = 0; j < i; ++j) {
            ItemStack itemstack = chest.m_8020_(j);
            if (!itemstack.m_41619_()) {
               this.inventory.m_6836_(j, itemstack.m_41777_());
            }
         }
      }

   }

   public void openCustomInventoryScreen(Player pPlayer) {
      if (this.isChested()) {
         if (this.inventory != null) {
            pPlayer.m_5893_(this.getMenu());
            if (!pPlayer.f_19853_.f_46443_) {
               this.m_146852_(GameEvent.f_157803_, pPlayer);
            }

         }
      }
   }

   public MenuProvider getMenu() {
      if (this.inventoryMenu == null) {
         this.inventoryMenu = new MenuProvider() {
            public AbstractContainerMenu m_7208_(int menu, Inventory inventory, Player player) {
               return new ChestMenu(MenuType.f_39958_, menu, inventory, EvilCentipedeBody.this.inventory, 2);
            }

            @NotNull
            public Component m_5446_() {
               return Component.m_237115_("container.chest");
            }
         };
      }

      return this.inventoryMenu;
   }

   protected void m_5907_() {
      super.m_5907_();
      if (this.isChested()) {
         if (!this.f_19853_.m_5776_()) {
            this.m_19998_(Blocks.f_50087_);

            for(int i = 0; i < this.inventory.m_6643_(); ++i) {
               this.m_19983_(this.inventory.m_8020_(i));
            }
         }

         this.inventory.m_6211_();
         this.setChested(false);
      }

   }

   public void m_6667_(@NotNull DamageSource cause) {
      super.m_6667_(cause);
      if (!this.f_19853_.m_5776_()) {
         if (!this.m_6084_()) {
            if (this.inventory != null) {
               for(int i = 0; i < this.inventory.m_6643_(); ++i) {
                  ItemStack itemstack = this.inventory.m_8020_(i);
                  if (!itemstack.m_41619_()) {
                     this.m_5552_(itemstack, 0.0F);
                  }
               }
            }

         }
      }
   }

   public boolean m_6146_() {
      return true;
   }

   protected boolean m_7310_(Entity pPassenger) {
      return this.getBodyIndex() == 0 ? false : super.m_7310_(pPassenger);
   }

   @NotNull
   public InteractionResult m_6071_(Player player, @NotNull InteractionHand hand) {
      ItemStack itemstack = player.m_21120_(hand);
      if (itemstack.m_41720_() instanceof HealingPotionItem) {
         return super.m_6071_(player, hand);
      } else {
         InteractionResult eating = this.handleEating(player, hand, itemstack);
         if (eating.m_19077_()) {
            return eating;
         } else {
            EvilCentipedeEntity head = (EvilCentipedeEntity)this.getHead();
            if (head != null && head.m_21824_()) {
               if (head.m_21830_(player)) {
                  if (player.m_36341_()) {
                     head.commanding(player);
                     return InteractionResult.m_19078_(this.f_19853_.f_46443_);
                  } else if (!this.isChested() && this.getBodyIndex() != 0 && itemstack.m_204117_(Items.CHESTS_WOODEN)) {
                     this.setChested(true);
                     this.m_5496_(SoundEvents.f_11811_, 1.0F, (this.f_19796_.m_188501_() - this.f_19796_.m_188501_()) * 0.2F + 1.0F);
                     if (!player.m_150110_().f_35937_) {
                        itemstack.m_41774_(1);
                     }

                     return InteractionResult.m_19078_(this.f_19853_.f_46443_);
                  } else if (this.isChested() && itemstack.m_150930_(net.minecraft.world.item.Items.f_42574_)) {
                     this.m_5496_(SoundEvents.f_12344_, 1.0F, (this.f_19796_.m_188501_() - this.f_19796_.m_188501_()) * 0.2F + 1.0F);
                     this.m_5907_();
                     return InteractionResult.m_19078_(this.f_19853_.f_46443_);
                  } else {
                     return this.nonTameInteract(player);
                  }
               } else {
                  return this.nonTameInteract(player);
               }
            } else {
               return InteractionResult.PASS;
            }
         }
      }
   }

   protected InteractionResult nonTameInteract(Player player) {
      if (this.isChested()) {
         this.openCustomInventoryScreen(player);
         return InteractionResult.m_19078_(this.f_19853_.f_46443_);
      } else if (this.m_146895_() == null && this.m_7310_(player)) {
         player.m_7998_(this, false);
         return InteractionResult.m_19078_(this.f_19853_.f_46443_);
      } else {
         return InteractionResult.PASS;
      }
   }

   public InteractionResult handleEating(Player player, InteractionHand hand, ItemStack itemstack) {
      EvilCentipedeEntity head = (EvilCentipedeEntity)this.getHead();
      if (head != null && head.m_6898_(itemstack) && head.m_21223_() < head.m_21233_()) {
         if (!player.m_7500_()) {
            itemstack.m_41774_(1);
         }

         head.m_8035_();
         this.m_9236_().m_6269_((Player)null, head, (SoundEvent)TensuraSoundEvents.EATING.get(), SoundSource.NEUTRAL, 1.0F, 1.0F);
         return InteractionResult.SUCCESS;
      } else {
         return InteractionResult.PASS;
      }
   }

   private <E extends IAnimatable> PlayState predicate(AnimationEvent<E> event) {
      if (this.isEndSegment() && this.m_21825_()) {
         event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.evil_centipede.hold", EDefaultLoopTypes.LOOP));
      } else if (event.isMoving()) {
         if (!this.m_20072_() && this.m_9236_().m_6425_(this.m_20183_().m_7495_()).m_76178_()) {
            if ((double)event.getLimbSwingAmount() >= 0.6D) {
               event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.evil_centipede.walk", EDefaultLoopTypes.LOOP));
            } else {
               event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.evil_centipede.idle", EDefaultLoopTypes.LOOP));
            }
         } else {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.evil_centipede.swim", EDefaultLoopTypes.LOOP));
         }
      }

      return PlayState.CONTINUE;
   }

   public void registerControllers(AnimationData data) {
      data.addAnimationController(new AnimationController(this, "controller", 0.0F, this::predicate));
   }

   public AnimationFactory getFactory() {
      return this.factory;
   }

   static {
      CHESTED = SynchedEntityData.m_135353_(EvilCentipedeBody.class, EntityDataSerializers.f_135035_);
   }
}
