package com.github.manasmods.tensura.entity;

import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.api.entity.ai.TamableFollowParentGoal;
import com.github.manasmods.tensura.api.entity.ai.WanderingFollowOwnerGoal;
import com.github.manasmods.tensura.api.entity.subclass.ITensuraMount;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.config.SpawnRateConfig;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.entity.template.TensuraTamableEntity;
import com.github.manasmods.tensura.item.food.HealingPotionItem;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.items.TensuraMaterialItems;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import com.github.manasmods.tensura.registry.sound.TensuraSoundEvents;
import com.github.manasmods.tensura.util.damage.TensuraEntityDamageSource;
import java.util.Iterator;
import java.util.List;
import java.util.UUID;
import java.util.function.Predicate;
import javax.annotation.Nullable;
import net.minecraft.commands.arguments.EntityAnchorArgument.Anchor;
import net.minecraft.core.BlockPos;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.core.particles.SimpleParticleType;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.nbt.ListTag;
import net.minecraft.network.chat.Component;
import net.minecraft.network.syncher.EntityDataAccessor;
import net.minecraft.network.syncher.EntityDataSerializers;
import net.minecraft.network.syncher.SynchedEntityData;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.util.Mth;
import net.minecraft.util.RandomSource;
import net.minecraft.world.Difficulty;
import net.minecraft.world.DifficultyInstance;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.InteractionResult;
import net.minecraft.world.MenuProvider;
import net.minecraft.world.SimpleContainer;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.AgeableMob;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.HasCustomInventoryScreen;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.MobSpawnType;
import net.minecraft.world.entity.PlayerRideableJumping;
import net.minecraft.world.entity.SpawnGroupData;
import net.minecraft.world.entity.ai.attributes.AttributeSupplier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.goal.BreedGoal;
import net.minecraft.world.entity.ai.goal.FloatGoal;
import net.minecraft.world.entity.ai.goal.LookAtPlayerGoal;
import net.minecraft.world.entity.ai.goal.MeleeAttackGoal;
import net.minecraft.world.entity.ai.goal.RandomLookAroundGoal;
import net.minecraft.world.entity.ai.goal.SitWhenOrderedToGoal;
import net.minecraft.world.entity.ai.goal.target.NonTameRandomTargetGoal;
import net.minecraft.world.entity.ai.goal.target.ResetUniversalAngerTargetGoal;
import net.minecraft.world.entity.animal.Animal;
import net.minecraft.world.entity.player.Inventory;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.food.FoodProperties;
import net.minecraft.world.inventory.AbstractContainerMenu;
import net.minecraft.world.inventory.ChestMenu;
import net.minecraft.world.inventory.MenuType;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;
import net.minecraft.world.item.enchantment.EnchantmentHelper;
import net.minecraft.world.level.ItemLike;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.LevelAccessor;
import net.minecraft.world.level.ServerLevelAccessor;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.gameevent.GameEvent;
import net.minecraft.world.phys.AABB;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.common.ForgeHooks;
import software.bernie.geckolib3.core.AnimationState;
import software.bernie.geckolib3.core.IAnimatable;
import software.bernie.geckolib3.core.PlayState;
import software.bernie.geckolib3.core.builder.AnimationBuilder;
import software.bernie.geckolib3.core.builder.ILoopType.EDefaultLoopTypes;
import software.bernie.geckolib3.core.controller.AnimationController;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.manager.AnimationData;
import software.bernie.geckolib3.core.manager.AnimationFactory;
import software.bernie.geckolib3.util.GeckoLibUtil;

public class BladeTigerEntity extends TensuraTamableEntity implements IAnimatable, ITensuraMount, HasCustomInventoryScreen, PlayerRideableJumping {
   private static final EntityDataAccessor<Integer> MISC_ANIMATION;
   private static final EntityDataAccessor<Boolean> SADDLED;
   private static final EntityDataAccessor<Boolean> CHESTED;
   private static final EntityDataAccessor<Boolean> WHITE;
   private final AnimationFactory factory = GeckoLibUtil.createFactory(this);
   public int miscAnimationTicks = 0;
   public SimpleContainer inventory;
   public MenuProvider inventoryMenu;
   private boolean hasChestVarChanged = false;
   protected float playerJumpPendingScale;
   protected boolean playerJumping;

   public BladeTigerEntity(EntityType<? extends BladeTigerEntity> pEntityType, Level pLevel) {
      super(pEntityType, pLevel);
      this.f_21364_ = 40;
      this.f_19793_ = 2.0F;
      this.initInventory();
   }

   public static AttributeSupplier setAttributes() {
      return Mob.m_21552_().m_22268_(Attributes.f_22281_, 20.0D).m_22268_(Attributes.f_22276_, 100.0D).m_22268_(Attributes.f_22279_, 0.20000000298023224D).m_22268_(Attributes.f_22277_, 32.0D).m_22268_(Attributes.f_22284_, 10.0D).m_22268_(Attributes.f_22278_, 0.6000000238418579D).m_22268_(Attributes.f_22288_, 1.5D).m_22265_();
   }

   protected void m_8099_() {
      this.f_21345_.m_25352_(0, new FloatGoal(this));
      this.f_21345_.m_25352_(1, new SitWhenOrderedToGoal(this));
      this.f_21345_.m_25352_(3, new BladeTigerEntity.BladeTigerAttackGoal());
      this.f_21345_.m_25352_(4, new WanderingFollowOwnerGoal(this, 1.5D, 10.0F, 5.0F, false));
      this.f_21345_.m_25352_(5, new BreedGoal(this, 1.2D, BladeTigerEntity.class));
      this.f_21345_.m_25352_(6, new TamableFollowParentGoal(this, 1.5D));
      this.f_21345_.m_25352_(7, new TensuraTamableEntity.WanderAroundPosGoal(this));
      this.f_21345_.m_25352_(8, new RandomLookAroundGoal(this));
      this.f_21345_.m_25352_(9, new LookAtPlayerGoal(this, Player.class, 6.0F));
      this.f_21346_.m_25352_(1, new TensuraTamableEntity.TensuraOwnerHurtByTargetGoal(this));
      this.f_21346_.m_25352_(2, new TensuraTamableEntity.TensuraOwnerHurtTargetGoal(this));
      this.f_21346_.m_25352_(3, (new TensuraTamableEntity.TensuraHurtByTargetGoal(this)).m_26044_(new Class[0]));
      this.f_21346_.m_25352_(4, new NonTameRandomTargetGoal(this, Player.class, false, (Predicate)null));
      this.f_21346_.m_25352_(4, new NonTameRandomTargetGoal(this, Animal.class, false, (e) -> {
         return e.m_6095_().m_204039_(TensuraTags.EntityTypes.ANIMAL_PREY);
      }));
      this.f_21346_.m_25352_(8, new ResetUniversalAngerTargetGoal(this, true));
   }

   protected void m_8097_() {
      super.m_8097_();
      this.f_19804_.m_135372_(MISC_ANIMATION, 0);
      this.f_19804_.m_135372_(SADDLED, Boolean.FALSE);
      this.f_19804_.m_135372_(CHESTED, Boolean.FALSE);
      this.f_19804_.m_135372_(WHITE, Boolean.FALSE);
   }

   public void m_7380_(CompoundTag compound) {
      super.m_7380_(compound);
      compound.m_128405_("MiscAnimation", this.getMiscAnimation());
      compound.m_128379_("Saddled", this.isSaddled());
      compound.m_128379_("Chested", this.isChested());
      compound.m_128379_("White", this.isWhite());
      if (this.inventory != null) {
         ListTag listTag = new ListTag();

         for(int i = 0; i < this.inventory.m_6643_(); ++i) {
            ItemStack itemstack = this.inventory.m_8020_(i);
            if (!itemstack.m_41619_()) {
               CompoundTag CompoundNBT = new CompoundTag();
               CompoundNBT.m_128344_("Slot", (byte)i);
               itemstack.m_41739_(CompoundNBT);
               listTag.add(CompoundNBT);
            }
         }

         compound.m_128365_("Items", listTag);
      }

   }

   public void m_7378_(CompoundTag compound) {
      super.m_7378_(compound);
      this.f_19804_.m_135381_(MISC_ANIMATION, compound.m_128451_("MiscAnimation"));
      this.setSaddled(compound.m_128471_("Saddled"));
      this.setChested(compound.m_128471_("Chested"));
      this.setWhite(compound.m_128471_("White"));
      ListTag listTag;
      int i;
      CompoundTag CompoundNBT;
      int j;
      if (this.inventory != null) {
         listTag = compound.m_128437_("Items", 10);
         this.initInventory();

         for(i = 0; i < listTag.size(); ++i) {
            CompoundNBT = listTag.m_128728_(i);
            j = CompoundNBT.m_128445_("Slot") & 255;
            this.inventory.m_6836_(j, ItemStack.m_41712_(CompoundNBT));
         }
      } else {
         listTag = compound.m_128437_("Items", 10);
         this.initInventory();

         for(i = 0; i < listTag.size(); ++i) {
            CompoundNBT = listTag.m_128728_(i);
            j = CompoundNBT.m_128445_("Slot") & 255;
            this.initInventory();
            this.inventory.m_6836_(j, ItemStack.m_41712_(CompoundNBT));
         }
      }

   }

   public int getMiscAnimation() {
      return (Integer)this.f_19804_.m_135370_(MISC_ANIMATION);
   }

   public void setMiscAnimation(int animation) {
      if (this.getMiscAnimation() == 0 || animation == 0) {
         this.f_19804_.m_135381_(MISC_ANIMATION, animation);
      }
   }

   public boolean isSaddled() {
      return (Boolean)this.f_19804_.m_135370_(SADDLED);
   }

   public void setSaddled(boolean saddled) {
      this.f_19804_.m_135381_(SADDLED, saddled);
   }

   public boolean isChested() {
      return (Boolean)this.f_19804_.m_135370_(CHESTED);
   }

   public void setChested(boolean chested) {
      this.f_19804_.m_135381_(CHESTED, chested);
      this.hasChestVarChanged = true;
   }

   public boolean isWhite() {
      return (Boolean)this.f_19804_.m_135370_(WHITE);
   }

   public void setWhite(boolean white) {
      this.f_19804_.m_135381_(WHITE, white);
   }

   public boolean canSleep() {
      return true;
   }

   public static boolean checkBladeTigerSpawnRules(EntityType<BladeTigerEntity> spider, LevelAccessor pLevel, MobSpawnType pSpawnType, BlockPos pPos, RandomSource pRandom) {
      return pLevel.m_46791_() != Difficulty.PEACEFUL && m_217057_(spider, pLevel, pSpawnType, pPos, pRandom);
   }

   public boolean m_5545_(LevelAccessor pLevel, MobSpawnType pSpawnReason) {
      return SpawnRateConfig.rollSpawn((Integer)SpawnRateConfig.INSTANCE.bladeTigerSpawnRate.get(), this.m_217043_(), pSpawnReason) && super.m_5545_(pLevel, pSpawnReason);
   }

   @Nullable
   public SpawnGroupData m_6518_(ServerLevelAccessor pLevel, DifficultyInstance pDifficulty, MobSpawnType pReason, @Nullable SpawnGroupData pSpawnData, @Nullable CompoundTag pDataTag) {
      if (this.f_19796_.m_188503_(100) == 69) {
         this.setWhite(true);
      }

      return super.m_6518_(pLevel, pDifficulty, pReason, pSpawnData, pDataTag);
   }

   public AgeableMob m_142606_(ServerLevel pLevel, AgeableMob pOtherParent) {
      BladeTigerEntity bear = (BladeTigerEntity)((EntityType)TensuraEntityTypes.BLADE_TIGER.get()).m_20615_(pLevel);
      if (bear == null) {
         return null;
      } else {
         UUID uuid = this.m_21805_();
         if (uuid != null) {
            bear.m_21816_(uuid);
            bear.m_7105_(true);
         }

         return bear;
      }
   }

   public boolean m_6898_(ItemStack pStack) {
      FoodProperties food = pStack.getFoodProperties(this);
      return food != null && food.m_38746_();
   }

   public boolean m_6673_(DamageSource source) {
      return source == DamageSource.f_19314_ || source == DamageSource.f_19325_ || super.m_6673_(source);
   }

   public boolean m_142535_(float pFallDistance, float pMultiplier, DamageSource pSource) {
      this.m_21229_();
      return false;
   }

   public boolean m_7327_(Entity pEntity) {
      boolean flag = super.m_7327_(pEntity);
      if (flag && this.getMiscAnimation() == 0) {
         this.setMiscAnimation(1);
      }

      return flag;
   }

   public void m_8119_() {
      super.m_8119_();
      this.targetingMovementHelper();
      if (this.getMiscAnimation() != 0) {
         ++this.miscAnimationTicks;
         if (!this.m_6084_()) {
            return;
         }

         LivingEntity target;
         if (this.getMiscAnimation() == 2 && this.miscAnimationTicks == 10) {
            target = this.m_5448_();
            if (target != null) {
               this.m_7327_(target);
               this.m_9236_().m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_12317_, SoundSource.NEUTRAL, 1.5F, 1.0F);
            }
         } else if (this.getMiscAnimation() == 3 && this.miscAnimationTicks == 10) {
            TensuraParticleHelper.addServerParticlesAroundSelf(this, ParticleTypes.f_123766_, 2.0D);
            TensuraParticleHelper.addServerParticlesAroundSelf(this, ParticleTypes.f_123766_, 3.0D);
            this.areaAttack(1.5D, 1.0F, 4.0F);
            this.m_9236_().m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_12317_, SoundSource.NEUTRAL, 1.5F, 1.0F);
         } else if (this.getMiscAnimation() == 4 && this.miscAnimationTicks == 10) {
            target = this.m_5448_();
            if (target != null) {
               TensuraParticleHelper.addServerParticlesAroundSelf(target, ParticleTypes.f_123797_, 1.0D);
               this.doTailPierce(target);
               this.m_9236_().m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_12313_, SoundSource.NEUTRAL, 1.5F, 1.0F);
            }
         } else if (this.getMiscAnimation() == 5 && this.miscAnimationTicks == 10) {
            target = this.m_5448_();
            if (target != null && target.m_20270_(this) <= 20.0F) {
               this.m_7618_(Anchor.EYES, target.m_146892_());
               this.voiceCannon(target.m_146892_());
            } else {
               this.voiceCannon(this.m_146892_().m_82549_(this.m_20154_().m_82490_(20.0D)));
            }
         }

         if (this.miscAnimationTicks >= this.getAnimationTick(this.getMiscAnimation())) {
            this.setMiscAnimation(0);
            this.miscAnimationTicks = 0;
         }
      }

      if (this.hasChestVarChanged && this.inventory != null && !this.isChested()) {
         for(int i = 3; i < 18; ++i) {
            if (!this.inventory.m_8020_(i).m_41619_()) {
               if (!this.f_19853_.f_46443_) {
                  this.m_5552_(this.inventory.m_8020_(i), 1.0F);
               }

               this.inventory.m_8016_(i);
            }
         }

         this.hasChestVarChanged = false;
      }

   }

   public void mountAbility(Player rider) {
      if (this.getMiscAnimation() != 3) {
         if (this.getMiscAnimation() != 4) {
            if (this.getMiscAnimation() != 5) {
               this.setMiscAnimation(5);
            }
         }
      }
   }

   private int getAnimationTick(int miscAnimation) {
      byte var10000;
      switch(miscAnimation) {
      case 1:
      case 3:
         var10000 = 30;
         break;
      default:
         var10000 = 20;
      }

      return var10000;
   }

   public void areaAttack(double multiplier, float strength, float range) {
      AABB aabb = this.m_20191_().m_82400_((double)range);
      List<LivingEntity> livingEntityList = this.f_19853_.m_6443_(LivingEntity.class, aabb, (entity) -> {
         return !entity.m_7307_(this) && entity != this.m_21826_() && !entity.equals(this) && (!(entity instanceof ArmoursaurusEntity) || entity == this.m_5448_());
      });
      if (!livingEntityList.isEmpty()) {
         Iterator var7 = livingEntityList.iterator();

         while(var7.hasNext()) {
            LivingEntity target = (LivingEntity)var7.next();
            target.m_6469_(DamageSource.m_19370_(this).m_19380_(), (float)(this.m_21133_(Attributes.f_22281_) * multiplier));
            SkillHelper.knockBack(this, target, strength);
         }

      }
   }

   public void doTailPierce(Entity target) {
      float f = (float)this.m_21133_(Attributes.f_22281_) * 2.0F;
      int i = EnchantmentHelper.m_44914_(this);
      if (i > 0) {
         target.m_20254_(i * 4);
      }

      if (target.m_6469_(DamageSource.m_19370_(this).m_19380_(), f)) {
         if (target instanceof Player) {
            Player player = (Player)target;
            ItemStack usingItem = player.m_6117_() ? player.m_21211_() : ItemStack.f_41583_;
            if (usingItem.m_150930_(Items.f_42740_)) {
               player.m_36335_().m_41524_(Items.f_42740_, 100);
               this.f_19853_.m_7605_(player, (byte)30);
            }
         }

         this.m_19970_(this, target);
         this.m_21335_(target);
      }

   }

   public void voiceCannon(Vec3 targetPos) {
      Level level = this.m_9236_();
      if (!level.m_5776_()) {
         Vec3 source = this.m_146892_().m_82549_(this.m_20154_().m_82490_(2.0D));
         Vec3 sourceToTarget = targetPos.m_82546_(source);
         Vec3 normalizes = sourceToTarget.m_82541_();
         level.m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_215771_, SoundSource.PLAYERS, 5.0F, 1.0F);

         for(int particleIndex = 1; particleIndex < Mth.m_14107_(sourceToTarget.m_82553_()); ++particleIndex) {
            Vec3 particlePos = source.m_82549_(normalizes.m_82490_((double)particleIndex));
            ((ServerLevel)level).m_8767_((SimpleParticleType)TensuraParticles.SONIC_BLAST.get(), particlePos.f_82479_, particlePos.f_82480_, particlePos.f_82481_, 1, 0.0D, 0.0D, 0.0D, 0.0D);
            AABB aabb = (new AABB(new BlockPos(particlePos.f_82479_, particlePos.f_82480_, particlePos.f_82481_))).m_82400_(2.0D);
            List<LivingEntity> list = level.m_6443_(LivingEntity.class, aabb, (livingx) -> {
               return !livingx.m_7306_(this);
            });
            if (!list.isEmpty()) {
               Iterator var10 = list.iterator();

               while(var10.hasNext()) {
                  LivingEntity living = (LivingEntity)var10.next();
                  DamageSource damagesource = (new TensuraEntityDamageSource("sonic_boom", this)).setMpCost(100.0D).m_19380_().m_238403_();
                  living.m_6469_(damagesource, (float)this.m_21133_(Attributes.f_22281_) * 1.5F);
                  double d0 = Math.max(0.0D, 1.0D - living.m_21133_(Attributes.f_22278_));
                  Vec3 vec3 = this.m_20252_(1.0F).m_82541_().m_82490_(2.0D * d0 * 0.10000000149011612D);
                  if (vec3.m_82556_() > 0.0D) {
                     living.m_5997_(vec3.f_82479_, vec3.f_82480_, vec3.f_82481_);
                  }
               }
            }
         }

      }
   }

   private void initInventory() {
      SimpleContainer chest = this.inventory;
      this.inventory = new SimpleContainer(27) {
         public boolean m_6542_(Player player) {
            return BladeTigerEntity.this.m_6084_() && !BladeTigerEntity.this.f_19817_;
         }
      };
      if (chest != null) {
         int i = Math.min(chest.m_6643_(), this.inventory.m_6643_());

         for(int j = 0; j < i; ++j) {
            ItemStack itemstack = chest.m_8020_(j);
            if (!itemstack.m_41619_()) {
               this.inventory.m_6836_(j, itemstack.m_41777_());
            }
         }
      }

   }

   public void m_213583_(Player pPlayer) {
      if (this.isChested()) {
         if (this.inventory != null) {
            pPlayer.m_5893_(this.getMenu());
            if (!pPlayer.f_19853_.f_46443_) {
               this.m_146852_(GameEvent.f_157803_, pPlayer);
            }

         }
      }
   }

   public void m_6667_(DamageSource cause) {
      super.m_6667_(cause);
      if (!this.f_19853_.m_5776_()) {
         if (this.inventory != null) {
            if (!this.m_6084_()) {
               for(int i = 0; i < this.inventory.m_6643_(); ++i) {
                  ItemStack itemstack = this.inventory.m_8020_(i);
                  if (!itemstack.m_41619_()) {
                     this.m_5552_(itemstack, 0.0F);
                  }
               }

            }
         }
      }
   }

   public MenuProvider getMenu() {
      if (this.inventoryMenu == null) {
         this.inventoryMenu = new MenuProvider() {
            public AbstractContainerMenu m_7208_(int menu, Inventory inventory, Player player) {
               return new ChestMenu(MenuType.f_39959_, menu, inventory, BladeTigerEntity.this.inventory, 3);
            }

            public Component m_5446_() {
               return Component.m_237115_("container.chest");
            }
         };
      }

      return this.inventoryMenu;
   }

   public InteractionResult m_6071_(Player player, InteractionHand hand) {
      ItemStack itemstack = player.m_21120_(hand);
      if (itemstack.m_41720_() instanceof HealingPotionItem) {
         return super.m_6071_(player, hand);
      } else {
         InteractionResult eating = this.handleEating(player, hand, itemstack);
         if (eating.m_19077_()) {
            return eating;
         } else if (this.f_19853_.f_46443_) {
            boolean flag = this.m_21830_(player) || this.m_21824_();
            return flag ? InteractionResult.CONSUME : InteractionResult.PASS;
         } else if (this.m_21824_() && this.m_21830_(player)) {
            if (!this.m_6162_()) {
               Item item = itemstack.m_41720_();
               if (item.equals(TensuraMaterialItems.MONSTER_SADDLE.get()) && !this.isSaddled()) {
                  if (!player.m_150110_().f_35937_) {
                     itemstack.m_41774_(1);
                  }

                  this.setSaddled(true);
                  this.m_5496_(SoundEvents.f_11811_, 1.0F, (this.f_19796_.m_188501_() - this.f_19796_.m_188501_()) * 0.2F + 1.0F);
                  return InteractionResult.m_19078_(this.f_19853_.f_46443_);
               }

               if (!this.isChested() && itemstack.m_204117_(net.minecraftforge.common.Tags.Items.CHESTS_WOODEN)) {
                  this.setChested(true);
                  this.m_5496_(SoundEvents.f_11811_, 1.0F, (this.f_19796_.m_188501_() - this.f_19796_.m_188501_()) * 0.2F + 1.0F);
                  if (!player.m_150110_().f_35937_) {
                     itemstack.m_41774_(1);
                  }

                  return InteractionResult.m_19078_(this.f_19853_.f_46443_);
               }

               if (this.isChested() && item.equals(Items.f_42574_)) {
                  this.m_5496_(SoundEvents.f_12344_, 1.0F, (this.f_19796_.m_188501_() - this.f_19796_.m_188501_()) * 0.2F + 1.0F);
                  this.m_19998_(Blocks.f_50087_);

                  for(int i = 0; i < this.inventory.m_6643_(); ++i) {
                     this.m_19983_(this.inventory.m_8020_(i));
                  }

                  this.inventory.m_6211_();
                  this.setChested(false);
                  return InteractionResult.SUCCESS;
               }

               if (this.isSaddled() && item.equals(Items.f_42574_)) {
                  this.m_5496_(SoundEvents.f_12344_, 1.0F, (this.f_19796_.m_188501_() - this.f_19796_.m_188501_()) * 0.2F + 1.0F);
                  this.m_19998_((ItemLike)TensuraMaterialItems.MONSTER_SADDLE.get());
                  this.setSaddled(false);
                  return InteractionResult.m_19078_(this.f_19853_.f_46443_);
               }
            }

            if (player.m_36341_() || !this.isSaddled() && !this.isChested()) {
               this.commanding(player);
            } else if (player.m_146895_() == null && this.isSaddled()) {
               this.m_21839_(false);
               this.setWandering(false);
               player.m_7998_(this, false);
            } else if (this.isChested()) {
               this.m_213583_(player);
            }

            return InteractionResult.m_19078_(this.f_19853_.f_46443_);
         } else {
            return super.m_6071_(player, hand);
         }
      }
   }

   public InteractionResult handleEating(Player player, InteractionHand hand, ItemStack itemstack) {
      if (this.m_6898_(itemstack)) {
         if (this.m_21223_() < this.m_21233_()) {
            if (!player.m_7500_()) {
               itemstack.m_41774_(1);
            }

            this.m_5634_(5.0F);
            this.m_9236_().m_6269_((Player)null, this, (SoundEvent)TensuraSoundEvents.EATING.get(), SoundSource.NEUTRAL, 1.0F, 1.0F);
            this.setMiscAnimation(1);
            return InteractionResult.SUCCESS;
         }

         int i = this.m_146764_();
         if (!this.f_19853_.m_5776_() && i == 0 && this.m_5957_()) {
            this.m_142075_(player, hand, itemstack);
            this.m_27595_(player);
            this.setMiscAnimation(1);
            return InteractionResult.SUCCESS;
         }

         if (this.m_6162_()) {
            this.m_142075_(player, hand, itemstack);
            this.m_146740_(m_216967_(-this.m_146764_()), true);
            this.m_9236_().m_6269_(player, this, (SoundEvent)TensuraSoundEvents.EATING.get(), SoundSource.NEUTRAL, 1.0F, 1.0F);
            this.setMiscAnimation(1);
            return InteractionResult.m_19078_(this.f_19853_.f_46443_);
         }
      }

      return InteractionResult.PASS;
   }

   public boolean m_7132_() {
      return this.m_20160_();
   }

   public double getCustomJump() {
      return this.m_21133_(Attributes.f_22288_);
   }

   public boolean m_6146_() {
      return true;
   }

   @Nullable
   public LivingEntity getControllingPassenger() {
      Iterator var1 = this.m_20197_().iterator();

      while(var1.hasNext()) {
         Entity passenger = (Entity)var1.next();
         if (passenger instanceof Player) {
            Player player = (Player)passenger;
            if (player.equals(this.m_21826_())) {
               return player;
            }
         }
      }

      return null;
   }

   public void m_7332_(Entity passenger) {
      if (this.m_20363_(passenger)) {
         passenger.m_183634_();
         float radius = 0.25F;
         float angle = 0.017453292F * this.f_20883_;
         double extraX = (double)(radius * Mth.m_14031_((float)(3.141592653589793D + (double)angle)));
         double extraZ = (double)(radius * Mth.m_14089_(angle));
         double yOffset = this.m_20186_() + 0.25D + this.m_6048_() + passenger.m_6049_();
         passenger.m_6034_(this.m_20185_() + extraX, yOffset, this.m_20189_() + extraZ);
      }
   }

   public void m_7888_(int pJumpPower) {
      if (pJumpPower >= 90) {
         this.playerJumpPendingScale = 1.0F;
      } else {
         if (pJumpPower < 0) {
            pJumpPower = 0;
         }

         this.playerJumpPendingScale = 0.4F + 0.4F * (float)pJumpPower / 90.0F;
      }

   }

   public void m_7199_(int pJumpPower) {
      if (this.m_20096_()) {
         this.playJumpSound();
      }

   }

   public void m_8012_() {
   }

   public void m_7023_(Vec3 pTravelVector) {
      if (this.m_6084_()) {
         LivingEntity controller = this.getControllingPassenger();
         if (this.m_20160_() && controller != null) {
            this.m_146922_(controller.m_146908_());
            this.f_19859_ = this.m_146908_();
            this.m_146926_(controller.m_146909_() * 0.5F);
            this.m_19915_(this.m_146908_(), this.m_146909_());
            this.f_20883_ = this.m_146908_();
            this.f_20885_ = this.f_20883_;
            float f = controller.f_20900_ * 0.5F;
            float f1 = controller.f_20902_;
            if (f1 <= 0.0F) {
               f1 *= 0.25F;
            }

            if (this.playerJumpPendingScale > 0.0F && !this.isPlayerJumping() && this.m_20096_()) {
               double d0 = this.getCustomJump() * (double)this.playerJumpPendingScale * (double)this.m_20098_();
               double d1 = d0 + this.m_182332_();
               Vec3 vec3 = this.m_20184_();
               this.m_20334_(vec3.f_82479_, d1, vec3.f_82481_);
               this.setPlayerJumping(true);
               this.f_19812_ = true;
               ForgeHooks.onLivingJump(this);
               if (f1 > 0.0F) {
                  float f2 = Mth.m_14031_(this.m_146908_() * 0.017453292F);
                  float f3 = Mth.m_14089_(this.m_146908_() * 0.017453292F);
                  this.m_20256_(this.m_20184_().m_82520_((double)(-0.4F * f2 * this.playerJumpPendingScale), 0.0D, (double)(0.4F * f3 * this.playerJumpPendingScale)));
               }

               this.playerJumpPendingScale = 0.0F;
            }

            this.f_20887_ = this.m_6113_() * 0.1F;
            if (this.m_6109_()) {
               float speed = (float)this.m_21133_(Attributes.f_22279_);
               if (controller.m_20142_()) {
                  speed = (float)((double)speed * 2.5D);
               }

               this.m_7910_(speed);
               if (this.isInFluidType((fluidType, height) -> {
                  return height > this.m_20204_();
               }) && f1 > 0.0F) {
                  this.m_20256_(this.m_20184_().m_82520_(0.0D, 0.03D, 0.0D));
               }

               super.m_7023_(new Vec3((double)f, pTravelVector.f_82480_, (double)f1));
            } else if (controller instanceof Player) {
               this.m_20256_(Vec3.f_82478_);
            }

            if (this.f_19861_) {
               this.playerJumpPendingScale = 0.0F;
               this.setPlayerJumping(false);
            }

            this.m_146872_();
         } else {
            this.f_20887_ = 0.02F;
            super.m_7023_(pTravelVector);
         }
      }

   }

   protected void m_5907_() {
      super.m_5907_();
      if (this.isSaddled() && !this.m_9236_().m_5776_()) {
         this.m_19998_((ItemLike)TensuraMaterialItems.MONSTER_SADDLE.get());
      }

      if (this.isChested()) {
         if (!this.f_19853_.f_46443_) {
            this.m_19998_(Blocks.f_50087_);

            for(int i = 0; i < this.inventory.m_6643_(); ++i) {
               this.m_19983_(this.inventory.m_8020_(i));
            }
         }

         this.inventory.m_6211_();
         this.setChested(false);
      }

   }

   protected SoundEvent m_7515_() {
      return this.m_6162_() ? SoundEvents.f_12281_ : SoundEvents.f_12280_;
   }

   protected SoundEvent m_7975_(DamageSource pDamageSource) {
      return SoundEvents.f_12283_;
   }

   protected SoundEvent m_5592_() {
      return SoundEvents.f_12282_;
   }

   public SoundSource m_5720_() {
      return SoundSource.HOSTILE;
   }

   protected void playJumpSound() {
      this.m_5496_((SoundEvent)TensuraSoundEvents.SMALL_JUMP_IMPACT.get(), 0.4F, 1.0F);
   }

   private <E extends IAnimatable> PlayState predicate(AnimationEvent<E> event) {
      if (this.m_5803_()) {
         event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.blade_tiger.sleep", EDefaultLoopTypes.LOOP));
         return PlayState.CONTINUE;
      } else if (this.m_21825_()) {
         event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.blade_tiger.sit", EDefaultLoopTypes.LOOP));
         return PlayState.CONTINUE;
      } else {
         if (event.isMoving()) {
            if (this.m_20072_() || !this.m_21660_() && (this.getControllingPassenger() == null || !this.getControllingPassenger().m_20142_())) {
               event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.blade_tiger.walk", EDefaultLoopTypes.LOOP));
            } else {
               event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.blade_tiger.run", EDefaultLoopTypes.LOOP));
            }
         } else {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.blade_tiger.idle", EDefaultLoopTypes.LOOP));
         }

         return PlayState.CONTINUE;
      }
   }

   private <E extends IAnimatable> PlayState miscPredicate(AnimationEvent<E> event) {
      if (event.getController().getAnimationState().equals(AnimationState.Stopped)) {
         event.getController().markNeedsReload();
         if (this.getMiscAnimation() == 1) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.blade_tiger.eat", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 2) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.blade_tiger.strike", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 3) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.blade_tiger.tail_swing", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 4) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.blade_tiger.tail_pierce", EDefaultLoopTypes.PLAY_ONCE));
         } else if (this.getMiscAnimation() == 5) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.blade_tiger.eat", EDefaultLoopTypes.PLAY_ONCE));
         }
      }

      return PlayState.CONTINUE;
   }

   public void registerControllers(AnimationData data) {
      data.addAnimationController(new AnimationController(this, "controller", 0.0F, this::predicate));
      data.addAnimationController(new AnimationController(this, "miscController", 0.0F, this::miscPredicate));
   }

   public AnimationFactory getFactory() {
      return this.factory;
   }

   public boolean isPlayerJumping() {
      return this.playerJumping;
   }

   public void setPlayerJumping(boolean playerJumping) {
      this.playerJumping = playerJumping;
   }

   static {
      MISC_ANIMATION = SynchedEntityData.m_135353_(BladeTigerEntity.class, EntityDataSerializers.f_135028_);
      SADDLED = SynchedEntityData.m_135353_(BladeTigerEntity.class, EntityDataSerializers.f_135035_);
      CHESTED = SynchedEntityData.m_135353_(BladeTigerEntity.class, EntityDataSerializers.f_135035_);
      WHITE = SynchedEntityData.m_135353_(BladeTigerEntity.class, EntityDataSerializers.f_135035_);
   }

   class BladeTigerAttackGoal extends MeleeAttackGoal {
      private final BladeTigerEntity tiger = BladeTigerEntity.this;

      public BladeTigerAttackGoal() {
         super(BladeTigerEntity.this, 2.5D, true);
      }

      public boolean m_8036_() {
         return this.tiger.m_21827_() ? false : super.m_8036_();
      }

      public boolean m_8045_() {
         return this.tiger.m_21827_() ? false : super.m_8045_();
      }

      public void m_8037_() {
         if (this.tiger.getMiscAnimation() == 0) {
            super.m_8037_();
         }

      }

      protected void m_6739_(LivingEntity pEnemy, double pDistToEnemySqr) {
         double distance = this.m_6639_(pEnemy);
         if (this.tiger.getMiscAnimation() == 0) {
            int randomAttack = this.randomAttack(pEnemy, pDistToEnemySqr);
            double var10000;
            switch(randomAttack) {
            case 3:
               this.tiger.m_21573_().m_26573_();
               var10000 = 25.0D;
               break;
            case 4:
               var10000 = distance + 9.0D;
               break;
            case 5:
               var10000 = 400.0D;
               break;
            default:
               var10000 = distance;
            }

            double attackRange = var10000;
            if (pDistToEnemySqr <= attackRange && this.m_25564_()) {
               this.m_25563_();
               this.tiger.setMiscAnimation(randomAttack);
               if (randomAttack == 1) {
                  this.tiger.m_7327_(pEnemy);
               }
            }
         }

      }

      protected int randomAttack(LivingEntity target, double distance) {
         if (this.tiger.f_19796_.m_188503_(7) == 2 && distance >= 36.0D) {
            return 5;
         } else if (this.tiger.f_19796_.m_188503_(5) == 2 && distance <= 30.0D) {
            return 3;
         } else if ((double)this.tiger.f_19796_.m_188501_() <= 0.4D && this.m_6639_(target) + 9.0D >= distance && this.tiger.getControllingPassenger() == null) {
            return 4;
         } else {
            return this.tiger.f_19796_.m_188503_(10) != 1 ? 2 : 1;
         }
      }

      protected double m_6639_(LivingEntity pAttackTarget) {
         return (double)(this.f_25540_.m_20205_() * this.f_25540_.m_20205_() * 3.0F + pAttackTarget.m_20205_());
      }
   }
}
