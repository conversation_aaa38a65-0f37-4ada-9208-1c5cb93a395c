package com.github.manasmods.tensura.entity.magic.projectile;

import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.entity.magic.TensuraProjectile;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import java.util.Optional;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.level.Level;

public class WindBladeProjectile extends TensuraProjectile {
   public WindBladeProjectile(EntityType<? extends WindBladeProjectile> entityType, Level level) {
      super(entityType, level);
   }

   public WindBladeProjectile(Level levelIn, LivingEntity shooter) {
      super((EntityType)TensuraEntityTypes.WIND_BLADE.get(), levelIn);
      this.m_5602_(shooter);
   }

   public String getMagic() {
      return "tensura.wind_attack";
   }

   public boolean shouldDiscardInWater() {
      return false;
   }

   public ResourceLocation[] getTextureLocation() {
      return new ResourceLocation[]{new ResourceLocation("tensura", "textures/entity/projectiles/wind_blade/wind_blade_0.png"), new ResourceLocation("tensura", "textures/entity/projectiles/wind_blade/wind_blade_1.png"), new ResourceLocation("tensura", "textures/entity/projectiles/wind_blade/wind_blade_2.png"), new ResourceLocation("tensura", "textures/entity/projectiles/wind_blade/wind_blade_3.png"), new ResourceLocation("tensura", "textures/entity/projectiles/wind_blade/wind_blade_4.png"), new ResourceLocation("tensura", "textures/entity/projectiles/wind_blade/wind_blade_5.png"), new ResourceLocation("tensura", "textures/entity/projectiles/wind_blade/wind_blade_6.png"), new ResourceLocation("tensura", "textures/entity/projectiles/wind_blade/wind_blade_7.png")};
   }

   public Optional<SoundEvent> hitSound() {
      return Optional.of(SoundEvents.f_12317_);
   }

   public void hitParticles(double x, double y, double z) {
      TensuraParticleHelper.spawnServerParticles(this.f_19853_, (ParticleOptions)TensuraParticles.SMALL_GUST.get(), x, y, z, 20, 0.1D, 0.1D, 0.1D, 0.15D, true);
   }

   public void flyingParticles() {
   }
}
