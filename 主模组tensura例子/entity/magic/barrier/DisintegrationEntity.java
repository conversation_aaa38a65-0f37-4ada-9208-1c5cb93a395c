package com.github.manasmods.tensura.entity.magic.barrier;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.network.TensuraNetwork;
import com.github.manasmods.tensura.network.play2client.RequestFxSpawningPacket;
import com.github.manasmods.tensura.race.RaceHelper;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.skill.ResistanceSkills;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import java.util.Iterator;
import javax.annotation.Nullable;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.util.Mth;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.item.ItemEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.network.PacketDistributor;

public class DisintegrationEntity extends BarrierEntity {
   public DisintegrationEntity(Level level, LivingEntity entity) {
      this((EntityType)TensuraEntityTypes.DISINTEGRATION.get(), level);
      this.m_5602_(entity);
   }

   public DisintegrationEntity(EntityType<? extends DisintegrationEntity> entityType, Level level) {
      super(entityType, level);
   }

   public boolean canWalkThrough() {
      return true;
   }

   public boolean canWalkThrough(Entity entity) {
      Entity owner = this.m_37282_();
      return owner != null && (entity.m_7307_(owner) || entity == owner);
   }

   public boolean m_6469_(DamageSource pSource, float pAmount) {
      return false;
   }

   public void onAddedToWorld() {
      super.onAddedToWorld();
      TensuraNetwork.INSTANCE.send(PacketDistributor.TRACKING_ENTITY_AND_SELF.with(() -> {
         return this;
      }), new RequestFxSpawningPacket(new ResourceLocation("tensura:disintegration"), this.m_19879_(), 0.0D, 0.5D, 0.0D, false, true));
   }

   public void m_8119_() {
      super.m_8119_();
      if (!this.f_19853_.m_5776_()) {
         if (this.m_37282_() == null) {
            this.m_146870_();
         }

         this.m_5496_(SoundEvents.f_11737_, 5.0F, 0.95F + this.f_19796_.m_188501_() * 0.1F);
         if (this.f_19797_ >= 250) {
            this.m_5496_(SoundEvents.f_11913_, 5.0F, 0.95F + this.f_19796_.m_188501_() * 0.1F);
         }

      }
   }

   protected void hitTarget() {
      super.hitTarget();
      Iterator var1 = this.m_9236_().m_45976_(ItemEntity.class, this.getAffectedArea()).iterator();

      while(var1.hasNext()) {
         ItemEntity target = (ItemEntity)var1.next();
         target.m_6469_(TensuraDamageSources.HOLY_DAMAGE, 100.0F);
      }

   }

   public void applyEffect(LivingEntity entity) {
      if (entity != this.m_37282_()) {
         if (entity instanceof Player) {
            Player player = (Player)entity;
            if (player.m_7500_() || player.m_5833_()) {
               return;
            }
         }

         if (this.f_19797_ < this.getAstraBindTick(entity)) {
            entity.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.MOVEMENT_INTERFERENCE.get(), 20, 9, false, false, false));
            entity.m_20219_(this.m_20182_());
            entity.m_20256_(Vec3.f_82478_);
            entity.f_19864_ = true;
         } else {
            entity.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.MOVEMENT_INTERFERENCE.get(), 20, 4, false, false, false));
         }

         if (this.f_19797_ >= 250) {
            if (entity.m_6469_(getDisintegrationDamage(this.m_37282_()), entity.m_21233_())) {
               entity.m_20254_(5);
            }

         }
      }
   }

   private int getAstraBindTick(LivingEntity entity) {
      double tick = 300.0D;
      if (RaceHelper.isSpiritualLifeForm(entity)) {
         tick -= tick * 0.25D;
      }

      if (SkillUtils.isSkillToggled(entity, (ManasSkill)ResistanceSkills.SPIRITUAL_ATTACK_NULLIFICATION.get())) {
         tick -= tick * 0.5D;
      } else if (SkillUtils.isSkillToggled(entity, (ManasSkill)ResistanceSkills.SPIRITUAL_ATTACK_RESISTANCE.get())) {
         tick -= tick * 0.25D;
      }

      return Mth.m_14165_(tick);
   }

   public static DamageSource getDisintegrationDamage(@Nullable Entity entity) {
      DamageSource source = entity == null ? TensuraDamageSources.HOLY_DAMAGE : TensuraDamageSources.holyDamage(entity);
      return DamageSourceHelper.turnTensura(source).setNoKnock().setIgnoreBarrier(3.0F).m_238403_().m_19382_().m_19381_();
   }
}
