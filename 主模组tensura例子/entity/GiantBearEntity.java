package com.github.manasmods.tensura.entity;

import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.api.entity.ai.TamableFollowParentGoal;
import com.github.manasmods.tensura.api.entity.ai.WanderingFollowOwnerGoal;
import com.github.manasmods.tensura.api.entity.subclass.ITensuraMount;
import com.github.manasmods.tensura.config.SpawnRateConfig;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.entity.projectile.KunaiProjectile;
import com.github.manasmods.tensura.entity.projectile.SpearProjectile;
import com.github.manasmods.tensura.entity.template.TensuraTamableEntity;
import com.github.manasmods.tensura.item.food.HealingPotionItem;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.sound.TensuraSoundEvents;
import java.util.Iterator;
import java.util.List;
import java.util.Random;
import java.util.UUID;
import java.util.function.Predicate;
import javax.annotation.Nullable;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.syncher.EntityDataAccessor;
import net.minecraft.network.syncher.EntityDataSerializers;
import net.minecraft.network.syncher.SynchedEntityData;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.util.Mth;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.InteractionResult;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.AgeableMob;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityDimensions;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.MobSpawnType;
import net.minecraft.world.entity.PlayerRideableJumping;
import net.minecraft.world.entity.Pose;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.AttributeSupplier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.goal.BreedGoal;
import net.minecraft.world.entity.ai.goal.FloatGoal;
import net.minecraft.world.entity.ai.goal.LookAtPlayerGoal;
import net.minecraft.world.entity.ai.goal.MeleeAttackGoal;
import net.minecraft.world.entity.ai.goal.RandomLookAroundGoal;
import net.minecraft.world.entity.ai.goal.SitWhenOrderedToGoal;
import net.minecraft.world.entity.ai.goal.target.NearestAttackableTargetGoal;
import net.minecraft.world.entity.ai.goal.target.NonTameRandomTargetGoal;
import net.minecraft.world.entity.ai.goal.target.ResetUniversalAngerTargetGoal;
import net.minecraft.world.entity.animal.Animal;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.entity.projectile.Arrow;
import net.minecraft.world.entity.projectile.ThrownTrident;
import net.minecraft.world.food.FoodProperties;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.LevelAccessor;
import net.minecraft.world.phys.AABB;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.common.ForgeHooks;
import net.minecraftforge.common.ForgeMod;
import net.minecraftforge.event.ForgeEventFactory;
import software.bernie.geckolib3.core.AnimationState;
import software.bernie.geckolib3.core.IAnimatable;
import software.bernie.geckolib3.core.PlayState;
import software.bernie.geckolib3.core.builder.AnimationBuilder;
import software.bernie.geckolib3.core.builder.ILoopType.EDefaultLoopTypes;
import software.bernie.geckolib3.core.controller.AnimationController;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.manager.AnimationData;
import software.bernie.geckolib3.core.manager.AnimationFactory;
import software.bernie.geckolib3.util.GeckoLibUtil;

public class GiantBearEntity extends TensuraTamableEntity implements IAnimatable, ITensuraMount, PlayerRideableJumping {
   private static final EntityDataAccessor<Integer> MISC_ANIMATION;
   private final AnimationFactory factory = GeckoLibUtil.createFactory(this);
   private boolean prevSprint;
   public int miscAnimationTicks = 0;
   protected float playerJumpPendingScale;
   protected boolean playerJumping;

   public GiantBearEntity(EntityType<? extends GiantBearEntity> pEntityType, Level pLevel) {
      super(pEntityType, pLevel);
      this.f_21364_ = 40;
      this.f_19793_ = 1.0F;
   }

   public static AttributeSupplier setAttributes() {
      return Mob.m_21552_().m_22268_(Attributes.f_22281_, 18.0D).m_22268_(Attributes.f_22276_, 40.0D).m_22268_(Attributes.f_22279_, 0.20000000298023224D).m_22268_(Attributes.f_22277_, 32.0D).m_22268_(Attributes.f_22284_, 8.0D).m_22268_(Attributes.f_22278_, 0.4000000059604645D).m_22268_(Attributes.f_22288_, 1.0D).m_22268_((Attribute)ForgeMod.SWIM_SPEED.get(), 2.0D).m_22265_();
   }

   protected void m_8099_() {
      this.f_21345_.m_25352_(0, new FloatGoal(this));
      this.f_21345_.m_25352_(1, new SitWhenOrderedToGoal(this));
      this.f_21345_.m_25352_(3, new GiantBearEntity.GiantBearAttackGoal(this));
      this.f_21345_.m_25352_(4, new WanderingFollowOwnerGoal(this, 1.5D, 10.0F, 5.0F, false));
      this.f_21345_.m_25352_(5, new BreedGoal(this, 1.2D, GiantBearEntity.class));
      this.f_21345_.m_25352_(6, new TamableFollowParentGoal(this, 1.5D));
      this.f_21345_.m_25352_(7, new TensuraTamableEntity.WanderAroundPosGoal(this));
      this.f_21345_.m_25352_(8, new RandomLookAroundGoal(this));
      this.f_21345_.m_25352_(9, new LookAtPlayerGoal(this, Player.class, 6.0F));
      this.f_21346_.m_25352_(1, new TensuraTamableEntity.TensuraOwnerHurtByTargetGoal(this));
      this.f_21346_.m_25352_(2, new TensuraTamableEntity.TensuraOwnerHurtTargetGoal(this));
      this.f_21346_.m_25352_(3, (new TensuraTamableEntity.TensuraHurtByTargetGoal(this)).m_26044_(new Class[0]));
      this.f_21346_.m_25352_(3, new GiantBearEntity.GiantBearAttackPlayersGoal());
      this.f_21346_.m_25352_(4, new NearestAttackableTargetGoal(this, Player.class, 10, true, false, this::m_21674_));
      this.f_21346_.m_25352_(4, new NonTameRandomTargetGoal(this, Player.class, false, (e) -> {
         return this.m_21223_() < this.m_21233_() / 2.0F;
      }));
      this.f_21346_.m_25352_(4, new NonTameRandomTargetGoal(this, Animal.class, false, (e) -> {
         return e.m_6095_().m_204039_(TensuraTags.EntityTypes.ANIMAL_PREY) && this.m_21223_() < this.m_21233_() / 2.0F;
      }));
      this.f_21346_.m_25352_(8, new ResetUniversalAngerTargetGoal(this, true));
   }

   protected void m_8097_() {
      super.m_8097_();
      this.f_19804_.m_135372_(MISC_ANIMATION, 0);
   }

   public void m_7380_(CompoundTag compound) {
      super.m_7380_(compound);
      compound.m_128405_("MiscAnimation", this.getMiscAnimation());
   }

   public void m_7378_(CompoundTag compound) {
      super.m_7378_(compound);
      this.f_19804_.m_135381_(MISC_ANIMATION, compound.m_128451_("MiscAnimation"));
   }

   public int getMiscAnimation() {
      return (Integer)this.f_19804_.m_135370_(MISC_ANIMATION);
   }

   public void setMiscAnimation(int animation) {
      if (this.getMiscAnimation() == 0 || animation == 0) {
         this.f_19804_.m_135381_(MISC_ANIMATION, animation);
      }
   }

   public void m_7350_(EntityDataAccessor<?> pKey) {
      if (f_21798_.equals(pKey)) {
         this.m_6210_();
      }

      super.m_7350_(pKey);
   }

   protected boolean m_8028_() {
      return false;
   }

   public boolean m_5545_(LevelAccessor pLevel, MobSpawnType pSpawnReason) {
      return SpawnRateConfig.rollSpawn((Integer)SpawnRateConfig.INSTANCE.giantBearSpawnRate.get(), this.m_217043_(), pSpawnReason) && super.m_5545_(pLevel, pSpawnReason);
   }

   public AgeableMob m_142606_(ServerLevel pLevel, AgeableMob pOtherParent) {
      GiantBearEntity bear = (GiantBearEntity)((EntityType)TensuraEntityTypes.GIANT_BEAR.get()).m_20615_(pLevel);
      if (bear == null) {
         return null;
      } else {
         UUID uuid = this.m_21805_();
         if (uuid != null) {
            bear.m_21816_(uuid);
            bear.m_7105_(true);
         }

         return bear;
      }
   }

   public boolean m_6898_(ItemStack pStack) {
      if (pStack.m_204117_(TensuraTags.Items.BEAR_TAMING_FOOD)) {
         return false;
      } else {
         FoodProperties food = pStack.getFoodProperties(this);
         return food != null && food.m_38746_();
      }
   }

   public boolean isPassengerSprint() {
      return this.getControllingPassenger() != null && this.getControllingPassenger().m_20142_();
   }

   public EntityDimensions m_6972_(Pose pPose) {
      EntityDimensions entitydimensions = super.m_6972_(pPose);
      if (this.isPassengerSprint()) {
         return EntityDimensions.m_20398_(entitydimensions.f_20377_, entitydimensions.f_20378_ * 4.0F / 5.0F);
      } else {
         return this.m_21825_() ? EntityDimensions.m_20398_(entitydimensions.f_20377_, entitydimensions.f_20378_ * 2.0F / 3.0F) : entitydimensions;
      }
   }

   public boolean m_6673_(DamageSource source) {
      if (!(source.m_7640_() instanceof Arrow) && !(source.m_7640_() instanceof SpearProjectile) && !(source.m_7640_() instanceof ThrownTrident) && !(source.m_7640_() instanceof KunaiProjectile)) {
         return source == DamageSource.f_19314_ || source == DamageSource.f_19325_ || super.m_6673_(source);
      } else {
         this.setMiscAnimation(6);
         return true;
      }
   }

   public boolean m_142535_(float pFallDistance, float pMultiplier, DamageSource pSource) {
      if (pFallDistance < 6.0F) {
         return false;
      } else {
         int i = this.m_5639_(pFallDistance - 7.0F, pMultiplier);
         if (i <= 0) {
            return false;
         } else {
            this.m_6469_(pSource, (float)i);
            this.m_21229_();
            return true;
         }
      }
   }

   public void m_8119_() {
      super.m_8119_();
      if (this.prevSprint != this.isPassengerSprint()) {
         this.prevSprint = this.isPassengerSprint();
         this.m_6210_();
      }

      if (this.getMiscAnimation() != 0) {
         ++this.miscAnimationTicks;
         if (this.miscAnimationTicks >= 11) {
            this.setMiscAnimation(0);
            this.miscAnimationTicks = 0;
         }
      }

   }

   public void mountAbility(Player rider) {
      if (this.getMiscAnimation() != 3) {
         if (this.getMiscAnimation() != 4) {
            if (this.getMiscAnimation() != 5) {
               if (!this.m_6162_()) {
                  LivingEntity target = SkillHelper.getTargetingEntity(rider, 6.0D, false);
                  if (target != null) {
                     this.setMiscAnimation(5);
                     this.m_7327_(target);
                  }
               }
            }
         }
      }
   }

   public void areaAttack(int animType) {
      float multiply = animType == 4 ? 1.0F : 1.5F;
      AABB aabb = this.m_20191_().m_82400_(4.0D);
      List<LivingEntity> livingEntityList = this.f_19853_.m_6443_(LivingEntity.class, aabb, (entity) -> {
         return !entity.m_7307_(this) && !entity.equals(this.m_21826_()) && !entity.equals(this) && (!(entity instanceof GiantBearEntity) || entity == this.m_5448_());
      });
      if (!livingEntityList.isEmpty()) {
         Iterator var5 = livingEntityList.iterator();

         while(var5.hasNext()) {
            LivingEntity target = (LivingEntity)var5.next();
            target.m_6469_(DamageSource.m_19370_(target), (float)(this.m_21133_(Attributes.f_22281_) * (double)multiply));
            target.m_20184_().m_82520_(0.0D, 0.5D * (double)multiply, 0.0D);
         }

      }
   }

   public InteractionResult m_6071_(Player player, InteractionHand hand) {
      ItemStack itemstack = player.m_21120_(hand);
      if (itemstack.m_41720_() instanceof HealingPotionItem) {
         return super.m_6071_(player, hand);
      } else {
         InteractionResult eating = this.handleEating(player, hand, itemstack);
         if (eating.m_19077_()) {
            return eating;
         } else if (!player.m_36341_() && !this.m_6162_() && this.m_21830_(player)) {
            this.setMiscAnimation(2);
            if (this.m_21827_()) {
               this.m_21839_(false);
            }

            if (this.isWandering()) {
               this.setWandering(false);
            }

            if (!this.f_19853_.m_5776_()) {
               player.m_7998_(this, true);
            }

            return InteractionResult.m_19078_(this.f_19853_.m_5776_());
         } else if (this.f_19853_.f_46443_) {
            boolean flag = this.m_21830_(player) || this.m_21824_() || itemstack.m_204117_(TensuraTags.Items.BEAR_TAMING_FOOD);
            return flag ? InteractionResult.CONSUME : InteractionResult.PASS;
         } else {
            if (this.m_21824_()) {
               if (!super.m_6071_(player, hand).m_19077_() && this.m_21830_(player)) {
                  this.commanding(player);
                  return InteractionResult.SUCCESS;
               }
            } else if (itemstack.m_204117_(TensuraTags.Items.BEAR_TAMING_FOOD)) {
               if (!player.m_7500_()) {
                  itemstack.m_41774_(1);
               }

               if (this.f_19796_.m_188503_(10) == 0 && !ForgeEventFactory.onAnimalTame(this, player)) {
                  this.m_21828_(player);
                  this.f_21344_.m_26573_();
                  this.m_6710_((LivingEntity)null);
                  this.m_21839_(true);
                  this.f_19853_.m_7605_(this, (byte)7);
               } else {
                  if (this.f_19796_.m_188503_(20) == 0) {
                     this.m_6710_(player);
                  }

                  this.f_19853_.m_7605_(this, (byte)6);
               }

               return InteractionResult.SUCCESS;
            }

            return InteractionResult.PASS;
         }
      }
   }

   public InteractionResult handleEating(Player player, InteractionHand hand, ItemStack itemstack) {
      if (this.m_6898_(itemstack)) {
         if (this.m_21223_() < this.m_21233_()) {
            if (!player.m_7500_()) {
               itemstack.m_41774_(1);
            }

            this.m_5634_(3.0F);
            this.m_9236_().m_6269_((Player)null, this, (SoundEvent)TensuraSoundEvents.EATING.get(), SoundSource.NEUTRAL, 1.0F, 1.0F);
            this.setMiscAnimation(1);
            return InteractionResult.SUCCESS;
         }

         if (this.m_6162_()) {
            this.m_142075_(player, hand, itemstack);
            this.m_146740_(m_216967_(-this.m_146764_()), true);
            this.m_9236_().m_6269_(player, this, (SoundEvent)TensuraSoundEvents.EATING.get(), SoundSource.NEUTRAL, 1.0F, 1.0F);
            this.setMiscAnimation(1);
            return InteractionResult.m_19078_(this.f_19853_.f_46443_);
         }

         if (!this.f_19853_.f_46443_ && this.m_146764_() == 0 && this.m_5957_()) {
            this.m_142075_(player, hand, itemstack);
            this.m_27595_(player);
            return InteractionResult.SUCCESS;
         }
      }

      return InteractionResult.PASS;
   }

   public boolean m_7132_() {
      return this.m_20160_();
   }

   public double getCustomJump() {
      return this.m_21133_(Attributes.f_22288_);
   }

   @Nullable
   public LivingEntity getControllingPassenger() {
      Entity entity = this.m_146895_();
      return entity instanceof LivingEntity ? (LivingEntity)entity : null;
   }

   public void m_7888_(int pJumpPower) {
      if (pJumpPower >= 90) {
         this.playerJumpPendingScale = 1.0F;
      } else {
         if (pJumpPower < 0) {
            pJumpPower = 0;
         }

         this.playerJumpPendingScale = 0.4F + 0.4F * (float)pJumpPower / 90.0F;
      }

   }

   public void m_7199_(int pJumpPower) {
      if (this.m_20096_()) {
         this.playJumpSound();
      }
   }

   public void m_8012_() {
   }

   public void m_7023_(Vec3 pTravelVector) {
      if (this.m_6084_()) {
         LivingEntity livingentity = this.getControllingPassenger();
         if (this.m_20160_() && livingentity != null) {
            this.m_146922_(livingentity.m_146908_());
            this.f_19859_ = this.m_146908_();
            this.m_146926_(livingentity.m_146909_() * 0.5F);
            this.m_19915_(this.m_146908_(), this.m_146909_());
            this.f_20883_ = this.m_146908_();
            this.f_20885_ = this.f_20883_;
            float f = livingentity.f_20900_ * 0.5F;
            float f1 = livingentity.f_20902_;
            if (f1 <= 0.0F) {
               f1 *= 0.25F;
            }

            if (this.playerJumpPendingScale > 0.0F && !this.isPlayerJumping() && this.f_19861_) {
               double d0 = this.getCustomJump() * (double)this.playerJumpPendingScale * (double)this.m_20098_();
               double d1 = d0 + this.m_182332_();
               Vec3 vec3 = this.m_20184_();
               this.m_20334_(vec3.f_82479_, d1, vec3.f_82481_);
               this.setPlayerJumping(true);
               this.f_19812_ = true;
               ForgeHooks.onLivingJump(this);
               if (f1 > 0.0F) {
                  float f2 = Mth.m_14031_(this.m_146908_() * 0.017453292F);
                  float f3 = Mth.m_14089_(this.m_146908_() * 0.017453292F);
                  this.m_20256_(this.m_20184_().m_82520_((double)(-0.4F * f2 * this.playerJumpPendingScale), 0.0D, (double)(0.4F * f3 * this.playerJumpPendingScale)));
               }

               this.playerJumpPendingScale = 0.0F;
            }

            this.f_20887_ = this.m_6113_() * 0.1F;
            if (this.m_6109_()) {
               float speed = (float)this.m_21133_(Attributes.f_22279_);
               if (livingentity.m_20142_()) {
                  speed = (float)((double)speed * 1.5D);
               }

               this.m_7910_(speed);
               if (this.isInFluidType((fluidType, height) -> {
                  return height > this.m_20204_();
               }) && f1 > 0.0F) {
                  this.m_20256_(this.m_20184_().m_82520_(0.0D, 0.03D, 0.0D));
                  super.m_7023_(new Vec3((double)f, (double)livingentity.f_20901_, (double)f1));
               } else {
                  super.m_7023_(new Vec3((double)f, pTravelVector.f_82480_, (double)f1));
               }
            } else if (livingentity instanceof Player) {
               this.m_20256_(Vec3.f_82478_);
            }

            if (this.f_19861_) {
               this.playerJumpPendingScale = 0.0F;
               this.setPlayerJumping(false);
            }

            this.m_21043_(this, false);
            this.m_146872_();
         } else {
            this.f_20887_ = 0.02F;
            super.m_7023_(pTravelVector);
         }
      }

   }

   protected SoundEvent m_7515_() {
      return this.m_6162_() ? SoundEvents.f_12281_ : SoundEvents.f_12280_;
   }

   protected SoundEvent m_7975_(DamageSource pDamageSource) {
      if (this.getMiscAnimation() == 0) {
         this.setMiscAnimation(6);
      }

      return SoundEvents.f_12283_;
   }

   protected SoundEvent m_5592_() {
      return SoundEvents.f_12282_;
   }

   public SoundSource m_5720_() {
      return SoundSource.HOSTILE;
   }

   protected void playJumpSound() {
      this.m_5496_((SoundEvent)TensuraSoundEvents.SMALL_JUMP_IMPACT.get(), 0.4F, 1.0F);
   }

   private <E extends IAnimatable> PlayState predicate(AnimationEvent<E> event) {
      if (this.m_21825_()) {
         event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.giant_bear.sit", EDefaultLoopTypes.LOOP));
      } else if (event.isMoving()) {
         if ((!this.m_20096_() || !this.m_21660_()) && (this.getControllingPassenger() == null || !this.getControllingPassenger().m_20142_())) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.giant_bear.walk", EDefaultLoopTypes.LOOP));
         } else {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.giant_bear.run", EDefaultLoopTypes.LOOP));
         }
      } else if (this.m_21223_() <= this.m_21233_() / 4.0F) {
         event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.giant_bear.idle_hurt", EDefaultLoopTypes.LOOP));
      } else {
         event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.giant_bear.idle", EDefaultLoopTypes.LOOP));
      }

      return PlayState.CONTINUE;
   }

   private <E extends IAnimatable> PlayState miscPredicate(AnimationEvent<E> event) {
      if (event.getController().getAnimationState().equals(AnimationState.Stopped)) {
         event.getController().markNeedsReload();
         if (this.getMiscAnimation() == 1) {
            if (this.m_21825_()) {
               event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.giant_bear.block", EDefaultLoopTypes.PLAY_ONCE));
               return PlayState.CONTINUE;
            }

            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.giant_bear.eat", EDefaultLoopTypes.PLAY_ONCE));
         } else if (!this.m_21825_()) {
            if (this.getMiscAnimation() == 2) {
               event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.giant_bear.pick_up_item", EDefaultLoopTypes.PLAY_ONCE));
            } else if (this.getMiscAnimation() == 3) {
               event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.giant_bear.slam_both_hand", EDefaultLoopTypes.PLAY_ONCE));
            } else if (this.getMiscAnimation() == 4) {
               event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.giant_bear.slam_one_hand", EDefaultLoopTypes.PLAY_ONCE));
            } else if (this.getMiscAnimation() == 5) {
               event.getController().setAnimation((new AnimationBuilder()).addAnimation((new Random()).nextFloat() <= 0.5F ? "animation.giant_bear.right_punch" : "animation.giant_bear.left_punch", EDefaultLoopTypes.PLAY_ONCE));
            } else if (this.getMiscAnimation() == 6) {
               event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.giant_bear.block", EDefaultLoopTypes.PLAY_ONCE));
            }
         }
      }

      return PlayState.CONTINUE;
   }

   public void registerControllers(AnimationData data) {
      data.addAnimationController(new AnimationController(this, "controller", 0.0F, this::predicate));
      data.addAnimationController(new AnimationController(this, "miscController", 0.0F, this::miscPredicate));
   }

   public AnimationFactory getFactory() {
      return this.factory;
   }

   public void setPlayerJumping(boolean playerJumping) {
      this.playerJumping = playerJumping;
   }

   public boolean isPlayerJumping() {
      return this.playerJumping;
   }

   static {
      MISC_ANIMATION = SynchedEntityData.m_135353_(GiantBearEntity.class, EntityDataSerializers.f_135028_);
   }

   static class GiantBearAttackGoal extends MeleeAttackGoal {
      public final GiantBearEntity giantBear;

      public GiantBearAttackGoal(GiantBearEntity bear) {
         super(bear, 3.0D, true);
         this.giantBear = bear;
      }

      protected void m_6739_(LivingEntity pEnemy, double pDistToEnemySqr) {
         double d0 = this.m_6639_(pEnemy);
         if ((double)this.giantBear.f_19796_.m_188501_() >= 0.2D) {
            if (pDistToEnemySqr <= d0 && this.m_25564_()) {
               this.m_25563_();
               this.f_25540_.m_7327_(pEnemy);
               this.giantBear.setMiscAnimation(5);
            }
         } else {
            int slamAnim = (new Random()).nextFloat() <= 0.5F ? 4 : 3;
            if (pDistToEnemySqr <= d0 + 4.0D && this.m_25564_()) {
               this.m_25563_();
               this.f_25540_.f_19853_.m_5594_((Player)null, this.f_25540_.m_20183_(), SoundEvents.f_11913_, SoundSource.NEUTRAL, 1.0F, 1.0F);
               this.giantBear.areaAttack(slamAnim);
            } else if (pDistToEnemySqr <= d0 * 2.0D) {
               if (this.m_25564_()) {
                  this.m_25563_();
               }

               if (this.m_25565_() <= 150) {
                  this.giantBear.setMiscAnimation(slamAnim);
               }
            } else {
               this.m_25563_();
            }
         }

      }
   }

   class GiantBearAttackPlayersGoal extends NearestAttackableTargetGoal<Player> {
      public GiantBearAttackPlayersGoal() {
         super(GiantBearEntity.this, Player.class, 20, true, true, (Predicate)null);
      }

      public boolean m_8036_() {
         if (GiantBearEntity.this.m_21824_()) {
            return false;
         } else if (GiantBearEntity.this.m_6162_()) {
            return false;
         } else {
            if (super.m_8036_()) {
               Iterator var1 = GiantBearEntity.this.f_19853_.m_45976_(GiantBearEntity.class, GiantBearEntity.this.m_20191_().m_82377_(8.0D, 4.0D, 8.0D)).iterator();

               while(var1.hasNext()) {
                  GiantBearEntity bear = (GiantBearEntity)var1.next();
                  if (bear.m_6162_() && !bear.m_21824_()) {
                     return true;
                  }
               }
            }

            return false;
         }
      }

      protected double m_7623_() {
         return super.m_7623_() * 0.5D;
      }
   }
}
