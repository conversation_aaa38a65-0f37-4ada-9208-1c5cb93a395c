package com.github.manasmods.tensura.entity.variant;

import com.google.common.collect.Maps;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Map;
import net.minecraft.Util;
import net.minecraft.resources.ResourceLocation;

public enum HoundDogVariant {
   DEFAULT(0),
   EVOLVED(1);

   private static final HoundDogVariant[] BY_ID = (HoundDogVariant[])Arrays.stream(values()).sorted(Comparator.comparingInt(HoundDogVariant::getId)).toArray((x$0) -> {
      return new HoundDogVariant[x$0];
   });
   private final int id;
   public static final Map<HoundDogVariant, ResourceLocation> LOCATION_BY_VARIANT = (Map)Util.m_137469_(Maps.newEnumMap(HoundDogVariant.class), (variant) -> {
      variant.put(DEFAULT, new ResourceLocation("tensura", "textures/entity/hound_dog/hound_dog.png"));
      variant.put(EVOLVED, new ResourceLocation("tensura", "textures/entity/hound_dog/hound_dog_snake.png"));
   });

   private HoundDogVariant(int id) {
      this.id = id;
   }

   public int getId() {
      return this.id;
   }

   public static HoundDogVariant byId(int id) {
      return BY_ID[id % BY_ID.length];
   }

   // $FF: synthetic method
   private static HoundDogVariant[] $values() {
      return new HoundDogVariant[]{DEFAULT, EVOLVED};
   }
}
