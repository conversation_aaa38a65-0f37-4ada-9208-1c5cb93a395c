package com.github.manasmods.tensura.entity.variant;

import com.google.common.collect.Maps;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Map;
import net.minecraft.Util;
import net.minecraft.resources.ResourceLocation;

public enum OrcVariant {
   NORMAL(0),
   ROYAL(1),
   ROYAL_LORD(2);

   private static final OrcVariant[] BY_ID = (OrcVariant[])Arrays.stream(values()).sorted(Comparator.comparingInt(OrcVariant::getId)).toArray((x$0) -> {
      return new OrcVariant[x$0];
   });
   private final int id;
   public static final Map<OrcVariant, ResourceLocation> LOCATION_BY_VARIANT = (Map)Util.m_137469_(Maps.newEnumMap(OrcVariant.class), (variant) -> {
      variant.put(NORMAL, new ResourceLocation("tensura", "textures/entity/orc/orc.png"));
      variant.put(ROYAL, new ResourceLocation("tensura", "textures/entity/orc/orc_royal.png"));
      variant.put(ROYAL_LORD, new ResourceLocation("tensura", "textures/entity/orc/orc_royal.png"));
   });

   private OrcVariant(int id) {
      this.id = id;
   }

   public int getId() {
      return this.id;
   }

   public static OrcVariant byId(int id) {
      return BY_ID[id % BY_ID.length];
   }

   // $FF: synthetic method
   private static OrcVariant[] $values() {
      return new OrcVariant[]{NORMAL, ROYAL, ROYAL_LORD};
   }
}
