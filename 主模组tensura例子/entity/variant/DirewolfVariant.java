package com.github.manasmods.tensura.entity.variant;

import java.util.Arrays;
import java.util.Comparator;
import javax.annotation.Nullable;

public enum DirewolfVariant {
   DIREWOLF(0, "direwolf", "direwolf_boss", "direwolf_birthmark"),
   RED_FANG(1, "red_fang"),
   MYSTIC_FIRE_WOLF(2, "mystic_fire_wolf"),
   GREEN_FANG(3, "green_fang"),
   MYSTIC_WIND_WOLF(4, "mystic_wind_wolf"),
   BLUE_FANG(5, "blue_fang"),
   MYSTIC_WATER_WOLF(6, "mystic_water_wolf"),
   BROWN_FANG(7, "brown_fang"),
   MYSTIC_EARTH_WOLF(8, "mystic_earth_wolf"),
   PURPLE_FANG(9, "purple_fang"),
   MYSTIC_SPACE_WOLF(10, "mystic_space_wolf"),
   BLACK_FANG(11, "black_fang"),
   MYSTIC_WOLF(12, "mystic_wolf"),
   TEMPEST_WOLF(13, "tempest_wolf", "direwolf_boss", "direwolf_birthmark"),
   STAR_WOLF(14, "tempest_wolf", (String)null, "star_wolf"),
   TEMPEST_STAR_WOLF(15, "tempest_wolf", (String)null, "tempest_star_wolf");

   private static final DirewolfVariant[] BY_ID = (DirewolfVariant[])Arrays.stream(values()).sorted(Comparator.comparingInt(DirewolfVariant::getId)).toArray((x$0) -> {
      return new DirewolfVariant[x$0];
   });
   private final int id;
   private final String name;
   @Nullable
   private final String alphaName;
   @Nullable
   private final String birthmarkName;

   private DirewolfVariant(int id, String location) {
      this.id = id;
      this.name = location;
      this.alphaName = null;
      this.birthmarkName = null;
   }

   private DirewolfVariant(int id, String location, @Nullable String alphaName, @Nullable String birthmarkName) {
      this.id = id;
      this.name = location;
      this.alphaName = alphaName;
      this.birthmarkName = birthmarkName;
   }

   public static DirewolfVariant byId(int id) {
      return BY_ID[id % BY_ID.length];
   }

   public int getId() {
      return this.id;
   }

   public String getName() {
      return this.name;
   }

   @Nullable
   public String getAlphaName() {
      return this.alphaName;
   }

   @Nullable
   public String getBirthmarkName() {
      return this.birthmarkName;
   }

   // $FF: synthetic method
   private static DirewolfVariant[] $values() {
      return new DirewolfVariant[]{DIREWOLF, RED_FANG, MYSTIC_FIRE_WOLF, GREEN_FANG, MYSTIC_WIND_WOLF, BLUE_FANG, MYSTIC_WATER_WOLF, BROWN_FANG, MYSTIC_EARTH_WOLF, PURPLE_FANG, MYSTIC_SPACE_WOLF, BLACK_FANG, MYSTIC_WOLF, TEMPEST_WOLF, STAR_WOLF, TEMPEST_STAR_WOLF};
   }
}
