package com.github.manasmods.tensura.entity.variant;

import com.google.common.collect.Maps;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Map;
import net.minecraft.Util;
import net.minecraft.resources.ResourceLocation;

public enum HoverLizardVariant {
   YELLOW(0),
   GREEN(1),
   BLUE(2),
   RED(3),
   PURPLE(4);

   private static final HoverLizardVariant[] BY_ID = (HoverLizardVariant[])Arrays.stream(values()).sorted(Comparator.comparingInt(HoverLizardVariant::getId)).toArray((x$0) -> {
      return new HoverLizardVariant[x$0];
   });
   private final int id;
   public static final Map<HoverLizardVariant, ResourceLocation> LOCATION_BY_VARIANT = (Map)Util.m_137469_(Maps.newEnumMap(HoverLizardVariant.class), (variant) -> {
      variant.put(YELLOW, new ResourceLocation("tensura", "textures/entity/hover_lizard/hover_lizard.png"));
      variant.put(GREEN, new ResourceLocation("tensura", "textures/entity/hover_lizard/hover_lizard_green.png"));
      variant.put(BLUE, new ResourceLocation("tensura", "textures/entity/hover_lizard/hover_lizard_blue.png"));
      variant.put(RED, new ResourceLocation("tensura", "textures/entity/hover_lizard/hover_lizard_red.png"));
      variant.put(PURPLE, new ResourceLocation("tensura", "textures/entity/hover_lizard/hover_lizard_purple.png"));
   });

   private HoverLizardVariant(int id) {
      this.id = id;
   }

   public int getId() {
      return this.id;
   }

   public static HoverLizardVariant byId(int id) {
      return BY_ID[id % BY_ID.length];
   }

   // $FF: synthetic method
   private static HoverLizardVariant[] $values() {
      return new HoverLizardVariant[]{YELLOW, GREEN, BLUE, RED, PURPLE};
   }
}
