package com.github.manasmods.tensura.entity.human;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.manascore.attribute.ManasCoreAttributes;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.skill.unique.CookSkill;
import com.github.manasmods.tensura.ability.skill.unique.ReaperSkill;
import com.github.manasmods.tensura.api.entity.ai.CrossbowAttackGoal;
import com.github.manasmods.tensura.api.entity.ai.TamableFollowParentGoal;
import com.github.manasmods.tensura.api.entity.ai.WanderingFollowOwnerGoal;
import com.github.manasmods.tensura.api.race.AdvancedHitbox;
import com.github.manasmods.tensura.capability.effects.TensuraEffectsCapability;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.config.TensuraConfig;
import com.github.manasmods.tensura.effect.template.SkillMobEffect;
import com.github.manasmods.tensura.entity.template.HumanoidNPCEntity;
import com.github.manasmods.tensura.entity.template.TensuraTamableEntity;
import com.github.manasmods.tensura.race.Race;
import com.github.manasmods.tensura.race.RaceHelper;
import com.github.manasmods.tensura.registry.attribute.TensuraAttributeRegistry;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.skill.ExtraSkills;
import com.github.manasmods.tensura.registry.skill.IntrinsicSkills;
import com.github.manasmods.tensura.registry.skill.UniqueSkills;
import com.github.manasmods.tensura.registry.sound.TensuraSoundEvents;
import com.github.manasmods.tensura.util.attribute.TensuraAttributeHelper;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import net.minecraft.core.Registry;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.syncher.EntityDataAccessor;
import net.minecraft.network.syncher.EntityDataSerializers;
import net.minecraft.network.syncher.SynchedEntityData;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.InteractionResult;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityDimensions;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.Pose;
import net.minecraft.world.entity.Entity.RemovalReason;
import net.minecraft.world.entity.EquipmentSlot.Type;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.AttributeInstance;
import net.minecraft.world.entity.ai.attributes.AttributeModifier;
import net.minecraft.world.entity.ai.attributes.AttributeSupplier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.attributes.AttributeModifier.Operation;
import net.minecraft.world.entity.ai.goal.FloatGoal;
import net.minecraft.world.entity.ai.goal.LookAtPlayerGoal;
import net.minecraft.world.entity.ai.goal.RandomLookAroundGoal;
import net.minecraft.world.entity.ai.goal.RangedBowAttackGoal;
import net.minecraft.world.entity.ai.goal.SitWhenOrderedToGoal;
import net.minecraft.world.entity.ai.goal.WaterAvoidingRandomStrollGoal;
import net.minecraft.world.entity.ai.goal.target.ResetUniversalAngerTargetGoal;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.level.ChunkPos;
import net.minecraft.world.level.Level;
import net.minecraftforge.common.ForgeMod;
import net.minecraftforge.common.util.ITeleporter;
import net.minecraftforge.registries.ForgeRegistries;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public class CloneEntity extends PlayerLikeEntity {
   private static final EntityDataAccessor<Integer> LIFE;
   private static final EntityDataAccessor<Float> HEIGHT;
   private static final EntityDataAccessor<Float> WIDTH;
   private static final EntityDataAccessor<String> SKILL;
   private static final EntityDataAccessor<Boolean> CHUNK_LOADER;
   private static final EntityDataAccessor<Boolean> IMMOBILE;
   private ChunkPos chunkPos = null;
   private int life = 0;
   private double size = 1.0D;

   public CloneEntity(EntityType<? extends CloneEntity> pEntityType, Level pLevel) {
      super(pEntityType, pLevel);
      this.f_21365_ = new CloneEntity.CloneLookControl();
      this.f_21342_ = new CloneEntity.CloneMoveControl();
      this.f_19793_ = 1.0F;
   }

   public static AttributeSupplier setAttributes() {
      return Mob.m_21552_().m_22268_(Attributes.f_22281_, 1.0D).m_22268_(Attributes.f_22284_, 0.0D).m_22268_(Attributes.f_22285_, 0.0D).m_22268_(Attributes.f_22276_, 20.0D).m_22268_(Attributes.f_22279_, 0.20000000298023224D).m_22268_(Attributes.f_22277_, 32.0D).m_22268_(Attributes.f_22278_, 0.0D).m_22268_((Attribute)ForgeMod.SWIM_SPEED.get(), 2.0D).m_22268_((Attribute)ForgeMod.ATTACK_RANGE.get(), 1.0D).m_22268_((Attribute)ForgeMod.NAMETAG_DISTANCE.get(), 64.0D).m_22268_((Attribute)TensuraAttributeRegistry.BARRIER.get(), 0.0D).m_22268_((Attribute)TensuraAttributeRegistry.SIZE.get(), 0.0D).m_22268_((Attribute)TensuraAttributeRegistry.MAX_SPIRITUAL_HEALTH.get(), 20.0D).m_22268_((Attribute)ManasCoreAttributes.CRIT_CHANCE.get(), 0.0D).m_22268_((Attribute)ManasCoreAttributes.CRIT_MULTIPLIER.get(), 0.0D).m_22268_((Attribute)ManasCoreAttributes.SWEEP_CHANCE.get(), 0.0D).m_22268_((Attribute)ManasCoreAttributes.JUMP_POWER.get(), 0.41999998688697815D).m_22265_();
   }

   protected void m_8099_() {
      this.f_21345_.m_25352_(0, new FloatGoal(this));
      this.f_21345_.m_25352_(1, new SitWhenOrderedToGoal(this));
      this.f_21345_.m_25352_(2, new HumanoidNPCEntity.EatingItemGoal(this, (entity) -> {
         return this.shouldHeal();
      }, 3.0F));
      this.f_21345_.m_25352_(3, new CrossbowAttackGoal(this, 1.2D, 20.0F));
      this.f_21345_.m_25352_(3, new RangedBowAttackGoal(this, 1.0D, 20, 20.0F));
      this.f_21345_.m_25352_(3, new HumanoidNPCEntity.SpearTypeAttackGoal(this, 1.0D, 20, 20.0F));
      this.f_21345_.m_25352_(3, new HumanoidNPCEntity.NPCMeleeAttackGoal(this, 2.0D, true));
      this.f_21345_.m_25352_(4, new WanderingFollowOwnerGoal(this, 1.5D, 10.0F, 5.0F, false));
      this.f_21345_.m_25352_(6, new TamableFollowParentGoal(this, 1.5D));
      this.f_21345_.m_25352_(7, new WaterAvoidingRandomStrollGoal(this, 1.2D));
      this.f_21345_.m_25352_(8, new RandomLookAroundGoal(this));
      this.f_21345_.m_25352_(9, new LookAtPlayerGoal(this, Player.class, 6.0F));
      this.f_21346_.m_25352_(1, new TensuraTamableEntity.TensuraOwnerHurtByTargetGoal(this));
      this.f_21346_.m_25352_(2, new TensuraTamableEntity.TensuraOwnerHurtTargetGoal(this));
      this.f_21346_.m_25352_(3, (new TensuraTamableEntity.TensuraHurtByTargetGoal(this, new Class[]{CloneEntity.class})).m_26044_(new Class[0]));
      this.f_21346_.m_25352_(8, new ResetUniversalAngerTargetGoal(this, true));
   }

   protected void m_8097_() {
      super.m_8097_();
      this.f_19804_.m_135372_(LIFE, -1);
      this.f_19804_.m_135372_(HEIGHT, 1.0F);
      this.f_19804_.m_135372_(WIDTH, 1.0F);
      this.f_19804_.m_135372_(SKILL, "tensura:none");
      this.f_19804_.m_135372_(CHUNK_LOADER, false);
      this.f_19804_.m_135372_(IMMOBILE, false);
   }

   public void m_7380_(CompoundTag compound) {
      super.m_7380_(compound);
      compound.m_128405_("Life", this.getLife());
      compound.m_128350_("Height", this.getHeight());
      compound.m_128350_("Width", this.getWidth());
      compound.m_128379_("ChunkLoader", this.isChunkLoader());
      compound.m_128379_("Immobile", this.m_6107_());
      compound.m_128359_("Skill", (String)this.f_19804_.m_135370_(SKILL));
   }

   public void m_7378_(CompoundTag compound) {
      super.m_7378_(compound);
      this.setLife(compound.m_128451_("Life"));
      this.setHeight(compound.m_128457_("Height"));
      this.setWidth(compound.m_128457_("Width"));
      this.setChunkLoader(compound.m_128471_("ChunkLoader"));
      this.setImmobile(compound.m_128471_("Immobile"));
      this.f_19804_.m_135381_(SKILL, compound.m_128461_("Skill"));
   }

   public int getLife() {
      return (Integer)this.f_19804_.m_135370_(LIFE);
   }

   public void setLife(int life) {
      this.f_19804_.m_135381_(LIFE, life);
   }

   public float getHeight() {
      return (Float)this.f_19804_.m_135370_(HEIGHT);
   }

   public void setHeight(float pSize) {
      this.f_19804_.m_135381_(HEIGHT, pSize);
   }

   public float getWidth() {
      return (Float)this.f_19804_.m_135370_(WIDTH);
   }

   public void setWidth(float pSize) {
      this.f_19804_.m_135381_(WIDTH, pSize);
   }

   public boolean isChunkLoader() {
      return (Boolean)this.f_19804_.m_135370_(CHUNK_LOADER);
   }

   public void setChunkLoader(boolean loader) {
      this.f_19804_.m_135381_(CHUNK_LOADER, loader);
   }

   public boolean m_6107_() {
      return (Boolean)this.f_19804_.m_135370_(IMMOBILE);
   }

   public void setImmobile(boolean immobile) {
      this.f_19804_.m_135381_(IMMOBILE, immobile);
   }

   @Nullable
   public ManasSkill getSkill() {
      ResourceLocation location = new ResourceLocation((String)this.f_19804_.m_135370_(SKILL));
      return (ManasSkill)SkillAPI.getSkillRegistry().getValue(location);
   }

   public void setSkill(@Nullable ManasSkill skill) {
      this.f_19804_.m_135381_(SKILL, SkillUtils.getSkillId(skill).toString());
   }

   public void m_7350_(EntityDataAccessor<?> pKey) {
      if (HEIGHT.equals(pKey) || WIDTH.equals(pKey)) {
         this.m_20090_();
         this.m_6210_();
      }

      super.m_7350_(pKey);
   }

   public void m_21828_(Player pPlayer) {
      super.m_21828_(pPlayer);
      if (TensuraEPCapability.isMajin(pPlayer)) {
         TensuraEPCapability.setMajin(this, true);
      }

   }

   @NotNull
   public EntityDimensions m_6972_(Pose pPose) {
      EntityDimensions entitydimensions = super.m_6972_(pPose).m_20388_(RaceHelper.getSkillSizeMultiplier(this)).m_20390_(this.getWidth(), this.getHeight());
      if (this.shouldSwim()) {
         return entitydimensions.m_20390_(1.0F, 0.25F);
      } else {
         return !this.m_21827_() && !this.m_21825_() ? entitydimensions : entitydimensions.m_20390_(1.0F, 0.75F);
      }
   }

   public boolean m_20067_() {
      return true;
   }

   public boolean canSleep() {
      LivingEntity owner = this.m_21826_();
      return owner == null ? false : owner.m_5803_();
   }

   public boolean m_7243_(ItemStack pStack) {
      return false;
   }

   public boolean m_6785_(double pDistanceToClosestPlayer) {
      return false;
   }

   protected void m_21226_() {
   }

   @Nullable
   public Entity changeDimension(ServerLevel level, ITeleporter teleporter) {
      return null;
   }

   public void m_8119_() {
      super.m_8119_();
      if (this.size != (double)RaceHelper.getSkillSizeMultiplier(this)) {
         this.m_20090_();
         this.m_6210_();
         this.size = (double)RaceHelper.getSkillSizeMultiplier(this);
      }

      if (!this.m_9236_().m_5776_()) {
         this.skillHandling();
         if (this.isChunkLoader()) {
            this.loadChunkHandler();
         }

         if (this.getLife() >= 0 && this.life++ >= this.getLife()) {
            this.m_146870_();
         }

      }
   }

   public void skillHandling() {
      if (this.getSkill() != null) {
         LivingEntity owner = this.m_21826_();
         if (owner == null || owner.m_213877_()) {
            return;
         }

         if (this.getSkill().equals(UniqueSkills.REAPER.get())) {
            AttributeInstance recon = (AttributeInstance)Objects.requireNonNull(owner.m_21051_((Attribute)TensuraAttributeRegistry.SIZE.get()));
            if (recon.m_22111_(ReaperSkill.REAPER) == null) {
               this.remove();
            }

            this.removeIfSkillLost(owner);
         } else if (this.getSkill().equals(UniqueSkills.UNYIELDING.get())) {
            this.m_146922_(owner.m_146908_());
            this.f_19859_ = this.m_146908_();
            this.m_146926_(owner.m_146909_());
            this.m_19915_(this.m_146908_(), this.m_146909_());
            this.f_20883_ = this.m_146908_();
            this.f_20885_ = this.f_20883_;
            this.removeIfSkillLost(owner);
         } else if (this.getSkill().equals(IntrinsicSkills.POSSESSION.get())) {
            if (this.getLife() > 0) {
               return;
            }

            Optional<ManasSkillInstance> possession = SkillAPI.getSkillsFrom(owner).getSkill(this.getSkill());
            if (possession.isEmpty()) {
               this.setLife((Integer)TensuraConfig.INSTANCE.skillsConfig.bodyDespawnTick.get() * 20);
            } else if (((ManasSkillInstance)possession.get()).getOrCreateTag().m_128403_("OriginalBody") && !Objects.equals(((ManasSkillInstance)possession.get()).getOrCreateTag().m_128342_("OriginalBody"), this.m_20148_())) {
               this.setLife((Integer)TensuraConfig.INSTANCE.skillsConfig.bodyDespawnTick.get() * 20);
            }
         } else if (this.getSkill().equals(ExtraSkills.BODY_DOUBLE.get())) {
            this.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.FRAGILITY.get(), 200, 1, false, false, false));
            this.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.ENERGY_BLOCKADE.get(), 200, 4, false, false, false));
            if (this.f_19797_ % 20 == 0 && this.m_21223_() < this.m_21233_() && this.m_6084_()) {
               this.m_5634_(2.0F);
               SkillHelper.reduceEP(this, (LivingEntity)null, 20.0D, false);
            }

            if (owner.m_20270_(this) > 50.0F) {
               this.m_21573_().m_26573_();
            }

            this.removeIfSkillLost(owner);
         }
      }

   }

   private void removeIfSkillLost(LivingEntity owner) {
      if (this.f_19797_ % 100 == 0) {
         if (this.getSkill() != null) {
            if (!SkillUtils.hasSkill(owner, this.getSkill())) {
               this.remove();
            }

         }
      }
   }

   public void loadChunkHandler() {
      Level var2 = this.m_9236_();
      if (var2 instanceof ServerLevel) {
         ServerLevel serverLevel = (ServerLevel)var2;
         ChunkPos chunk = new ChunkPos(this.m_20097_());
         if (this.chunkPos == null || !this.chunkPos.equals(chunk)) {
            if (this.chunkPos != null) {
               forceLoadChunks(this.chunkPos, serverLevel, false);
            }

            this.chunkPos = chunk;
            forceLoadChunks(chunk, serverLevel, true);
         }

      }
   }

   public void m_142687_(RemovalReason pReason) {
      super.m_142687_(pReason);
      if (pReason.m_146965_()) {
         if (this.isChunkLoader()) {
            if (this.chunkPos != null) {
               Level var3 = this.m_9236_();
               if (var3 instanceof ServerLevel) {
                  ServerLevel serverLevel = (ServerLevel)var3;
                  forceLoadChunks(this.chunkPos, serverLevel, false);
               }
            }

         }
      }
   }

   private static void forceLoadChunks(ChunkPos pos, ServerLevel pServerLevel, boolean add) {
      for(int i = -1; i < 2; ++i) {
         for(int j = -1; j < 2; ++j) {
            int k = pos.f_45578_ + i;
            int l = pos.f_45579_ + j;
            pServerLevel.m_8602_(k, l, add);
         }
      }

   }

   public void remove() {
      this.f_19853_.m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_11862_, SoundSource.PLAYERS, 1.0F, 1.0F);
      TensuraParticleHelper.addServerParticlesAroundSelf(this, ParticleTypes.f_123765_, 1.0D);
      TensuraParticleHelper.addServerParticlesAroundSelf(this, ParticleTypes.f_123765_, 2.0D);

      for(int i = 0; i < this.inventory.m_6643_(); ++i) {
         ItemStack itemstack = this.inventory.m_8020_(i);
         if (!itemstack.m_41619_()) {
            this.m_5552_(itemstack, 0.0F);
         }
      }

      this.m_146870_();
   }

   @NotNull
   public InteractionResult handleEating(Player player, InteractionHand hand, ItemStack stack) {
      if (this.m_6898_(stack) && this.m_21223_() < this.m_21233_()) {
         if (!player.m_7500_()) {
            stack.m_41774_(1);
         }

         this.m_8035_();
         this.m_9236_().m_6269_((Player)null, this, (SoundEvent)TensuraSoundEvents.EATING.get(), SoundSource.NEUTRAL, 1.0F, 1.0F);
         return InteractionResult.SUCCESS;
      } else {
         return InteractionResult.PASS;
      }
   }

   public void m_8035_() {
      super.m_8035_();
      this.m_5634_(3.0F);
   }

   public static void copyRotation(LivingEntity from, LivingEntity to) {
      to.m_146926_(from.m_146909_());
      to.m_146922_(from.m_146908_());
      to.m_5618_(from.f_20883_);
      to.m_5616_(from.f_20885_);
      to.m_146867_();
   }

   public static void copyEffects(LivingEntity from, LivingEntity to) {
      to.m_7311_(from.m_20094_());
      TensuraEffectsCapability.getFrom(from).ifPresent((fromCap) -> {
         TensuraEffectsCapability.getFrom(to).ifPresent((toCap) -> {
            toCap.setSeveranceAmount(fromCap.getSeveranceAmount());
            toCap.setSeveranceRemoveSec(fromCap.getSeveranceRemoveSec());
            Iterator var2 = Registry.f_122823_.iterator();

            while(var2.hasNext()) {
               MobEffect effect = (MobEffect)var2.next();
               ResourceLocation location = Registry.f_122823_.m_7981_(effect);
               if (location != null) {
                  toCap.setEffectSource(fromCap.getEffectSource(location), location);
               }
            }

         });
      });
      TensuraEffectsCapability.sync(to);
      to.m_6210_();
   }

   public static void copyAttributeModifiers(LivingEntity from, LivingEntity to) {
      Iterator var2 = ForgeRegistries.ATTRIBUTES.getValues().iterator();

      while(true) {
         AttributeInstance fromInstance;
         AttributeInstance toInstance;
         do {
            Attribute attribute;
            do {
               if (!var2.hasNext()) {
                  if (to.m_21223_() > to.m_21233_()) {
                     to.m_21153_(to.m_21233_());
                  }

                  return;
               }

               attribute = (Attribute)var2.next();
               fromInstance = from.m_21051_(attribute);
            } while(fromInstance == null);

            toInstance = to.m_21051_(attribute);
         } while(toInstance == null);

         Iterator var6 = fromInstance.m_22122_().iterator();

         while(var6.hasNext()) {
            AttributeModifier modifier = (AttributeModifier)var6.next();
            toInstance.m_22120_(modifier.m_22209_());
            toInstance.m_22125_(new AttributeModifier(modifier.m_22209_(), modifier.m_22214_(), modifier.m_22218_(), modifier.m_22217_()));
         }
      }
   }

   public void copyStatsAndSkills(LivingEntity owner, boolean copySize) {
      this.copyStatsAndSkills(owner, CloneEntity.CopySkill.ALL, copySize);
   }

   public void copyStatsAndSkills(LivingEntity owner, CloneEntity.CopySkill copySkill, boolean copySize) {
      this.m_6593_(owner.m_7755_());
      copyRotation(owner, this);
      Iterator var4 = ForgeRegistries.ATTRIBUTES.getValues().iterator();

      while(true) {
         Attribute attribute;
         AttributeInstance fromInstance;
         AttributeInstance toInstance;
         do {
            do {
               if (!var4.hasNext()) {
                  this.m_21153_(Math.max(0.0F, owner.m_21223_()));
                  var4 = owner.m_21220_().iterator();

                  while(true) {
                     MobEffectInstance instance;
                     do {
                        if (!var4.hasNext()) {
                           if (copySize) {
                              this.copySize(owner);
                           }

                           this.m_20090_();
                           this.m_6210_();
                           if (copySkill == CloneEntity.CopySkill.NONE) {
                              return;
                           }

                           var4 = List.copyOf(SkillAPI.getSkillsFrom(owner).getLearnedSkills()).iterator();

                           while(var4.hasNext()) {
                              ManasSkillInstance instance = (ManasSkillInstance)var4.next();
                              ManasSkillInstance skill = TensuraSkillInstance.fromNBT(instance.toNBT());
                              if (copySkill == CloneEntity.CopySkill.INTRINSIC) {
                                 if (skill.isTemporarySkill()) {
                                    SkillAPI.getSkillsFrom(this).learnSkill(skill);
                                 } else if (owner instanceof Player) {
                                    Player player = (Player)owner;
                                    if (TensuraPlayerCapability.getIntrinsicList(player).contains(SkillUtils.getSkillId(skill.getSkill()))) {
                                       SkillAPI.getSkillsFrom(this).learnSkill(skill);
                                    }
                                 }
                              } else {
                                 SkillAPI.getSkillsFrom(this).learnSkill(skill);
                              }
                           }

                           return;
                        }

                        instance = (MobEffectInstance)var4.next();
                     } while(copySkill != CloneEntity.CopySkill.ALL && instance.m_19544_() instanceof SkillMobEffect);

                     this.m_7292_(new MobEffectInstance(instance));
                  }
               }

               attribute = (Attribute)var4.next();
               fromInstance = owner.m_21051_(attribute);
            } while(fromInstance == null);

            toInstance = this.m_21051_(attribute);
         } while(toInstance == null);

         double base = fromInstance.m_22115_();
         if (attribute.equals(Attributes.f_22279_)) {
            base = base / 0.1D * 0.23D;
         }

         toInstance.m_22100_(base);
         Iterator var10 = fromInstance.m_22122_().iterator();

         while(var10.hasNext()) {
            AttributeModifier modifier = (AttributeModifier)var10.next();
            if (!TensuraAttributeHelper.ARMOR_MODIFIER_UUID_PER_SLOT.contains(modifier.m_22209_()) && !Objects.equals(modifier.m_22209_(), CookSkill.COOK)) {
               AttributeModifier modifierCopy = modifier;
               if (attribute.equals(Attributes.f_22279_) && modifier.m_22217_().equals(Operation.ADDITION)) {
                  modifierCopy = new AttributeModifier(modifier.m_22209_(), modifier.m_22214_(), modifier.m_22218_() / 0.1D * 0.23D, modifier.m_22217_());
               }

               toInstance.m_22125_(modifierCopy);
            }
         }
      }
   }

   public void copySize(LivingEntity owner) {
      if (owner instanceof Player) {
         Player player = (Player)owner;
         Race race = TensuraPlayerCapability.getRace(player);
         if (race != null) {
            float size = RaceHelper.getRaceSize(race);
            this.setHeight(size * TensuraEffectsCapability.getHeight(owner));
            this.setWidth(size);
            if (race instanceof AdvancedHitbox) {
               AdvancedHitbox advancedHitbox = (AdvancedHitbox)race;
               this.setHeight(this.getHeight() * advancedHitbox.getHitboxHeightModifier());
               this.setWidth(this.getWidth() * advancedHitbox.getHitboxWidthModifier());
            }

         }
      }
   }

   public void copyEquipments(LivingEntity owner) {
      EquipmentSlot[] var2 = EquipmentSlot.values();
      int var3 = var2.length;

      for(int var4 = 0; var4 < var3; ++var4) {
         EquipmentSlot slot = var2[var4];
         ItemStack stack = owner.m_6844_(slot).m_41777_();
         this.m_8061_(slot, stack);
         if (slot.m_20743_() == Type.ARMOR) {
            this.inventory.m_6836_(3 - slot.m_20749_(), stack);
         } else if (slot.equals(EquipmentSlot.MAINHAND)) {
            this.inventory.m_6836_(4, stack);
         } else {
            this.inventory.m_6836_(5, stack);
         }

         this.inventory.m_6596_();
      }

   }

   public void copyEquipmentsOntoOwner(LivingEntity owner, boolean forceReplace) {
      EquipmentSlot[] var3 = EquipmentSlot.values();
      int var4 = var3.length;

      for(int var5 = 0; var5 < var4; ++var5) {
         EquipmentSlot slot = var3[var5];
         int id = 4;
         if (slot.m_20743_() == Type.ARMOR) {
            id = 3 - slot.m_20749_();
         } else if (slot.equals(EquipmentSlot.OFFHAND)) {
            id = 5;
         }

         ItemStack stack = this.inventory.m_8020_(id);
         if (!forceReplace && !owner.m_6844_(slot).m_41619_()) {
            SkillHelper.dropItem(owner, owner.m_217043_(), stack, 0, 0.1F);
         } else {
            owner.m_8061_(slot, stack);
         }

         this.inventory.m_6836_(id, ItemStack.f_41583_);
         this.inventory.m_6596_();
      }

   }

   static {
      LIFE = SynchedEntityData.m_135353_(CloneEntity.class, EntityDataSerializers.f_135028_);
      HEIGHT = SynchedEntityData.m_135353_(CloneEntity.class, EntityDataSerializers.f_135029_);
      WIDTH = SynchedEntityData.m_135353_(CloneEntity.class, EntityDataSerializers.f_135029_);
      SKILL = SynchedEntityData.m_135353_(CloneEntity.class, EntityDataSerializers.f_135030_);
      CHUNK_LOADER = SynchedEntityData.m_135353_(CloneEntity.class, EntityDataSerializers.f_135035_);
      IMMOBILE = SynchedEntityData.m_135353_(CloneEntity.class, EntityDataSerializers.f_135035_);
   }

   public class CloneLookControl extends TensuraTamableEntity.SleepLookControl {
      public CloneLookControl() {
         super();
      }

      public void m_8128_() {
         if (!CloneEntity.this.m_6107_()) {
            super.m_8128_();
         }

      }
   }

   public class CloneMoveControl extends TensuraTamableEntity.SleepMoveControl {
      public CloneMoveControl() {
         super();
      }

      public void m_8126_() {
         if (!CloneEntity.this.m_6107_()) {
            super.m_8126_();
         }

      }
   }

   public static enum CopySkill {
      ALL,
      INTRINSIC,
      NONE;

      // $FF: synthetic method
      private static CloneEntity.CopySkill[] $values() {
         return new CloneEntity.CopySkill[]{ALL, INTRINSIC, NONE};
      }
   }
}
