package com.github.manasmods.tensura.entity.human;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.config.TensuraConfig;
import com.github.manasmods.tensura.race.Race;
import com.github.manasmods.tensura.race.dwarf.DwarfRace;
import com.github.manasmods.tensura.race.elf.ElfRace;
import com.github.manasmods.tensura.race.human.HumanRace;
import java.util.Iterator;
import java.util.List;
import javax.annotation.Nullable;
import net.minecraft.ChatFormatting;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraftforge.event.entity.ProjectileImpactEvent;

public interface IOtherworlder {
   ResourceLocation getTextureLocation();

   List<ManasSkill> getUniqueSkills();

   default boolean shouldAttackPlayer(LivingEntity target) {
      Race race = TensuraPlayerCapability.getRace(target);
      if (race != null && !race.isSpiritual()) {
         if (TensuraEPCapability.getEP(target) < 5000.0D) {
            return false;
         } else if (TensuraEPCapability.isMajin(target)) {
            return true;
         } else if (race instanceof ElfRace) {
            return false;
         } else if (race instanceof DwarfRace) {
            return false;
         } else {
            return !(race instanceof HumanRace);
         }
      } else {
         return false;
      }
   }

   default void onProjectileImpact(ProjectileImpactEvent event) {
   }

   default void dropSkills(@Nullable Entity attacker) {
      if (attacker instanceof Player) {
         Player player = (Player)attacker;
         Iterator var3 = this.getUniqueSkills().iterator();

         while(var3.hasNext()) {
            ManasSkill skill = (ManasSkill)var3.next();
            double chance = (Double)TensuraConfig.INSTANCE.skillsConfig.otherworlderDrops.get();
            if (!(chance <= (double)(player.m_217043_().m_188501_() * 100.0F)) && SkillUtils.learnSkill(player, (ManasSkill)skill)) {
               player.m_5661_(Component.m_237110_("tensura.skill.acquire", new Object[]{skill.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)), false);
               player.m_9236_().m_6263_((Player)null, player.m_20185_(), player.m_20186_(), player.m_20189_(), SoundEvents.f_12275_, SoundSource.PLAYERS, 1.0F, 1.0F);
            }
         }

      }
   }
}
