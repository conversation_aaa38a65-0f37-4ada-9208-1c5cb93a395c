package com.github.manasmods.tensura.entity.human;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.magic.spiritual.earth.EarthSpikesMagic;
import com.github.manasmods.tensura.ability.skill.resist.AbnormalConditionNullification;
import com.github.manasmods.tensura.ability.skill.unique.CookSkill;
import com.github.manasmods.tensura.api.entity.ai.CrossbowAttackGoal;
import com.github.manasmods.tensura.api.entity.ai.DynamicMeleeAttackGoal;
import com.github.manasmods.tensura.api.entity.ai.FlyingFollowOwnerGoal;
import com.github.manasmods.tensura.api.entity.ai.UndergroundTargetingEntitiesGoal;
import com.github.manasmods.tensura.api.entity.ai.WanderingFollowOwnerGoal;
import com.github.manasmods.tensura.api.entity.controller.FlightMoveController;
import com.github.manasmods.tensura.api.entity.navigator.StraightFlightNavigator;
import com.github.manasmods.tensura.api.entity.subclass.DynamicMeleeAttackAction;
import com.github.manasmods.tensura.api.entity.subclass.IElementalSpirit;
import com.github.manasmods.tensura.api.entity.subclass.IFollower;
import com.github.manasmods.tensura.api.entity.subclass.ITeleportation;
import com.github.manasmods.tensura.capability.effects.TensuraEffectsCapability;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.config.TensuraConfig;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.effect.template.SkillMobEffect;
import com.github.manasmods.tensura.entity.magic.barrier.BlizzardEntity;
import com.github.manasmods.tensura.entity.magic.barrier.DisintegrationEntity;
import com.github.manasmods.tensura.entity.magic.barrier.HolyFieldEntity;
import com.github.manasmods.tensura.entity.magic.field.Hellfire;
import com.github.manasmods.tensura.entity.template.GreaterSpiritEntity;
import com.github.manasmods.tensura.entity.template.HumanoidNPCEntity;
import com.github.manasmods.tensura.entity.template.TensuraTamableEntity;
import com.github.manasmods.tensura.item.custom.MoonlightItem;
import com.github.manasmods.tensura.network.TensuraNetwork;
import com.github.manasmods.tensura.network.play2client.RequestFxSpawningPacket;
import com.github.manasmods.tensura.race.Race;
import com.github.manasmods.tensura.race.RaceHelper;
import com.github.manasmods.tensura.race.dwarf.DwarfRace;
import com.github.manasmods.tensura.race.elf.ElfRace;
import com.github.manasmods.tensura.race.human.HumanRace;
import com.github.manasmods.tensura.registry.attribute.TensuraAttributeRegistry;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.items.TensuraArmorItems;
import com.github.manasmods.tensura.registry.items.TensuraToolItems;
import com.github.manasmods.tensura.registry.magic.SpiritualMagics;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import com.github.manasmods.tensura.registry.skill.ResistanceSkills;
import com.github.manasmods.tensura.registry.skill.UniqueSkills;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.github.manasmods.tensura.util.damage.TensuraDamageSource;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import java.util.ArrayList;
import java.util.EnumSet;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import javax.annotation.Nullable;
import net.minecraft.commands.arguments.EntityAnchorArgument.Anchor;
import net.minecraft.core.BlockPos;
import net.minecraft.core.particles.BlockParticleOption;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.core.particles.SimpleParticleType;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.nbt.ListTag;
import net.minecraft.network.chat.Component;
import net.minecraft.network.syncher.EntityDataAccessor;
import net.minecraft.network.syncher.EntityDataSerializers;
import net.minecraft.network.syncher.SynchedEntityData;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.server.level.ServerBossEvent;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.util.Mth;
import net.minecraft.util.RandomSource;
import net.minecraft.world.DifficultyInstance;
import net.minecraft.world.InteractionHand;
import net.minecraft.world.BossEvent.BossBarColor;
import net.minecraft.world.BossEvent.BossBarOverlay;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.MobSpawnType;
import net.minecraft.world.entity.SpawnGroupData;
import net.minecraft.world.entity.TamableAnimal;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.AttributeInstance;
import net.minecraft.world.entity.ai.attributes.AttributeSupplier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.goal.AvoidEntityGoal;
import net.minecraft.world.entity.ai.goal.Goal;
import net.minecraft.world.entity.ai.goal.LookAtPlayerGoal;
import net.minecraft.world.entity.ai.goal.RandomLookAroundGoal;
import net.minecraft.world.entity.ai.goal.RangedBowAttackGoal;
import net.minecraft.world.entity.ai.goal.SitWhenOrderedToGoal;
import net.minecraft.world.entity.ai.goal.WaterAvoidingRandomFlyingGoal;
import net.minecraft.world.entity.ai.goal.WaterAvoidingRandomStrollGoal;
import net.minecraft.world.entity.ai.goal.Goal.Flag;
import net.minecraft.world.entity.ai.goal.target.ResetUniversalAngerTargetGoal;
import net.minecraft.world.entity.ai.navigation.GroundPathNavigation;
import net.minecraft.world.entity.ai.util.LandRandomPos;
import net.minecraft.world.entity.animal.FlyingAnimal;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.entity.vehicle.AbstractMinecart;
import net.minecraft.world.entity.vehicle.Boat;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.level.ItemLike;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.ServerLevelAccessor;
import net.minecraft.world.level.ClipContext.Fluid;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.levelgen.Heightmap.Types;
import net.minecraft.world.level.pathfinder.BlockPathTypes;
import net.minecraft.world.phys.AABB;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.common.ForgeMod;
import net.minecraftforge.fluids.FluidType;
import net.minecraftforge.network.PacketDistributor;
import org.jetbrains.annotations.NotNull;

public class HinataSakaguchiEntity extends OtherworlderEntity implements ITeleportation, FlyingAnimal, IFollower {
   private static final EntityDataAccessor<Integer> PHASE;
   private static final EntityDataAccessor<Integer> ATTACK;
   protected static final EntityDataAccessor<Boolean> FLYING;
   public float prevFlyProgress;
   public float flyProgress;
   protected boolean wasFlying;
   public int timeFlying = 0;
   public int miscAnimationTicks = 0;
   public int disintegrationCooldown = 0;
   public int earthJailCooldown = 0;
   private UUID disintegrationUUID = null;
   private final List<UUID> spiritIDList = new ArrayList();
   private final ServerBossEvent bossEvent;
   private final List<MobEffect> affectedEffects;

   public HinataSakaguchiEntity(EntityType<? extends HinataSakaguchiEntity> pEntityType, Level pLevel) {
      super(pEntityType, pLevel);
      this.bossEvent = (ServerBossEvent)(new ServerBossEvent(this.m_5446_(), BossBarColor.WHITE, BossBarOverlay.NOTCHED_20)).m_7005_(true);
      this.affectedEffects = List.of((MobEffect)TensuraMobEffects.ANTI_SKILL.get(), (MobEffect)TensuraMobEffects.INFINITE_IMPRISONMENT.get(), (MobEffect)TensuraMobEffects.LUST_EMBRACEMENT.get(), (MobEffect)TensuraMobEffects.OPPRESSION.get(), (MobEffect)TensuraMobEffects.SOUL_DRAIN.get(), (MobEffect)TensuraMobEffects.DROWSINESS.get());
      this.f_19793_ = 2.0F;
      this.f_21364_ = 12000;
      this.f_21365_ = new HinataSakaguchiEntity.HinataLookControl();
      this.m_21441_(BlockPathTypes.DANGER_FIRE, -1.0F);
      this.m_21441_(BlockPathTypes.WATER, -1.0F);
      this.m_21441_(BlockPathTypes.WATER_BORDER, 16.0F);
      this.m_21441_(BlockPathTypes.FENCE, -1.0F);
      this.switchNavigator(false);
   }

   protected void switchNavigator(boolean onLand) {
      if (this.canFly()) {
         if (!onLand && !this.m_5803_()) {
            this.f_21342_ = new HinataSakaguchiEntity.HinataFlightMoveControl();
            this.f_21344_ = new StraightFlightNavigator(this, this.f_19853_);
            this.wasFlying = true;
         } else {
            this.f_21342_ = new HinataSakaguchiEntity.HinataMoveControl();
            this.f_21344_ = new GroundPathNavigation(this, this.f_19853_);
            this.wasFlying = false;
         }

      }
   }

   public static AttributeSupplier setAttributes() {
      return Mob.m_21552_().m_22268_(Attributes.f_22284_, 50.0D).m_22268_(Attributes.f_22281_, 20.0D).m_22268_(Attributes.f_22276_, 1000.0D).m_22268_(Attributes.f_22279_, 0.3499999940395355D).m_22268_(Attributes.f_22277_, 64.0D).m_22268_(Attributes.f_22278_, 0.800000011920929D).m_22268_((Attribute)ForgeMod.SWIM_SPEED.get(), 3.0D).m_22268_((Attribute)ForgeMod.ATTACK_RANGE.get(), 3.0D).m_22265_();
   }

   protected void m_8099_() {
      this.f_21345_.m_25352_(1, new SitWhenOrderedToGoal(this));
      this.f_21345_.m_25352_(2, new HumanoidNPCEntity.EatingItemGoal(this, (entity) -> {
         return this.shouldHeal();
      }, 3.0F));
      this.f_21345_.m_25352_(1, new AvoidEntityGoal<Player>(this, Player.class, 15.0F, 1.2D, 2.0D) {
         public boolean m_8036_() {
            return HinataSakaguchiEntity.this.getAttack() == 7 ? false : super.m_8036_();
         }
      });
      this.f_21345_.m_25352_(4, new CrossbowAttackGoal(this, 1.2D, 20.0F));
      this.f_21345_.m_25352_(4, new RangedBowAttackGoal(this, 1.0D, 20, 20.0F));
      this.f_21345_.m_25352_(4, new HumanoidNPCEntity.SpearTypeAttackGoal(this, 1.0D, 20, 20.0F));
      this.f_21345_.m_25352_(4, new HinataSakaguchiEntity.HinataAttackGoal(this));
      this.f_21345_.m_25352_(3, new FlyingFollowOwnerGoal(this, 0.7D, 10.0F, 4.0F, true, false) {
         public boolean m_8036_() {
            return !HinataSakaguchiEntity.this.canFly() ? false : super.m_8036_();
         }
      });
      this.f_21345_.m_25352_(5, new WanderingFollowOwnerGoal(this, 1.5D, 10.0F, 5.0F, false) {
         public boolean m_8036_() {
            return HinataSakaguchiEntity.this.canFly() ? false : super.m_8036_();
         }
      });
      this.f_21345_.m_25352_(6, new HinataSakaguchiEntity.WalkGoal(this));
      this.f_21345_.m_25352_(7, new WaterAvoidingRandomStrollGoal(this, 1.2D) {
         public boolean m_8036_() {
            return HinataSakaguchiEntity.this.canFly() ? false : super.m_8036_();
         }
      });
      this.f_21345_.m_25352_(8, new WaterAvoidingRandomFlyingGoal(this, 1.2D) {
         public boolean m_8036_() {
            return !HinataSakaguchiEntity.this.canFly() ? false : super.m_8036_();
         }
      });
      this.f_21345_.m_25352_(8, new RandomLookAroundGoal(this));
      this.f_21345_.m_25352_(9, new LookAtPlayerGoal(this, Player.class, 6.0F));
      this.f_21346_.m_25352_(1, new TensuraTamableEntity.TensuraOwnerHurtByTargetGoal(this));
      this.f_21346_.m_25352_(2, new TensuraTamableEntity.TensuraOwnerHurtTargetGoal(this));
      this.f_21346_.m_25352_(3, (new TensuraTamableEntity.TensuraHurtByTargetGoal(this, new Class[]{HinataSakaguchiEntity.class})).m_26044_(new Class[0]));
      this.f_21346_.m_25352_(4, new UndergroundTargetingEntitiesGoal<Player>(this, Player.class, false, 50.0F, this::shouldAttackPlayer) {
         @NotNull
         protected AABB m_7255_(double pTargetDistance) {
            return HinataSakaguchiEntity.this.m_29443_() ? this.f_26135_.m_20191_().m_82377_(pTargetDistance, (double)this.yDistance, pTargetDistance) : this.f_26135_.m_20191_().m_82377_(pTargetDistance, 4.0D, pTargetDistance).m_82369_(this.f_26135_.m_20182_().m_82520_(0.0D, (double)this.yDistance, 0.0D));
         }
      });
      this.f_21346_.m_25352_(5, new UndergroundTargetingEntitiesGoal<LivingEntity>(this, LivingEntity.class, false, 50.0F, this::shouldAttack) {
         @NotNull
         protected AABB m_7255_(double pTargetDistance) {
            return this.f_26135_.m_20191_().m_82377_(pTargetDistance, 4.0D, pTargetDistance).m_82369_(this.f_26135_.m_20182_().m_82520_(0.0D, (double)this.yDistance, 0.0D));
         }
      });
      this.f_21346_.m_25352_(8, new ResetUniversalAngerTargetGoal(this, true));
   }

   public boolean shouldAttack(LivingEntity entity) {
      if (entity == this) {
         return false;
      } else if (!entity.m_6084_()) {
         return false;
      } else if (this.m_7307_(entity)) {
         return false;
      } else {
         Mob mob;
         if (this.m_21826_() == null) {
            if (entity instanceof Mob) {
               mob = (Mob)entity;
               if (mob.m_5448_() == this) {
                  return true;
               }
            }

            return entity.m_6095_().m_204039_(TensuraTags.EntityTypes.HERO_BOSS);
         } else if (entity.m_7307_(this.m_21826_())) {
            return false;
         } else if (entity instanceof Mob) {
            mob = (Mob)entity;
            return mob.m_5448_() == this.m_21826_();
         } else {
            return this.m_21826_().m_21214_() == entity || this.m_21826_().m_21188_() == entity;
         }
      }
   }

   public boolean shouldAttackPlayer(LivingEntity target) {
      if (target instanceof Player) {
         Player player = (Player)target;
         if (player.m_7500_() || player.m_5833_()) {
            return false;
         }
      }

      if (this.m_21674_(target)) {
         return true;
      } else {
         Race race = TensuraPlayerCapability.getRace(target);
         if (race == null) {
            return false;
         } else if (TensuraEPCapability.getEP(target) < 10000.0D) {
            return false;
         } else if (TensuraEPCapability.isMajin(target)) {
            return true;
         } else if (race instanceof ElfRace) {
            return false;
         } else if (race instanceof DwarfRace) {
            return false;
         } else {
            return !(race instanceof HumanRace);
         }
      }
   }

   protected void m_8097_() {
      super.m_8097_();
      this.f_19804_.m_135372_(PHASE, 0);
      this.f_19804_.m_135372_(ATTACK, 0);
      this.f_19804_.m_135372_(FLYING, false);
   }

   public void m_7380_(CompoundTag compound) {
      super.m_7380_(compound);
      compound.m_128405_("Phase", this.getPhase());
      compound.m_128405_("Attack", this.getAttack());
      if (this.disintegrationUUID != null) {
         compound.m_128362_("Disintegration", this.disintegrationUUID);
      }

      compound.m_128379_("Flying", this.m_29443_());
      ListTag listTag = new ListTag();

      for(int i = 0; i < this.spiritIDList.size(); ++i) {
         CompoundTag tag = new CompoundTag();
         tag.m_128362_("UUID" + i, (UUID)this.spiritIDList.get(i));
         listTag.add(tag);
      }

      compound.m_128365_("SpiritList", listTag);
   }

   public void m_7378_(CompoundTag compound) {
      super.m_7378_(compound);
      if (this.m_8077_()) {
         this.bossEvent.m_6456_(this.m_5446_());
      }

      this.setPhase(compound.m_128451_("Phase"));
      this.setAttack(compound.m_128451_("Attack"));
      if (compound.m_128441_("Disintegration")) {
         this.disintegrationUUID = compound.m_128342_("Disintegration");
      }

      this.setFlying(compound.m_128471_("Flying"));
      ListTag listTag = (ListTag)compound.m_128423_("SpiritList");
      if (listTag != null) {
         this.spiritIDList.clear();

         for(int i = 0; i < listTag.size(); ++i) {
            CompoundTag spirit = (CompoundTag)listTag.get(i);
            this.spiritIDList.add(spirit.m_128342_("UUID" + i));
         }

      }
   }

   public int getPhase() {
      return (Integer)this.f_19804_.m_135370_(PHASE);
   }

   public void setPhase(int phase) {
      this.f_19804_.m_135381_(PHASE, phase);
   }

   public int getAttack() {
      return (Integer)this.f_19804_.m_135370_(ATTACK);
   }

   public void setAttack(int attack) {
      this.f_19804_.m_135381_(ATTACK, attack);
   }

   public boolean m_29443_() {
      return (Boolean)this.f_19804_.m_135370_(FLYING);
   }

   public void setFlying(boolean flying) {
      this.f_19804_.m_135381_(FLYING, flying);
   }

   public boolean canFly() {
      return this.getPhase() == 4 || this.m_20072_();
   }

   public ResourceLocation getTextureLocation() {
      return new ResourceLocation("tensura", "textures/entity/otherworlder/hinata_sakaguchi.png");
   }

   public List<ManasSkill> getUniqueSkills() {
      return List.of((ManasSkill)UniqueSkills.USURPER.get(), (ManasSkill)UniqueSkills.MATHEMATICIAN.get());
   }

   public boolean m_6060_() {
      return false;
   }

   public boolean canSleep() {
      return false;
   }

   public boolean m_7243_(ItemStack pStack) {
      return false;
   }

   public boolean m_142535_(float pFallDistance, float pMultiplier, DamageSource pSource) {
      return false;
   }

   public boolean m_7301_(MobEffectInstance instance) {
      if (instance.m_19544_().equals(MobEffects.f_19619_)) {
         return true;
      } else if (AbnormalConditionNullification.getAbnormalEffects().contains(instance.m_19544_())) {
         return false;
      } else if (this.affectedEffects.contains(instance.m_19544_())) {
         return true;
      } else {
         return !(instance.m_19544_() instanceof SkillMobEffect) && !instance.m_19544_().m_19486_() ? false : super.m_7301_(instance);
      }
   }

   public boolean m_6469_(DamageSource pSource, float pAmount) {
      if (this.m_6673_(pSource)) {
         return false;
      } else if (pSource.m_7639_() instanceof IElementalSpirit) {
         return false;
      } else if (this.canActivateFaultField(pSource)) {
         return false;
      } else if (this.shouldDodge(pSource)) {
         return false;
      } else {
         label91: {
            if (!pSource.m_19387_()) {
               label86: {
                  if (pSource instanceof TensuraDamageSource) {
                     TensuraDamageSource source = (TensuraDamageSource)pSource;
                     if (source.getSkill() != null) {
                        break label86;
                     }
                  }

                  pAmount *= 0.5F;
                  break label91;
               }
            }

            if (this.getPhase() == 4) {
               pAmount *= RaceHelper.spiritualReducePhysicalDamage(pSource);
            }
         }

         boolean hurt = super.m_6469_(pSource, pAmount);
         if (hurt) {
            if (this.getAttack() == 7 && pAmount >= this.m_21233_() / 10.0F && this.miscAnimationTicks <= 250 && this.disintegrationUUID != null) {
               Entity entity = ((ServerLevel)this.f_19853_).m_8791_(this.disintegrationUUID);
               if (entity instanceof DisintegrationEntity) {
                  TensuraParticleHelper.addServerParticlesAroundSelf(entity, ParticleTypes.f_123812_, 3.0D);
                  TensuraParticleHelper.addServerParticlesAroundSelf(entity, ParticleTypes.f_123813_, 3.0D);
                  entity.m_146870_();
                  this.disintegrationCooldown = 1000;
                  this.setAttack(0);
                  this.miscAnimationTicks = 0;
                  this.m_9236_().m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_11913_, SoundSource.PLAYERS, 5.0F, 1.0F);
               } else {
                  this.disintegrationUUID = null;
               }
            }

            Entity var5 = pSource.m_7639_();
            if (var5 instanceof LivingEntity) {
               LivingEntity damageSource = (LivingEntity)var5;
               if (!damageSource.m_6084_()) {
                  return true;
               }

               if (damageSource instanceof Player) {
                  Player player = (Player)damageSource;
                  if (player.m_7500_() || player.m_5833_()) {
                     return true;
                  }
               }

               List<GreaterSpiritEntity> list = this.f_19853_.m_6443_(GreaterSpiritEntity.class, this.m_20191_().m_82400_(32.0D), (entityx) -> {
                  return !entityx.m_21824_();
               });
               if (!list.isEmpty()) {
                  list.forEach((spirit) -> {
                     spirit.m_6710_(damageSource);
                  });
               }
            }
         }

         return hurt;
      }
   }

   private boolean shouldDodge(DamageSource source) {
      if (this.m_9236_().m_5776_()) {
         return false;
      } else if (this.shouldStopMoving()) {
         return false;
      } else if (SkillUtils.canNegateDodge(this, source)) {
         return false;
      } else {
         Entity var3 = source.m_7640_();
         if (var3 instanceof LivingEntity) {
            LivingEntity entity = (LivingEntity)var3;
            if (source.m_19378_()) {
               return false;
            } else if ((double)entity.m_217043_().m_188501_() >= 0.125D * (double)this.getPhase()) {
               return false;
            } else {
               this.m_9236_().m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_11852_, SoundSource.PLAYERS, 2.0F, 1.0F);
               if (!this.m_9236_().m_5776_() && this.m_5448_() != null) {
                  this.teleportTowards(this, this.m_5448_(), 5.0D);
               }

               this.f_19802_ = 60;
               return true;
            }
         } else {
            return false;
         }
      }
   }

   private boolean canActivateFaultField(DamageSource damageSource) {
      if (this.getPhase() != 1) {
         return false;
      } else if (damageSource.m_19378_()) {
         return false;
      } else if (DamageSourceHelper.isSpiritual(damageSource)) {
         return false;
      } else if (DamageSourceHelper.isSpatialDamage(damageSource)) {
         return false;
      } else {
         return !SkillUtils.haveSeveranceAttack(damageSource, this);
      }
   }

   public boolean m_7307_(Entity entity) {
      if (super.m_7307_(entity)) {
         return true;
      } else if (entity instanceof IElementalSpirit) {
         IElementalSpirit spirit = (IElementalSpirit)entity;
         return Objects.equals(spirit.getSummonerUUID(), this.m_20148_());
      } else {
         return false;
      }
   }

   public void m_6710_(@Nullable LivingEntity target) {
      super.m_6710_(target);
      if (target instanceof IElementalSpirit) {
         IElementalSpirit spirit = (IElementalSpirit)target;
         if (spirit.getSummoningTick() > 0) {
            spirit.setSummonerUUID(this.m_20148_());
            if (spirit instanceof Mob) {
               Mob mob = (Mob)spirit;
               mob.m_6710_((LivingEntity)null);
            }
         }
      }

   }

   public boolean shouldCountMotionBlock() {
      if (this.getPhase() != 4) {
         return true;
      } else {
         return this.m_5448_() != null && this.m_5448_().m_20096_();
      }
   }

   public void m_6593_(@Nullable Component pName) {
      super.m_6593_(pName);
      this.bossEvent.m_6456_(this.m_5446_());
   }

   public void m_6457_(ServerPlayer pPlayer) {
      super.m_6457_(pPlayer);
      this.bossEvent.m_6543_(pPlayer);
   }

   public void m_6452_(ServerPlayer pPlayer) {
      super.m_6452_(pPlayer);
      this.bossEvent.m_6539_(pPlayer);
   }

   protected void m_8024_() {
      super.m_8024_();
      this.bossEvent.m_142711_(this.m_21223_() / this.m_21233_());
      if (this.getPhase() == 4) {
         this.bossEvent.m_6451_(BossBarColor.YELLOW);
         this.bossEvent.m_5648_(BossBarOverlay.NOTCHED_12);
      }

   }

   private boolean shouldStopMoving() {
      if (this.getPhase() == 1) {
         return true;
      } else {
         int attack = this.getAttack();
         return attack == 2 || attack == 6 || attack == 7 || attack == 8;
      }
   }

   protected void handleFlying() {
      this.prevFlyProgress = this.flyProgress;
      if (this.m_29443_()) {
         if (this.flyProgress < 5.0F) {
            ++this.flyProgress;
         }
      } else if (this.flyProgress > 0.0F) {
         --this.flyProgress;
      }

      if (!this.f_19853_.m_5776_()) {
         boolean isFlying = this.m_29443_() && this.canFly();
         if (isFlying != this.wasFlying) {
            this.switchNavigator(!isFlying);
         }

         if (isFlying) {
            ++this.timeFlying;
            this.m_20242_(true);
            if (this.m_21827_() || this.m_20159_() || this.m_27593_() || this.m_5803_()) {
               this.setFlying(false);
            }
         } else {
            this.timeFlying = 0;
            this.m_20242_(false);
         }
      }

   }

   public void m_8119_() {
      super.m_8119_();
      if (!this.m_9236_().m_5776_()) {
         this.miscAnimationHandler();
         this.phaseHandler();
         if (this.m_20202_() instanceof Boat || this.m_20202_() instanceof AbstractMinecart) {
            this.m_20202_().m_6469_(DamageSource.m_19370_(this), 40.0F);
         }

         this.handleFlying();
         if (this.disintegrationCooldown > 0) {
            --this.disintegrationCooldown;
         }

         if (this.earthJailCooldown > 0) {
            --this.earthJailCooldown;
         }

         if (this.f_19797_ % 20 == 0) {
            if (!this.m_6084_()) {
               return;
            }

            if (this.shouldStopMoving()) {
               return;
            }

            if (this.m_21223_() >= this.m_21233_() && TensuraEPCapability.getSpiritualHealth(this) >= this.m_21133_((Attribute)TensuraAttributeRegistry.MAX_SPIRITUAL_HEALTH.get())) {
               return;
            }

            if (this.f_19797_ % 200 == 0) {
               if (this.getAttack() == 0) {
                  this.setAttack(9);
               }

               this.m_5634_(100.0F);
               TensuraEPCapability.healSpiritualHealth(this, 200.0D);
               TensuraEffectsCapability.resetEverything(this, false, false);
               this.m_7292_(new MobEffectInstance(MobEffects.f_19619_, 30, 0, false, false, false));
               TensuraParticleHelper.addServerParticlesAroundSelf(this, ParticleTypes.f_123810_, 2.0D);
               TensuraParticleHelper.addServerParticlesAroundSelf(this, ParticleTypes.f_123749_, 2.0D);
            } else if (this.getPhase() != 4) {
               this.m_5634_(2.0F);
               TensuraEPCapability.healSpiritualHealth(this, 20.0D);
            } else {
               this.m_5634_(10.0F);
               TensuraEPCapability.healSpiritualHealth(this, 50.0D);
            }
         }

      }
   }

   protected void phaseHandler() {
      switch(this.getPhase()) {
      case 0:
         if (this.m_5448_() != null) {
            this.setPhase(this.m_21824_() ? 2 : 1);
            if (this.m_6084_()) {
               this.m_5634_(this.m_21233_());
            }

            HolyFieldEntity holyField = new HolyFieldEntity(this.f_19853_, this);
            holyField.setRadius(50.0F);
            holyField.setLife(-1);
            holyField.m_146884_(this.m_20182_().m_82520_(0.0D, -25.0D, 0.0D));
            this.f_19853_.m_7967_(holyField);
         }
         break;
      case 1:
         LivingEntity target = this.m_5448_();
         if (target != null) {
            this.m_7618_(Anchor.EYES, target.m_146892_());
         }

         if (!((double)this.m_21223_() <= (double)this.m_21233_() * 0.75D) && !(this.m_21223_() <= 750.0F)) {
            if (this.spiritIDList.isEmpty()) {
               return;
            }

            Iterator var2 = List.copyOf(this.spiritIDList).iterator();

            while(true) {
               UUID id;
               Entity entity;
               do {
                  if (!var2.hasNext()) {
                     if (this.spiritIDList.isEmpty()) {
                        this.setPhase(2);
                     }

                     return;
                  }

                  id = (UUID)var2.next();
                  entity = ((ServerLevel)this.f_19853_).m_8791_(id);
               } while(entity != null && entity.m_6084_());

               this.spiritIDList.remove(id);
            }
         }

         this.setPhase(3);
         return;
      case 2:
         if (!((double)this.m_21223_() > (double)this.m_21233_() * 0.75D) && !(this.m_21223_() <= 750.0F)) {
            this.setPhase(3);
            break;
         }

         return;
      case 3:
         if ((double)this.m_21223_() > (double)this.m_21233_() * 0.5D || this.m_21223_() <= 500.0F) {
            return;
         }

         this.enterLastPhase();
      }

   }

   private void enterLastPhase() {
      this.f_19802_ = 100;
      CookSkill.removeCookedHP(this, (ManasSkillInstance)null);
      AttributeInstance HP = this.m_21051_(Attributes.f_22276_);
      if (HP != null) {
         HP.m_22100_(HP.m_22115_() * 0.5D);
         this.m_21153_((float)HP.m_22135_());
      }

      TensuraEPCapability.getFrom(this).ifPresent((cap) -> {
         if (cap.getEP() <= 0.0D) {
            cap.setEP(this, 1000000.0D, false);
         }

         if (cap.getCurrentEP() <= 0.0D) {
            cap.setCurrentEP(this, cap.getEP() * 0.5D);
         }

         double SHP = this.m_21133_((Attribute)TensuraAttributeRegistry.MAX_SPIRITUAL_HEALTH.get());
         if (cap.getSpiritualHealth() < SHP * 0.5D) {
            cap.setSpiritualHealth(SHP * 0.5D);
         }

         TensuraEPCapability.sync(this);
      });
      AttributeInstance armor = this.m_21051_(Attributes.f_22284_);
      if (armor != null) {
         armor.m_22100_(armor.m_22115_() + 20.0D);
      }

      TensuraEffectsCapability.resetEverything(this, false, false);
      this.f_20890_ = false;
      this.f_20919_ = 0;
      this.m_146912_();
      this.disintegrationCooldown = 200;
      this.setPhase(4);
      this.switchNavigator(false);
      this.m_8061_(EquipmentSlot.MAINHAND, ((MoonlightItem)TensuraToolItems.MOONLIGHT.get()).m_7968_());
      this.m_8061_(EquipmentSlot.CHEST, ((Item)TensuraArmorItems.HOLY_ARMAMENTS_CHESTPLATE.get()).m_7968_());
      this.m_8061_(EquipmentSlot.LEGS, ((Item)TensuraArmorItems.HOLY_ARMAMENTS_LEGGINGS.get()).m_7968_());
      this.m_8061_(EquipmentSlot.FEET, ((Item)TensuraArmorItems.HOLY_ARMAMENTS_BOOTS.get()).m_7968_());
      AABB aabb = this.m_20191_().m_82400_(this.m_21133_((Attribute)ForgeMod.ATTACK_RANGE.get()) + 6.0D);
      List<LivingEntity> list = this.f_19853_.m_6443_(LivingEntity.class, aabb, (entity) -> {
         return !entity.m_7307_(this) && entity != this.m_21826_() && entity != this;
      });
      Iterator var5 = list.iterator();

      while(var5.hasNext()) {
         LivingEntity target = (LivingEntity)var5.next();
         target.m_6469_(TensuraDamageSources.holyDamage(this), (float)this.m_21133_(Attributes.f_22281_) * 2.0F);
         target.m_20334_(0.0D, 0.2D, 0.0D);
         SkillHelper.knockBack(this, target, 3.0F);
      }

      TensuraParticleHelper.spawnServerParticles(this.f_19853_, ParticleTypes.f_123812_, this.m_20185_(), this.m_20186_() + (double)(this.m_20206_() / 2.0F), this.m_20189_(), 30, 0.08D, 0.08D, 0.08D, 0.5D, true);
      this.f_19853_.m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_11913_, SoundSource.NEUTRAL, 0.5F, 0.4F + this.f_19796_.m_188501_() * 0.4F + 0.8F);
   }

   protected void miscAnimationHandler() {
      if (this.getAttack() != 0) {
         ++this.miscAnimationTicks;
         if (!this.m_6084_()) {
            return;
         }

         LivingEntity target;
         switch(this.getAttack()) {
         case 2:
            this.m_21573_().m_26573_();
            this.summonSpirit((EntityType)TensuraEntityTypes.IFRIT.get(), this.m_20182_().m_82520_(5.0D, 0.0D, 0.0D), new ResourceLocation("tensura:fire_circle"), (ParticleOptions)TensuraParticles.RED_FIRE.get(), 0, this.miscAnimationTicks);
            this.summonSpirit((EntityType)TensuraEntityTypes.UNDINE.get(), this.m_20182_().m_82520_(-5.0D, 0.0D, 0.0D), new ResourceLocation("tensura:water_circle"), (ParticleOptions)TensuraParticles.WATER_BUBBLE.get(), 1, this.miscAnimationTicks);
            this.summonSpirit((EntityType)TensuraEntityTypes.SYLPHIDE.get(), this.m_20182_().m_82520_(0.0D, 0.0D, 5.0D), new ResourceLocation("tensura:wind_circle"), (ParticleOptions)TensuraParticles.SMALL_GUST.get(), 2, this.miscAnimationTicks);
            this.summonSpirit((EntityType)TensuraEntityTypes.WAR_GNOME.get(), this.m_20182_().m_82520_(0.0D, 0.0D, -5.0D), new ResourceLocation("tensura:earth_circle"), new BlockParticleOption(ParticleTypes.f_123794_, Blocks.f_220844_.m_49966_()), 3, this.miscAnimationTicks);
            this.summonSpirit((EntityType)TensuraEntityTypes.AKASH.get(), this.m_20182_().m_82520_(0.0D, 5.0D, 0.0D), new ResourceLocation("tensura:space_circle"), ParticleTypes.f_123789_, 4, this.miscAnimationTicks);
            this.f_19853_.m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_12052_, SoundSource.PLAYERS, 2.0F, 1.0F);
            break;
         case 3:
            if (this.miscAnimationTicks != 10) {
               TensuraParticleHelper.addServerParticlesAroundSelf(this, ParticleTypes.f_123809_, 2.0D);
            } else {
               this.hellFire();
               this.m_5496_(SoundEvents.f_11862_, 5.0F, 0.95F + this.f_19796_.m_188501_() * 0.1F);
            }
            break;
         case 4:
            if (this.miscAnimationTicks != 10) {
               TensuraParticleHelper.addServerParticlesAroundSelf(this, ParticleTypes.f_123809_, 2.0D);
            } else {
               this.blizzard();
               this.m_5496_(SoundEvents.f_11862_, 5.0F, 0.95F + this.f_19796_.m_188501_() * 0.1F);
            }
            break;
         case 5:
            if (this.miscAnimationTicks != 10) {
               TensuraParticleHelper.addServerParticlesAroundSelf(this, ParticleTypes.f_123809_, 2.0D);
            } else if (this.earthJailCooldown <= 0) {
               this.earthJail();
               this.m_5496_(SoundEvents.f_11862_, 5.0F, 0.95F + this.f_19796_.m_188501_() * 0.1F);
            }
            break;
         case 6:
            this.m_21573_().m_26573_();
            target = this.m_5448_();
            if (target != null) {
               this.m_7618_(Anchor.EYES, target.m_146892_());
            }

            TensuraParticleHelper.addServerParticlesAroundSelf(this, ParticleTypes.f_123809_, 2.0D);
            if (this.miscAnimationTicks >= 10) {
               this.aerialBlade();
               this.m_5496_(SoundEvents.f_12317_, 5.0F, 0.95F + this.f_19796_.m_188501_() * 0.1F);
               this.m_5496_(SoundEvents.f_12089_, 3.0F, 0.95F + this.f_19796_.m_188501_() * 0.1F);
            }
            break;
         case 7:
            target = this.m_5448_();
            if (target != null && this.disintegrationUUID == null) {
               if (this.disintegrationCooldown <= 0) {
                  this.disintegration(target);
               }
            } else if (this.disintegrationUUID != null) {
               Entity entity = ((ServerLevel)this.f_19853_).m_8791_(this.disintegrationUUID);
               if (entity instanceof DisintegrationEntity) {
                  DisintegrationEntity disintegration = (DisintegrationEntity)entity;
                  if (target != null) {
                     if (disintegration.getAffectedEntities().isEmpty()) {
                        disintegration.m_146870_();
                        this.setAttack(0);
                        this.miscAnimationTicks = 0;
                        this.disintegrationUUID = null;
                     } else if (this.m_20270_(disintegration) <= 10.0F) {
                        this.teleportTowards(this, disintegration, 15.0D);
                     }
                  } else {
                     this.setAttack(0);
                     this.miscAnimationTicks = 0;
                     this.disintegrationUUID = null;
                  }
               } else {
                  this.setAttack(0);
                  this.miscAnimationTicks = 0;
                  this.disintegrationUUID = null;
               }
            }

            TensuraParticleHelper.addServerParticlesAroundSelf(this, ParticleTypes.f_123809_, 2.0D);
            this.m_7292_(new MobEffectInstance(MobEffects.f_19619_, 30, 0, false, false, false));
            break;
         case 8:
            this.m_21573_().m_26573_();
            if (this.miscAnimationTicks < 40) {
               TensuraParticleHelper.addServerParticlesAroundSelf(this, ParticleTypes.f_123810_, 2.0D);
               this.m_7292_(new MobEffectInstance(MobEffects.f_19619_, 40, 0, false, false, false));
            } else if (this.miscAnimationTicks == 40) {
               this.meltSlash();
            }
         }

         if (this.miscAnimationTicks >= this.getAnimationTick(this.getAttack())) {
            this.setAttack(0);
            this.miscAnimationTicks = 0;
            this.disintegrationUUID = null;
         }
      }

   }

   private int getAnimationTick(int miscAnimation) {
      short var10000;
      switch(miscAnimation) {
      case 2:
      case 8:
         var10000 = 40;
         break;
      case 3:
      case 4:
      case 5:
         var10000 = 15;
         break;
      case 6:
         var10000 = 50;
         break;
      case 7:
         var10000 = 260;
         break;
      default:
         var10000 = 5;
      }

      return var10000;
   }

   private <T extends GreaterSpiritEntity> void summonSpirit(EntityType<T> type, Vec3 pos, ResourceLocation vfx, ParticleOptions particleOptions, int order, int summonTick) {
      if (summonTick == 1) {
         GreaterSpiritEntity spirit = (GreaterSpiritEntity)type.m_20615_(this.f_19853_);
         if (spirit != null) {
            spirit.m_21557_(true);
            spirit.m_20331_(true);
            spirit.f_19794_ = true;
            spirit.m_146884_(pos.m_82520_(0.0D, -1.5D * (double)spirit.m_20206_(), 0.0D));
            spirit.m_6518_((ServerLevelAccessor)this.f_19853_, this.f_19853_.m_6436_(new BlockPos(pos)), MobSpawnType.MOB_SUMMONED, (SpawnGroupData)null, (CompoundTag)null);
            spirit.m_217045_();
            spirit.setSummonerUUID(this.m_20148_());
            AttributeInstance attack = spirit.m_21051_(Attributes.f_22281_);
            if (attack != null) {
               attack.m_22100_(attack.m_22115_() / 2.0D);
            }

            AttributeInstance health = spirit.m_21051_(Attributes.f_22276_);
            if (health != null) {
               health.m_22100_(health.m_22115_() / 2.0D);
            }

            spirit.m_21153_(spirit.m_21233_());
            this.f_19853_.m_7967_(spirit);
            this.spiritIDList.add(spirit.m_20148_());
         }
      }

      Entity summonUUID = ((ServerLevel)this.f_19853_).m_8791_((UUID)this.spiritIDList.get(Math.min(order, this.spiritIDList.size() - 1)));
      if (summonUUID instanceof Mob) {
         Mob mob = (Mob)summonUUID;
         summonUUID.m_146884_(summonUUID.m_20182_().m_82520_(0.0D, (double)mob.m_20206_() * 1.5D / 40.0D, 0.0D));
         TensuraParticleHelper.addServerParticlesAroundSelf(mob, particleOptions, 3.0D);
         if (summonTick >= 39) {
            summonUUID.f_19794_ = false;
            mob.m_21557_(false);
            mob.m_20331_(false);
            mob.m_5496_(SoundEvents.f_11862_, 3.0F, 1.0F);
            TensuraParticleHelper.addServerParticlesAroundSelf(mob, ParticleTypes.f_123747_, 2.0D);
            TensuraParticleHelper.addServerParticlesAroundSelf(mob, ParticleTypes.f_123747_, 3.0D);
         }
      }

      TensuraNetwork.INSTANCE.send(PacketDistributor.TRACKING_ENTITY_AND_SELF.with(() -> {
         return this;
      }), new RequestFxSpawningPacket(vfx, new BlockPos(pos), 0.0D, 0.0D, 0.0D, 0, true));
   }

   private void disintegration(LivingEntity target) {
      this.m_7618_(Anchor.EYES, target.m_146892_());
      if (this.disintegrationUUID == null || ((ServerLevel)this.f_19853_).m_8791_(this.disintegrationUUID) == null) {
         DisintegrationEntity disintegration = new DisintegrationEntity(this.f_19853_, this);
         disintegration.setRadius(3.0F);
         disintegration.setHeight(50.0F);
         disintegration.setLife(300);
         double y = (double)this.f_19853_.m_6924_(Types.WORLD_SURFACE, (int)target.m_20185_(), (int)target.m_20189_());
         Vec3 pos = new Vec3(target.m_20185_(), y, target.m_20189_());
         disintegration.m_146884_(pos);
         this.f_19853_.m_7967_(disintegration);
         this.disintegrationUUID = disintegration.m_20148_();
         target.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.MOVEMENT_INTERFERENCE.get(), 40, 9, false, false, false));
         target.m_20219_(disintegration.m_20182_());
         target.m_20256_(Vec3.f_82478_);
         target.f_19864_ = true;
         if (this.m_20186_() < y - 1.0D) {
            double yNew = (double)this.f_19853_.m_6924_(Types.WORLD_SURFACE, (int)this.m_20185_(), (int)this.m_20189_());
            this.f_19853_.m_6263_((Player)null, this.f_19854_, this.f_19855_, this.f_19856_, SoundEvents.f_11852_, this.m_5720_(), 1.0F, 1.0F);
            this.m_6027_(this.m_20185_(), yNew, this.m_20189_());
            this.m_5496_(SoundEvents.f_11852_, 1.0F, 1.0F);
         }
      }

      this.disintegrationCooldown = 1000;
   }

   private void meltSlash() {
      LivingEntity target = this.m_5448_();
      if (target != null && target.m_20270_(this) <= 20.0F) {
         this.disintegrationCooldown = 1000;
         Vec3 source = this.m_20182_().m_82520_(0.0D, (double)(this.m_20206_() / 2.0F), 0.0D);
         Vec3 targetPos = target.m_20182_().m_82520_(0.0D, (double)(target.m_20206_() / 2.0F), 0.0D);
         Vec3 subtract = targetPos.m_82546_(source);
         Vec3 vec3 = targetPos.m_82549_(subtract.m_82541_().m_82490_(20.0D - subtract.m_82553_()));
         if (target.m_20096_()) {
            vec3 = new Vec3(vec3.m_7096_(), (double)this.f_19853_.m_6924_(Types.WORLD_SURFACE, (int)vec3.m_7096_(), (int)vec3.m_7094_()), vec3.m_7094_());
         }

         Vec3 offSetToTarget = vec3.m_82546_(source);

         label46:
         for(int particleIndex = 1; particleIndex < Mth.m_14107_(offSetToTarget.m_82553_()); ++particleIndex) {
            Vec3 particlePos = source.m_82549_(offSetToTarget.m_82541_().m_82490_((double)particleIndex));
            TensuraParticleHelper.addServerParticlesAroundPos(this.m_217043_(), this.f_19853_, particlePos, (ParticleOptions)TensuraParticles.SOLAR_FLASH.get(), 1.0D);
            TensuraParticleHelper.addServerParticlesAroundPos(this.m_217043_(), this.f_19853_, particlePos, ParticleTypes.f_123810_, 2.0D);
            double attackRange = this.m_21133_((Attribute)ForgeMod.ATTACK_RANGE.get()) + 2.0D;
            List<LivingEntity> list = this.f_19853_.m_6443_(LivingEntity.class, (new AABB(new BlockPos(particlePos))).m_82400_(attackRange), (entityx) -> {
               return !entityx.m_7306_(this) && !entityx.m_7307_(this);
            });
            if (!list.isEmpty()) {
               float amount = 3000.0F;
               DamageSource damageSource = DisintegrationEntity.getDisintegrationDamage(this);
               Iterator var14 = list.iterator();

               while(true) {
                  LivingEntity entity;
                  Player player;
                  do {
                     if (!var14.hasNext()) {
                        continue label46;
                     }

                     entity = (LivingEntity)var14.next();
                     if (!(entity instanceof Player)) {
                        break;
                     }

                     player = (Player)entity;
                  } while(player.m_7500_() || player.m_5833_());

                  entity.m_6469_(damageSource, amount);
                  DamageSourceHelper.directSpiritualHurt(entity, this, damageSource, amount);
                  SkillHelper.checkThenAddEffectSource(entity, this, (MobEffect)TensuraMobEffects.DISINTEGRATING.get(), 100, 0);
               }
            }
         }

         this.m_19877_();
         this.m_20219_(vec3);
         this.f_19812_ = true;
         this.m_21011_(InteractionHand.MAIN_HAND, true);
         this.m_21190_(InteractionHand.MAIN_HAND);
         if (!this.inventory.m_8020_(4).m_41619_()) {
            this.m_21008_(InteractionHand.MAIN_HAND, this.inventory.m_8020_(4));
         }

         this.f_19853_.m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_12317_, SoundSource.PLAYERS, 1.0F, 1.0F);
      }

   }

   private void hellFire() {
      Entity target = this.m_5448_();
      Vec3 pos;
      if (target != null) {
         pos = target.m_20182_().m_82520_(0.0D, (double)(target.m_20206_() / 2.0F), 0.0D);
      } else {
         BlockHitResult result = SkillHelper.getPlayerPOVHitResult(this.f_19853_, this, Fluid.NONE, 15.0D);
         pos = result.m_82450_().m_82520_(0.0D, 0.5D, 0.0D);
      }

      Hellfire sphere = new Hellfire(this.m_9236_(), this);
      sphere.setDamage((float)this.m_21133_(Attributes.f_22281_) * 5.0F);
      sphere.setMpCost(10000.0D);
      sphere.setSkill((ManasSkillInstance)SkillAPI.getSkillsFrom(this).getSkill((ManasSkill)SpiritualMagics.HELLFIRE.get()).orElse((Object)null));
      sphere.setLife(60);
      sphere.setRadius(2.5F);
      sphere.m_6034_(pos.f_82479_, pos.f_82480_ - (double)sphere.getRadius(), pos.f_82481_);
      this.m_9236_().m_7967_(sphere);
      this.m_21011_(InteractionHand.OFF_HAND, true);
      this.m_9236_().m_6263_((Player)null, sphere.m_20185_(), sphere.m_20186_(), sphere.m_20189_(), SoundEvents.f_11913_, SoundSource.PLAYERS, 1.0F, 1.0F);
      this.m_9236_().m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_11862_, SoundSource.PLAYERS, 1.0F, 1.0F);
      ((ServerLevel)this.m_9236_()).m_8767_((SimpleParticleType)TensuraParticles.RED_FIRE.get(), this.m_20185_(), this.m_20186_() + (double)this.m_20206_() / 2.0D, this.m_20189_(), 10, 0.08D, 0.08D, 0.08D, 0.15D);
      TensuraNetwork.INSTANCE.send(PacketDistributor.TRACKING_ENTITY_AND_SELF.with(() -> {
         return sphere;
      }), new RequestFxSpawningPacket(new ResourceLocation("tensura:fire_sphere_5x5"), sphere.m_19879_(), 0.0D, (double)sphere.getRadius(), 0.0D, false));
   }

   private void blizzard() {
      BlizzardEntity blizzard = new BlizzardEntity(this.m_9236_(), this);
      blizzard.setLife(200);
      blizzard.setRadius(15.0F);
      blizzard.setDamage((float)this.m_21133_(Attributes.f_22281_));
      blizzard.setMpCost(1000.0D);
      blizzard.setSkill((ManasSkillInstance)SkillAPI.getSkillsFrom(this).getSkill((ManasSkill)SpiritualMagics.BLIZZARD.get()).orElse((Object)null));
      blizzard.m_6034_(this.m_20185_(), this.m_20186_() + (double)(this.m_20206_() / 2.0F), this.m_20189_());
      this.m_9236_().m_7967_(blizzard);
      this.m_21011_(InteractionHand.OFF_HAND, true);
      this.m_9236_().m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_12090_, SoundSource.PLAYERS, 1.0F, 1.0F);
   }

   private void earthJail() {
      LivingEntity target = this.m_5448_();
      if (target != null) {
         this.m_21011_(InteractionHand.MAIN_HAND, true);
         this.earthJailCooldown = 600;
         this.m_9236_().m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_12054_, SoundSource.PLAYERS, 1.0F, 1.0F);
         TensuraParticleHelper.addServerParticlesAroundSelf(target, new BlockParticleOption(ParticleTypes.f_123794_, Blocks.f_220844_.m_49966_()));
         TensuraParticleHelper.addServerParticlesAroundSelf(target, (ParticleOptions)TensuraParticles.BOG_EFFECT.get());
         float damage = (float)this.m_21133_(Attributes.f_22281_);
         DamageSource damageSource = TensuraDamageSources.elementalAttack("tensura.earth_attack", this, true);
         if (target.m_6469_(damageSource, damage * 2.0F)) {
            if (SkillUtils.isSkillToggled(target, (ManasSkill)ResistanceSkills.EARTH_ATTACK_NULLIFICATION.get())) {
               return;
            }

            int slowness = 5;
            if (SkillUtils.isSkillToggled(target, (ManasSkill)ResistanceSkills.EARTH_ATTACK_RESISTANCE.get())) {
               slowness = 2;
            }

            target.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.MOVEMENT_INTERFERENCE.get(), 400, slowness, false, false, false));
            target.m_7292_(new MobEffectInstance(MobEffects.f_19599_, 400, 1, false, false, false));
            target.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.FRAGILITY.get(), 400, 2, false, false, false));
         }

         if (target.m_20096_()) {
            EarthSpikesMagic.spawnSpikes(target.m_20182_(), this, (ManasSkillInstance)null, damage, 200.0D);
            EarthSpikesMagic.spawnSpikes(target.m_20182_().m_82520_(0.0D, 0.0D, 1.0D), this, (ManasSkillInstance)null, damage, 200.0D);
            EarthSpikesMagic.spawnSpikes(target.m_20182_().m_82520_(0.0D, 0.0D, -1.0D), this, (ManasSkillInstance)null, damage, 200.0D);
            EarthSpikesMagic.spawnSpikes(target.m_20182_().m_82520_(1.0D, 0.0D, 0.0D), this, (ManasSkillInstance)null, damage, 200.0D);
            EarthSpikesMagic.spawnSpikes(target.m_20182_().m_82520_(-1.0D, 0.0D, 0.0D), this, (ManasSkillInstance)null, damage, 200.0D);
         }

      }
   }

   public void aerialBlade() {
      if (this.miscAnimationTicks % 10 == 0) {
         this.f_19853_.m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_12317_, SoundSource.PLAYERS, 5.0F, 1.0F);
         int radius = 4;
         BlockHitResult result = SkillHelper.getPlayerPOVHitResult(this.f_19853_, this, Fluid.NONE, (double)(radius + 4));
         BlockPos ahead = result.m_82425_();
         Vec3 aheadVec = new Vec3((double)ahead.m_123341_(), (double)ahead.m_123342_(), (double)ahead.m_123343_());
         TensuraParticleHelper.addServerParticlesAroundPos(this.f_19796_, this.f_19853_, aheadVec, (ParticleOptions)TensuraParticles.GUST.get(), 3.0D);
         TensuraParticleHelper.addServerParticlesAroundPos(this.f_19796_, this.f_19853_, aheadVec, (ParticleOptions)TensuraParticles.SMALL_GUST.get(), 4.0D);
         TensuraParticleHelper.addServerParticlesAroundPos(this.f_19796_, this.f_19853_, aheadVec, (ParticleOptions)TensuraParticles.SMALL_GUST.get(), 5.0D);
         TensuraParticleHelper.addServerParticlesAroundPos(this.f_19796_, this.f_19853_, aheadVec, ParticleTypes.f_123766_, 4.0D);
         TensuraParticleHelper.addServerParticlesAroundPos(this.f_19796_, this.f_19853_, aheadVec, ParticleTypes.f_123766_, 5.0D);
         AABB thisInflation = this.m_20191_().m_82400_((double)radius);
         List<LivingEntity> aroundList = this.m_9236_().m_6443_(LivingEntity.class, thisInflation, (living) -> {
            return !living.m_7306_(this) && living.m_6084_() && !living.m_7307_(this);
         });

         LivingEntity target;
         for(Iterator var7 = aroundList.iterator(); var7.hasNext(); target.f_19864_ = true) {
            target = (LivingEntity)var7.next();
            target.m_20256_(aheadVec.m_82546_(target.m_20182_()).m_82541_().m_82490_(2.0D));
         }

         AABB box = (new AABB(ahead)).m_82400_((double)radius);
         List<LivingEntity> list = this.m_9236_().m_6443_(LivingEntity.class, box.m_82367_(thisInflation), (living) -> {
            return !living.m_7306_(this) && living.m_6084_() && !living.m_7307_(this);
         });
         if (!list.isEmpty()) {
            float damage = (float)this.m_21133_(Attributes.f_22281_) * 0.75F;
            Iterator var10 = list.iterator();

            while(true) {
               LivingEntity target;
               Player player;
               do {
                  if (!var10.hasNext()) {
                     return;
                  }

                  target = (LivingEntity)var10.next();
                  if (!(target instanceof Player)) {
                     break;
                  }

                  player = (Player)target;
               } while(player.m_150110_().f_35934_);

               target.m_6469_(TensuraDamageSources.elementalAttack("tensura.wind_attack", this, true), damage);
               TensuraParticleHelper.addServerParticlesAroundSelf(target, (ParticleOptions)TensuraParticles.SMALL_GUST.get(), 3.0D);
               TensuraParticleHelper.addServerParticlesAroundSelf(target, (ParticleOptions)TensuraParticles.GUST.get(), 3.0D);
            }
         }
      }
   }

   public boolean randomTeleport(LivingEntity entity, double pX, double pY, double pZ, boolean pBroadcastTeleport) {
      boolean randomTeleport = ITeleportation.super.randomTeleport(entity, pX, pY, pZ, pBroadcastTeleport);
      if (randomTeleport && !this.m_20096_()) {
         this.setFlying(true);
         this.switchNavigator(false);
      }

      return randomTeleport;
   }

   public boolean canDrownInFluidType(FluidType type) {
      return type != ForgeMod.WATER_TYPE.get();
   }

   public boolean isPushedByFluid(FluidType type) {
      return type != ForgeMod.WATER_TYPE.get();
   }

   public double getFluidMotionScale(FluidType type) {
      return 1.0D;
   }

   public void m_7023_(Vec3 vec3d) {
      if (this.canFly() && this.m_20069_() && this.m_20184_().f_82480_ > 0.0D) {
         this.m_20256_(this.m_20184_().m_82542_(1.0D, 0.5D, 1.0D));
      }

      super.m_7023_(vec3d);
   }

   public boolean shouldFollow() {
      return !this.m_21827_() && !this.isWandering() && (this.m_5448_() == null || !this.m_5448_().m_6084_());
   }

   public void followEntity(TamableAnimal animal, LivingEntity owner, double followSpeed) {
      if (this.m_20270_(owner) > 5.0F) {
         this.setFlying(true);
         this.m_21566_().m_6849_(owner.m_20185_(), owner.m_20186_() + (double)owner.m_20206_(), owner.m_20189_(), followSpeed);
      } else {
         if (this.f_19861_ && !this.m_20072_()) {
            this.setFlying(false);
         }

         if (this.m_29443_() && !this.isOverWater(this)) {
            BlockPos vec = this.getGround(this, this.m_20183_());
            this.m_21566_().m_6849_((double)vec.m_123341_(), (double)vec.m_123342_(), (double)vec.m_123343_(), followSpeed);
         } else {
            this.m_21573_().m_5624_(owner, followSpeed);
         }
      }

   }

   public boolean m_6785_(double pDistanceToClosestPlayer) {
      return false;
   }

   @Nullable
   public SpawnGroupData m_6518_(ServerLevelAccessor pLevel, DifficultyInstance pDifficulty, MobSpawnType pReason, @Nullable SpawnGroupData pSpawnData, @Nullable CompoundTag pDataTag) {
      if (!pReason.equals(MobSpawnType.BUCKET)) {
         this.m_213945_(this.f_19796_, pDifficulty);
      }

      return super.m_6518_(pLevel, pDifficulty, pReason, pSpawnData, pDataTag);
   }

   protected void m_213945_(RandomSource pRandom, DifficultyInstance pDifficulty) {
      ItemStack stack = new ItemStack((ItemLike)TensuraToolItems.DEAD_END_RAINBOW.get());
      this.m_8061_(EquipmentSlot.MAINHAND, stack);
      this.inventory.m_6836_(4, stack);
      this.inventory.m_6596_();
   }

   protected float m_21519_(EquipmentSlot pSlot) {
      if (this.m_21824_()) {
         return 0.0F;
      } else {
         return pSlot.equals(EquipmentSlot.MAINHAND) ? 1.0F : super.m_21519_(pSlot);
      }
   }

   public void m_6667_(DamageSource source) {
      if (source != DamageSource.f_19317_ && this.getPhase() < 4) {
         this.enterLastPhase();
      } else {
         super.m_6667_(source);
      }
   }

   static {
      PHASE = SynchedEntityData.m_135353_(HinataSakaguchiEntity.class, EntityDataSerializers.f_135028_);
      ATTACK = SynchedEntityData.m_135353_(HinataSakaguchiEntity.class, EntityDataSerializers.f_135028_);
      FLYING = SynchedEntityData.m_135353_(HinataSakaguchiEntity.class, EntityDataSerializers.f_135035_);
   }

   public class HinataLookControl extends TensuraTamableEntity.SleepLookControl {
      public HinataLookControl() {
         super();
      }

      public void m_8128_() {
         HinataSakaguchiEntity hinata = HinataSakaguchiEntity.this;
         if (!hinata.shouldStopMoving()) {
            if (hinata.getPhase() != 1 || hinata.m_5448_() == null) {
               super.m_8128_();
            }

         }
      }
   }

   public class HinataMoveControl extends TensuraTamableEntity.SleepMoveControl {
      public HinataMoveControl() {
         super();
      }

      public void m_8126_() {
         HinataSakaguchiEntity hinata = HinataSakaguchiEntity.this;
         if (!hinata.shouldStopMoving()) {
            if (hinata.getPhase() != 1 || hinata.m_5448_() == null) {
               super.m_8126_();
            }

         }
      }
   }

   public class HinataFlightMoveControl extends FlightMoveController {
      public HinataFlightMoveControl() {
         super(HinataSakaguchiEntity.this, 1.0F, true);
      }

      public void m_8126_() {
         HinataSakaguchiEntity hinata = HinataSakaguchiEntity.this;
         if (!hinata.shouldStopMoving()) {
            if (hinata.getPhase() != 1 || hinata.m_5448_() == null) {
               super.m_8126_();
            }

         }
      }
   }

   static class HinataAttackGoal extends DynamicMeleeAttackGoal {
      public final HinataSakaguchiEntity hinata;

      public HinataAttackGoal(HinataSakaguchiEntity serpent) {
         super(serpent, List.of((self, target, goal) -> {
            return 1.0F;
         }));
         this.hinata = serpent;
      }

      public boolean m_8036_() {
         return this.hinata.m_21827_() ? false : super.m_8036_();
      }

      protected boolean shouldMoveToTarget() {
         return this.hinata.shouldStopMoving() ? false : this.shouldMoveToTarget;
      }

      protected List<DynamicMeleeAttackAction> getActions() {
         List<DynamicMeleeAttackAction> list = new ArrayList();
         list.add((self, target, goal) -> {
            float speed = 2.0F;
            double distanceSqr = self.m_20280_(target);
            if (distanceSqr > 400.0D) {
               speed = 2.5F;
               if (this.shouldMoveToTarget()) {
                  this.hinata.teleportTowards(this.hinata, target, 5.0D);
               }
            } else {
               if (this.shouldMoveToTarget()) {
                  if (distanceSqr <= this.getAttackReachSqr(target)) {
                     this.hinata.m_7327_(target);
                     this.hinata.m_21011_(InteractionHand.MAIN_HAND, true);
                  } else if (this.hinata.m_217043_().m_188503_(5) == 1) {
                     this.hinata.teleportTowards(this.hinata, target, 10.0D);
                  }

                  this.hinata.f_21342_.m_6849_(target.m_20185_(), target.m_20186_(), target.m_20189_(), this.hinata.f_21342_.m_24999_());
               }

               this.checkAndPerformAttack(target, distanceSqr);
               self.m_21391_(target, 70.0F, 70.0F);
            }

            return speed;
         });
         return list;
      }

      protected void checkAndPerformAttack(LivingEntity pEnemy, double pDistToEnemySqr) {
         double d0 = this.getAttackReachSqr(pEnemy);
         int randomAttack = this.randomAttack(pEnemy, pDistToEnemySqr);
         if (randomAttack != 0) {
            double var10000;
            switch(randomAttack) {
            case 1:
               var10000 = d0;
               break;
            case 2:
               var10000 = d0 + 1000.0D;
               break;
            case 3:
            case 5:
            default:
               var10000 = d0 + 900.0D;
               break;
            case 4:
               var10000 = d0 + 200.0D;
               break;
            case 6:
               var10000 = d0 + 100.0D;
            }

            double attackRange = var10000;
            if (pDistToEnemySqr <= attackRange) {
               if (randomAttack == 1) {
                  this.hinata.m_7327_(pEnemy);
                  this.hinata.m_21011_(InteractionHand.MAIN_HAND, true);
               } else if (this.hinata.getAttack() == 0 || this.hinata.getAttack() == 1) {
                  this.hinata.setAttack(randomAttack);
               }
            }

         }
      }

      protected int randomAttack(LivingEntity target, double distSqr) {
         if (this.hinata.getPhase() == 1) {
            return this.hinata.spiritIDList.isEmpty() ? 2 : 0;
         } else if (this.hinata.getPhase() == 2) {
            return 1;
         } else {
            if (distSqr <= 900.0D) {
               if ((distSqr <= 100.0D || (double)this.hinata.f_19796_.m_188501_() <= 0.3D) && (double)this.hinata.f_19796_.m_188501_() <= 0.1D) {
                  return 6;
               }

               if ((double)this.hinata.f_19796_.m_188501_() <= 0.05D) {
                  return 3;
               }

               if ((distSqr <= 200.0D || (double)this.hinata.f_19796_.m_188501_() <= 0.1D) && (double)this.hinata.f_19796_.m_188501_() <= 0.2D) {
                  return 4;
               }
            }

            if ((this.hinata.earthJailCooldown > 0 || !(distSqr >= 400.0D) || !((double)this.hinata.f_19796_.m_188501_() <= 0.5D)) && !((double)this.hinata.f_19796_.m_188501_() <= 0.3D)) {
               if (target.m_21223_() >= 50.0F && this.hinata.disintegrationCooldown <= 0) {
                  if (this.hinata.m_21205_().m_150930_((Item)TensuraToolItems.MOONLIGHT.get()) && ((double)this.hinata.f_19796_.m_188501_() <= 0.5D || this.hinata.m_21223_() <= this.hinata.m_21233_() * 0.5F && (double)this.hinata.f_19796_.m_188501_() <= 0.5D)) {
                     return 8;
                  }

                  if ((this.hinata.m_21223_() <= this.hinata.m_21233_() * 0.5F || this.hinata.getPhase() == 4) && (double)this.hinata.f_19796_.m_188501_() <= 0.2D) {
                     return 7;
                  }
               }

               return 1;
            } else {
               return 5;
            }
         }
      }

      protected double getAttackReachSqr(LivingEntity target) {
         double attackRange = this.hinata.m_21133_((Attribute)ForgeMod.ATTACK_RANGE.get());
         return (double)(this.hinata.m_20205_() * 2.0F * this.hinata.m_20205_() * 2.0F + target.m_20205_()) + attackRange * attackRange;
      }
   }

   public class WalkGoal extends Goal {
      protected final HinataSakaguchiEntity entity;
      protected double x;
      protected double y;
      protected double z;
      private boolean flightTarget = false;

      public WalkGoal(HinataSakaguchiEntity entity) {
         this.m_7021_(EnumSet.of(Flag.MOVE));
         this.entity = entity;
      }

      public boolean m_8036_() {
         if (!HinataSakaguchiEntity.this.canFly()) {
            return false;
         } else if (!this.entity.m_20160_() && (this.entity.m_5448_() == null || !this.entity.m_5448_().m_6084_()) && !this.entity.m_20159_() && !this.entity.m_21827_()) {
            if (this.entity.m_217043_().m_188503_(30) != 0 && !this.entity.m_29443_()) {
               return false;
            } else {
               if (this.entity.m_20096_()) {
                  this.flightTarget = HinataSakaguchiEntity.this.f_19796_.m_188499_();
               } else {
                  this.flightTarget = HinataSakaguchiEntity.this.f_19796_.m_188503_(5) > 0 && this.entity.timeFlying < 200;
               }

               Vec3 position = this.getPosition();
               if (position == null) {
                  return false;
               } else {
                  this.x = position.f_82479_;
                  this.y = position.f_82480_;
                  this.z = position.f_82481_;
                  return true;
               }
            }
         } else {
            return false;
         }
      }

      public void m_8037_() {
         if (this.flightTarget) {
            this.entity.m_21566_().m_6849_(this.x, this.y, this.z, 1.0D);
         } else {
            this.entity.m_21573_().m_26519_(this.x, this.y, this.z, 1.0D);
            if (HinataSakaguchiEntity.this.m_29443_() && this.entity.f_19861_) {
               this.entity.setFlying(false);
            }
         }

         if (HinataSakaguchiEntity.this.m_29443_() && this.entity.f_19861_ && this.entity.timeFlying > 10) {
            this.entity.setFlying(false);
         }

      }

      @Nullable
      protected Vec3 getPosition() {
         Vec3 vector3d = this.entity.m_20182_();
         if (HinataSakaguchiEntity.this.isOverWater(this.entity) || this.entity.m_20072_()) {
            this.flightTarget = true;
         }

         Vec3 pos = Vec3.m_82512_(this.entity.getWanderPos());
         double distance = (Double)TensuraConfig.INSTANCE.entitiesConfig.tamedWanderRadius.get();
         if (this.entity.isWandering() && this.entity.m_20238_(pos) >= distance * distance) {
            return pos;
         } else if (this.flightTarget) {
            return this.entity.timeFlying >= 50 && !HinataSakaguchiEntity.this.isOverWater(this.entity) ? HinataSakaguchiEntity.this.getBlockGrounding(this.entity, vector3d) : HinataSakaguchiEntity.this.getBlockInViewAway(this.entity, vector3d, 0.0F);
         } else {
            return LandRandomPos.m_148488_(this.entity, 10, 7);
         }
      }

      public boolean m_8045_() {
         if (this.entity.m_21827_()) {
            return false;
         } else if (this.flightTarget) {
            return this.entity.m_29443_() && this.entity.m_20275_(this.x, this.y, this.z) > 2.0D;
         } else {
            return !this.entity.m_21573_().m_26571_() && !this.entity.m_20160_();
         }
      }

      public void m_8056_() {
         if (this.flightTarget) {
            this.entity.setFlying(true);
            this.entity.m_21566_().m_6849_(this.x, this.y, this.z, 1.0D);
         } else {
            this.entity.m_21573_().m_26519_(this.x, this.y, this.z, 1.0D);
         }

      }

      public void m_8041_() {
         this.entity.m_21573_().m_26573_();
         super.m_8041_();
      }
   }
}
