package com.github.manasmods.tensura.entity.projectile;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.enchantment.EngravingEnchantment;
import com.github.manasmods.tensura.registry.enchantment.TensuraEnchantments;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.items.TensuraToolItems;
import com.github.manasmods.tensura.registry.skill.UniqueSkills;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.github.manasmods.tensura.util.damage.TensuraDamageSources;
import net.minecraft.core.BlockPos;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.syncher.EntityDataAccessor;
import net.minecraft.network.syncher.EntityDataSerializers;
import net.minecraft.network.syncher.SynchedEntityData;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.util.Mth;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LightningBolt;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Entity.RemovalReason;
import net.minecraft.world.entity.projectile.Projectile;
import net.minecraft.world.entity.projectile.ProjectileUtil;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.enchantment.Enchantment;
import net.minecraft.world.item.enchantment.EnchantmentHelper;
import net.minecraft.world.item.enchantment.Enchantments;
import net.minecraft.world.level.ItemLike;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.ClipContext.Fluid;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.phys.EntityHitResult;
import net.minecraft.world.phys.HitResult;
import net.minecraft.world.phys.Vec3;
import net.minecraft.world.phys.HitResult.Type;
import net.minecraftforge.event.ForgeEventFactory;

public class SevererBladeProjectile extends Projectile {
   private static final EntityDataAccessor<Integer> DELAY_TICK;
   private static final EntityDataAccessor<Float> LOOK_DISTANCE;
   private int piercingEntity;
   private Vec3 delayVec;
   private Vec3 ownerOffset;
   protected int age;
   private float baseDamage;
   private ItemStack sourceItem;

   public SevererBladeProjectile(EntityType<? extends SevererBladeProjectile> type, Level level) {
      super(type, level);
      this.piercingEntity = 0;
      this.delayVec = Vec3.f_82478_;
      this.ownerOffset = Vec3.f_82478_;
      this.baseDamage = 3.0F;
      this.sourceItem = new ItemStack((ItemLike)TensuraToolItems.SPATIAL_BLADE.get());
   }

   public SevererBladeProjectile(Level worldIn, LivingEntity shooter, boolean right, ItemStack pStack) {
      this((EntityType)TensuraEntityTypes.SEVERER_BLADE.get(), worldIn);
      this.m_5602_(shooter);
      this.sourceItem = pStack.m_41777_();
      float rot = shooter.f_20885_ + (float)(right ? 60 : -60);
      this.m_6034_(shooter.m_20185_() - (double)shooter.m_20205_() * 0.5D * (double)Mth.m_14031_(rot * 0.017453292F), shooter.m_20188_() - 0.20000000298023224D, shooter.m_20189_() + (double)shooter.m_20205_() * 0.5D * (double)Mth.m_14089_(rot * 0.017453292F));
   }

   public SevererBladeProjectile(Level worldIn, LivingEntity shooter, ItemStack blade) {
      this((EntityType)TensuraEntityTypes.SEVERER_BLADE.get(), worldIn);
      this.m_5602_(shooter);
      this.sourceItem = blade.m_41777_();
   }

   public void m_6686_(double x, double y, double z, float velocity, float inaccuracy) {
      Vec3 vector3d = (new Vec3(x, y, z)).m_82541_().m_82520_(this.f_19796_.m_188583_() * 0.007499999832361937D * (double)inaccuracy, this.f_19796_.m_188583_() * 0.007499999832361937D * (double)inaccuracy, this.f_19796_.m_188583_() * 0.007499999832361937D * (double)inaccuracy).m_82490_((double)velocity);
      this.m_20256_(vector3d);
      float f = Mth.m_14116_((float)(vector3d.f_82479_ * vector3d.f_82479_ + vector3d.f_82481_ * vector3d.f_82481_));
      this.m_146922_((float)(Mth.m_14136_(vector3d.f_82479_, vector3d.f_82481_) * 57.2957763671875D));
      this.m_146926_((float)(Mth.m_14136_(vector3d.f_82480_, (double)f) * 57.2957763671875D));
      this.f_19859_ = this.m_146908_();
      this.f_19860_ = this.m_146909_();
   }

   private void updateShootVector() {
      Entity entity = this.m_37282_();
      if (this.getLookDistance() != 0.0F) {
         if (entity instanceof LivingEntity) {
            LivingEntity owner = (LivingEntity)entity;
            LivingEntity target = SkillHelper.getTargetingEntity(owner, (double)this.getLookDistance(), false, true);
            Vec3 pos;
            if (target != null) {
               pos = target.m_20182_().m_82520_(0.0D, (double)(target.m_20206_() / 2.0F), 0.0D);
            } else {
               BlockHitResult result = SkillHelper.getPlayerPOVHitResult(entity.f_19853_, owner, Fluid.NONE, (double)this.getLookDistance());
               pos = result.m_82450_();
            }

            this.setDelayVec(pos.m_82546_(this.m_20182_()).m_82541_().m_82490_(2.0D));
         }
      }
   }

   public void m_8119_() {
      super.m_8119_();
      if (this.getDelayTick() > 0) {
         this.setDelayTick(this.getDelayTick() - 1);
         this.updateShootVector();
         Entity owner = this.m_37282_();
         if (this.getDelayTick() == 0) {
            this.m_20256_(this.getDelayVec());
            this.f_19864_ = true;
         } else if (this.ownerOffset != Vec3.f_82478_ && owner != null) {
            this.m_146884_(owner.m_146892_().m_82549_(owner.m_20154_().m_82541_().m_82490_(1.0D)).m_82549_(this.ownerOffset.m_82496_(-owner.m_146909_() * 0.017453292F).m_82524_(-owner.m_146908_() * 0.017453292F)));
         }

         this.setRotation(this.getDelayVec(), true);
      } else {
         this.setRotation(this.m_20184_(), false);
      }

      HitResult rayTraceResult = ProjectileUtil.m_37294_(this, (x$0) -> {
         return this.m_5603_(x$0);
      });
      if (rayTraceResult.m_6662_() != Type.MISS && !ForgeEventFactory.onProjectileImpact(this, rayTraceResult)) {
         this.m_6532_(rayTraceResult);
      }

      if (this.m_20077_()) {
         this.m_142687_(RemovalReason.DISCARDED);
      } else {
         this.m_20256_(this.m_20184_().m_82490_(0.9900000095367432D));
         this.m_146884_(this.m_20182_().m_82549_(this.m_20184_()));
         if (this.getDelayTick() <= 0) {
            this.m_37283_();
            if (!this.m_20068_()) {
               this.m_20256_(this.m_20184_().m_82520_(0.0D, -0.019999999552965164D, 0.0D));
            }
         }
      }

      this.m_20101_();
      if (this.age++ > 600) {
         this.m_146870_();
      }

   }

   public void setRotation(Vec3 vec3, boolean constantUpdate) {
      if (constantUpdate || this.f_19860_ == 0.0F && this.f_19859_ == 0.0F) {
         double d0 = vec3.m_165924_();
         this.m_146922_((float)(Mth.m_14136_(vec3.f_82479_, vec3.f_82481_) * 57.2957763671875D));
         this.m_146926_((float)(Mth.m_14136_(vec3.f_82480_, d0) * 57.2957763671875D));
         this.f_19859_ = this.m_146908_();
         this.f_19860_ = this.m_146909_();
      }

   }

   protected void m_37283_() {
      Vec3 vec3 = this.m_20184_();
      if (this.f_19794_) {
         this.m_146922_((float)(Mth.m_14136_(-vec3.f_82479_, -vec3.f_82481_) * 57.2957763671875D));
      } else {
         this.m_146922_((float)(Mth.m_14136_(vec3.f_82479_, vec3.f_82481_) * 57.2957763671875D));
      }

      this.m_146926_((float)(Mth.m_14136_(vec3.f_82480_, vec3.m_165924_()) * 57.2957763671875D));
      this.m_146926_(m_37273_(this.f_19860_, this.m_146909_()));
      this.m_146922_(m_37273_(this.f_19859_, this.m_146908_()));
   }

   protected void m_8097_() {
      this.f_19804_.m_135372_(DELAY_TICK, 0);
      this.f_19804_.m_135372_(LOOK_DISTANCE, 0.0F);
   }

   protected void m_7380_(CompoundTag compound) {
      super.m_7380_(compound);
      compound.m_128405_("DelayTick", this.getDelayTick());
      compound.m_128350_("LookDistance", this.getLookDistance());
      compound.m_128365_("sourceItem", this.sourceItem.m_41739_(new CompoundTag()));
   }

   protected void m_7378_(CompoundTag compound) {
      super.m_7378_(compound);
      this.setDelayTick(compound.m_128451_("DelayTick"));
      this.setLookDistance(compound.m_128457_("LookDistance"));
      if (compound.m_128425_("sourceItem", 10)) {
         this.sourceItem = ItemStack.m_41712_(compound.m_128469_("sourceItem"));
      }

   }

   public int getDelayTick() {
      return (Integer)this.f_19804_.m_135370_(DELAY_TICK);
   }

   public void setDelayTick(int i) {
      this.f_19804_.m_135381_(DELAY_TICK, i);
   }

   public float getLookDistance() {
      return (Float)this.f_19804_.m_135370_(LOOK_DISTANCE);
   }

   public void setLookDistance(float i) {
      this.f_19804_.m_135381_(LOOK_DISTANCE, i);
   }

   private int getSeveranceLevel() {
      return this.sourceItem.getEnchantmentLevel((Enchantment)TensuraEnchantments.SEVERANCE.get());
   }

   protected void channeling(SevererBladeProjectile entity) {
      if (!entity.m_9236_().m_5776_()) {
         if (entity.m_9236_().m_46470_()) {
            if (EnchantmentHelper.m_44936_(entity.sourceItem)) {
               BlockPos blockpos = entity.m_20183_();
               Entity owner = entity.m_37282_();
               if (entity.m_9236_().m_45527_(blockpos)) {
                  LightningBolt lightningbolt = (LightningBolt)EntityType.f_20465_.m_20615_(entity.m_9236_());
                  if (lightningbolt != null) {
                     lightningbolt.m_20219_(Vec3.m_82539_(blockpos));
                     ServerPlayer var10001;
                     if (owner instanceof ServerPlayer) {
                        ServerPlayer player = (ServerPlayer)owner;
                        var10001 = player;
                     } else {
                        var10001 = null;
                     }

                     lightningbolt.m_20879_(var10001);
                     entity.m_9236_().m_7967_(lightningbolt);
                  }
               }
            }
         }
      }
   }

   protected void m_5790_(EntityHitResult pResult) {
      Entity entity = pResult.m_82443_();
      Entity ownerEntity = this.m_37282_();
      if (entity != ownerEntity) {
         float baseDamage = this.baseDamage;
         DamageSource damagesource = TensuraDamageSources.severerBlade(this, ownerEntity);
         if (this.getSeveranceLevel() > 0) {
            if (this.sourceItem.m_41784_().m_128471_("dummy")) {
               damagesource = DamageSourceHelper.turnTensura((DamageSource)damagesource).setSpatial().setSkill(SkillUtils.getSkillOrNull(this.m_37282_(), (ManasSkill)UniqueSkills.SEVERER.get()));
            } else {
               damagesource = DamageSourceHelper.turnTensura((DamageSource)damagesource).setSpatial();
            }

            baseDamage += 7.0F;
         }

         if (entity instanceof LivingEntity) {
            LivingEntity livingEntity = (LivingEntity)entity;
            float damage = baseDamage + EnchantmentHelper.m_44833_(this.sourceItem, livingEntity.m_6336_());
            if (entity.m_6469_((DamageSource)damagesource, damage)) {
               if (entity.m_6095_() == EntityType.f_20566_) {
                  return;
               }

               if (ownerEntity instanceof LivingEntity) {
                  LivingEntity owner = (LivingEntity)ownerEntity;
                  EnchantmentHelper.m_44823_(livingEntity, ownerEntity);
                  EnchantmentHelper.m_44896_(owner, livingEntity);
                  EngravingEnchantment.doAdditionalAttack(this.sourceItem, owner, entity, damage);
                  pResult.m_82443_().m_20254_(3 * this.sourceItem.getEnchantmentLevel(Enchantments.f_44981_));
               }

               if (this.getSeveranceLevel() >= 5) {
                  entity.f_19802_ = 0;
               }
            }
         }

         ++this.piercingEntity;
         if (this.piercingEntity == 1) {
            this.channeling(this);
         }

         if (this.piercingEntity >= 5 + this.sourceItem.getEnchantmentLevel(Enchantments.f_44961_)) {
            this.m_146870_();
            this.m_5496_(SoundEvents.f_12018_, 0.5F, 0.75F);
         }

      }
   }

   protected void m_8060_(BlockHitResult pResult) {
      if (!this.f_19853_.f_46443_) {
         this.channeling(this);
         this.m_142687_(RemovalReason.DISCARDED);
         this.m_5496_(SoundEvents.f_12018_, 0.5F, 0.75F);
      } else {
         this.m_9236_().m_7106_(ParticleTypes.f_123808_, this.m_20185_(), this.m_20186_(), this.m_20189_(), 0.0D, 0.05D, 0.0D);
      }

   }

   public void setDelayVec(Vec3 delayVec) {
      this.delayVec = delayVec;
   }

   public Vec3 getDelayVec() {
      return this.delayVec;
   }

   public void setOwnerOffset(Vec3 ownerOffset) {
      this.ownerOffset = ownerOffset;
   }

   public Vec3 getOwnerOffset() {
      return this.ownerOffset;
   }

   public void setBaseDamage(float baseDamage) {
      this.baseDamage = baseDamage;
   }

   static {
      DELAY_TICK = SynchedEntityData.m_135353_(SevererBladeProjectile.class, EntityDataSerializers.f_135028_);
      LOOK_DISTANCE = SynchedEntityData.m_135353_(SevererBladeProjectile.class, EntityDataSerializers.f_135029_);
   }
}
