package com.github.manasmods.tensura.entity;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.api.entity.ai.BasicSpellGoal;
import com.github.manasmods.tensura.api.entity.ai.CrossbowAttackGoal;
import com.github.manasmods.tensura.api.entity.ai.UndergroundTargetingEntitiesGoal;
import com.github.manasmods.tensura.api.entity.ai.WanderingFollowOwnerGoal;
import com.github.manasmods.tensura.api.entity.subclass.IGiantMob;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.entity.magic.barrier.BarrierPart;
import com.github.manasmods.tensura.entity.template.HumanoidNPCEntity;
import com.github.manasmods.tensura.entity.template.TensuraTamableEntity;
import com.github.manasmods.tensura.race.RaceHelper;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.entity.TensuraEntityTypes;
import com.github.manasmods.tensura.registry.items.TensuraMobDropItems;
import com.github.manasmods.tensura.registry.items.TensuraToolItems;
import com.github.manasmods.tensura.registry.particle.TensuraParticles;
import com.github.manasmods.tensura.registry.skill.UniqueSkills;
import com.github.manasmods.tensura.world.TensuraGameRules;
import java.util.Iterator;
import java.util.List;
import java.util.Optional;
import javax.annotation.Nullable;
import net.minecraft.core.BlockPos;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.syncher.EntityDataAccessor;
import net.minecraft.network.syncher.EntityDataSerializers;
import net.minecraft.network.syncher.SynchedEntityData;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.util.Mth;
import net.minecraft.util.RandomSource;
import net.minecraft.world.DifficultyInstance;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.AgeableMob;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.MobSpawnType;
import net.minecraft.world.entity.SpawnGroupData;
import net.minecraft.world.entity.SpawnPlacements;
import net.minecraft.world.entity.EquipmentSlot.Type;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.AttributeInstance;
import net.minecraft.world.entity.ai.attributes.AttributeSupplier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.goal.FloatGoal;
import net.minecraft.world.entity.ai.goal.LookAtPlayerGoal;
import net.minecraft.world.entity.ai.goal.RandomLookAroundGoal;
import net.minecraft.world.entity.ai.goal.RangedBowAttackGoal;
import net.minecraft.world.entity.ai.goal.SitWhenOrderedToGoal;
import net.minecraft.world.entity.ai.goal.target.ResetUniversalAngerTargetGoal;
import net.minecraft.world.entity.ai.targeting.TargetingConditions;
import net.minecraft.world.entity.animal.Animal;
import net.minecraft.world.entity.animal.IronGolem;
import net.minecraft.world.entity.monster.AbstractIllager;
import net.minecraft.world.entity.npc.AbstractVillager;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.entity.vehicle.AbstractMinecart;
import net.minecraft.world.entity.vehicle.Boat;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;
import net.minecraft.world.level.GameRules;
import net.minecraft.world.level.ItemLike;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.NaturalSpawner;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.phys.AABB;
import net.minecraftforge.common.ForgeMod;
import net.minecraftforge.event.ForgeEventFactory;
import software.bernie.geckolib3.core.AnimationState;
import software.bernie.geckolib3.core.IAnimatable;
import software.bernie.geckolib3.core.PlayState;
import software.bernie.geckolib3.core.builder.AnimationBuilder;
import software.bernie.geckolib3.core.builder.ILoopType.EDefaultLoopTypes;
import software.bernie.geckolib3.core.controller.AnimationController;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.manager.AnimationData;

public class OrcLordEntity extends OrcEntity implements IAnimatable, IGiantMob {
   private static final EntityDataAccessor<Integer> LAUGHING;
   protected int selfRegen;

   public OrcLordEntity(EntityType<? extends OrcLordEntity> pEntityType, Level pLevel) {
      super(pEntityType, pLevel);
      this.m_21553_(false);
      this.f_21364_ = 2000;
      this.f_19793_ = 1.0F;
   }

   public static AttributeSupplier setAttributes() {
      return Mob.m_21552_().m_22268_(Attributes.f_22281_, 20.0D).m_22268_(Attributes.f_22276_, 200.0D).m_22268_(Attributes.f_22279_, 0.15000000596046448D).m_22268_(Attributes.f_22277_, 64.0D).m_22268_(Attributes.f_22278_, 1.0D).m_22268_((Attribute)ForgeMod.SWIM_SPEED.get(), 3.0D).m_22268_((Attribute)ForgeMod.ATTACK_RANGE.get(), 3.0D).m_22265_();
   }

   protected void m_8099_() {
      this.f_21345_.m_25352_(0, new FloatGoal(this));
      this.f_21345_.m_25352_(1, new SitWhenOrderedToGoal(this));
      this.f_21345_.m_25352_(2, new HumanoidNPCEntity.EatingItemGoal(this, (entity) -> {
         return this.shouldHeal();
      }, 3.0F) {
         public boolean m_8036_() {
            return OrcLordEntity.this.getLaughing() > 0 ? false : super.m_8036_();
         }
      });
      this.f_21345_.m_25352_(3, new CrossbowAttackGoal(this, 1.2D, 10.0F));
      this.f_21345_.m_25352_(3, new RangedBowAttackGoal(this, 1.0D, 20, 15.0F));
      this.f_21345_.m_25352_(3, new HumanoidNPCEntity.SpearTypeAttackGoal(this, 1.0D, 20, 15.0F));
      this.f_21345_.m_25352_(3, new OrcLordEntity.OrcLordAttackGoal(this));
      this.f_21345_.m_25352_(4, new OrcLordEntity.SummonOrcsGoal(this, 15, 400));
      this.f_21345_.m_25352_(5, new OrcLordEntity.RecoveryMagicGoal(this, 15, 100, 20.0F));
      this.f_21345_.m_25352_(6, new WanderingFollowOwnerGoal(this, 1.5D, 10.0F, 5.0F, false));
      this.f_21345_.m_25352_(7, new TensuraTamableEntity.WanderAroundPosGoal(this) {
         public boolean m_8036_() {
            return OrcLordEntity.this.getLaughing() > 0 ? false : super.m_8036_();
         }
      });
      this.f_21345_.m_25352_(8, new RandomLookAroundGoal(this));
      this.f_21345_.m_25352_(9, new LookAtPlayerGoal(this, Player.class, 6.0F));
      this.f_21346_.m_25352_(1, new TensuraTamableEntity.TensuraOwnerHurtByTargetGoal(this));
      this.f_21346_.m_25352_(2, new TensuraTamableEntity.TensuraOwnerHurtTargetGoal(this));
      this.f_21346_.m_25352_(3, (new TensuraTamableEntity.TensuraHurtByTargetGoal(this, new Class[]{OrcEntity.class})).m_26044_(new Class[0]));
      this.f_21346_.m_25352_(4, new UndergroundTargetingEntitiesGoal(this, LivingEntity.class, false, 8.0F, this::shouldAttack));
      this.f_21346_.m_25352_(8, new ResetUniversalAngerTargetGoal(this, true));
   }

   protected boolean shouldAttack(LivingEntity entity) {
      if (entity != this && !this.m_7307_(entity)) {
         if (this.m_21826_() == null) {
            if (entity instanceof Player) {
               Player player = (Player)entity;
               if (!player.m_7500_() && !player.m_5833_()) {
                  return true;
               }
            }

            if (!(entity instanceof AbstractVillager) && !(entity instanceof AbstractIllager)) {
               if (entity instanceof Animal) {
                  return !(entity instanceof OrcEntity);
               } else {
                  return entity instanceof IronGolem;
               }
            } else {
               return true;
            }
         } else if (entity.m_7307_(this.m_21826_())) {
            return false;
         } else if (entity instanceof Mob) {
            Mob mob = (Mob)entity;
            return mob.m_5448_() == this.m_21826_();
         } else {
            return this.m_21826_().m_21214_() == entity || this.m_21826_().m_21188_() == entity;
         }
      } else {
         return false;
      }
   }

   protected void m_8097_() {
      super.m_8097_();
      this.f_19804_.m_135372_(LAUGHING, 0);
   }

   public void m_7380_(CompoundTag compound) {
      super.m_7380_(compound);
      compound.m_128405_("Laughing", this.getLaughing());
   }

   public void m_7378_(CompoundTag compound) {
      super.m_7378_(compound);
      this.setLaughing(compound.m_128451_("Laughing"));
   }

   public int getLaughing() {
      return (Integer)this.f_19804_.m_135370_(LAUGHING);
   }

   public void setLaughing(int tick) {
      this.f_19804_.m_135381_(LAUGHING, tick);
   }

   public AgeableMob m_142606_(ServerLevel pLevel, AgeableMob pOtherParent) {
      return null;
   }

   public boolean m_6673_(DamageSource source) {
      return source == DamageSource.f_19325_ || source == DamageSource.f_19310_ || source == DamageSource.f_19314_ || super.m_6673_(source);
   }

   public boolean shouldSwim() {
      return false;
   }

   public boolean canSleep() {
      return false;
   }

   public boolean canEquipSlots(EquipmentSlot slot) {
      if (!super.canEquipSlots(slot)) {
         return false;
      } else {
         return !slot.m_20743_().equals(Type.ARMOR);
      }
   }

   public boolean shouldRiderSit() {
      return false;
   }

   public void m_7332_(Entity passenger) {
      if (this.m_20363_(passenger)) {
         passenger.m_183634_();
         float radius = 1.0F;
         float angle = 0.017453292F * this.f_20883_;
         double extraX = (double)(radius * Mth.m_14031_((float)(3.141592653589793D + (double)angle)));
         double extraZ = (double)(radius * Mth.m_14089_(angle));
         double yOffset = this.m_20186_() + this.m_6048_() + passenger.m_6049_() - 1.75D;
         passenger.m_6034_(this.m_20185_() + extraX, yOffset, this.m_20189_() + extraZ);
      }
   }

   public boolean breakableBlocks(LivingEntity entity, BlockPos pos, BlockState state) {
      return state.m_204336_(TensuraTags.Blocks.BOSS_IMMUNE) ? false : ForgeEventFactory.onEntityDestroyBlock(entity, pos, state);
   }

   public boolean dropBlockLoot(LivingEntity entity, BlockState state) {
      return !state.m_204336_(TensuraTags.Blocks.SKILL_BREAK_EASY);
   }

   @Nullable
   protected ManasSkillInstance getStarved() {
      Optional<ManasSkillInstance> skill = SkillAPI.getSkillsFrom(this).getSkill((ManasSkill)UniqueSkills.STARVED.get());
      if (skill.isEmpty()) {
         return null;
      } else {
         return !((ManasSkillInstance)skill.get()).canInteractSkill(this) ? null : (ManasSkillInstance)skill.get();
      }
   }

   public boolean m_7307_(Entity entity) {
      if (super.m_7307_(entity)) {
         return true;
      } else if (entity instanceof OrcEntity) {
         OrcEntity orc = (OrcEntity)entity;
         return orc.m_21824_() == this.m_21824_();
      } else {
         return false;
      }
   }

   public boolean m_6779_(LivingEntity pTarget) {
      return this.m_7307_(pTarget) ? false : super.m_6779_(pTarget);
   }

   public SoundSource m_5720_() {
      return SoundSource.HOSTILE;
   }

   protected void m_8024_() {
      super.m_8024_();
      if (!this.m_21824_()) {
         if (this.isColliding(this, false) || this.f_19797_ % 20 == 0 && this.m_5448_() != null) {
            if (this.f_19797_ % 20 == 0) {
               List<BarrierPart> list = this.f_19853_.m_45976_(BarrierPart.class, this.m_20191_().m_82400_(1.0D));
               if (!list.isEmpty()) {
                  Iterator var2 = list.iterator();

                  while(var2.hasNext()) {
                     BarrierPart barrier = (BarrierPart)var2.next();
                     this.m_7327_(barrier);
                  }
               }
            }

            if (!this.breakBlocks(this, 2.0F, false, 1, this.inventory, true)) {
               return;
            }

            if (this.getMiscAnimation() >= 1) {
               return;
            }

            this.setMiscAnimation(8);
         }

      }
   }

   public void m_8119_() {
      super.m_8119_();
      if (this.getLaughing() > 0) {
         this.setLaughing(this.getLaughing() - 1);
      }

      if (!this.f_19853_.f_46443_) {
         if (this.m_20202_() instanceof Boat || this.m_20202_() instanceof AbstractMinecart) {
            this.m_20202_().m_6469_(DamageSource.m_19370_(this), 40.0F);
         }

         if (--this.selfRegen <= 0 && this.m_21223_() < this.m_21233_() && this.m_6084_()) {
            this.healAndEat();
         }

      }
   }

   protected void evolvingTick() {
      if (this.getEvolving() > 0) {
         this.setEvolving(this.getEvolving() - 1);
         this.m_6210_();
         this.m_5496_(SoundEvents.f_12241_, 1.0F, 1.0F);
         TensuraParticleHelper.addServerParticlesAroundSelf(this, ParticleTypes.f_123765_);
         TensuraParticleHelper.addServerParticlesAroundSelf(this, (ParticleOptions)TensuraParticles.CHAOS_EATER_EFFECT.get());
         if (this.getEvolving() == 0) {
            this.evolveToDisaster();
         }
      }

   }

   protected void healAndEat() {
      this.m_5634_(2.0F);
      this.selfRegen = 20;
      if (this.m_146895_() != null && this.getMiscAnimation() <= 1) {
         this.setMiscAnimation(7);
      } else if (this.getMiscAnimation() <= 1 && this.m_146895_() == null) {
         boolean shouldEat = this.m_21223_() < this.m_21233_() / 4.0F && (double)this.f_19796_.m_188501_() <= 0.2D || this.m_21223_() < this.m_21233_() / 8.0F;
         if (!shouldEat) {
            return;
         }

         List<OrcEntity> list = this.f_19853_.m_45976_(OrcEntity.class, this.m_20191_().m_82400_(3.0D));
         if (!list.isEmpty()) {
            Iterator var3 = list.iterator();

            while(var3.hasNext()) {
               OrcEntity sacrifice = (OrcEntity)var3.next();
               if (sacrifice != this) {
                  this.setMiscAnimation(7);
                  sacrifice.m_7998_(this, true);
                  return;
               }
            }
         }
      }

   }

   protected void miscAnimationHandler() {
      if (this.getMiscAnimation() != 0) {
         ++this.miscAnimationTicks;
         if (this.m_6084_() && !this.f_19853_.f_46443_) {
            if (this.getMiscAnimation() == 3 && this.miscAnimationTicks == 15) {
               this.areaAttack(1.5F, 0.75F);
               TensuraParticleHelper.spawnGroundSlamParticle(this, 5, 2.5F);
               this.m_9236_().m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_215778_, SoundSource.HOSTILE, 1.0F, 1.0F);
               if (this.f_19853_.m_46469_().m_46207_(GameRules.f_46132_)) {
                  SkillHelper.launchBlock(this, this.m_20182_(), 3, 1, 0.4F, 0.3F, (blockState) -> {
                     return this.m_217043_().m_188503_(3) != 1 ? false : blockState.m_204336_(TensuraTags.Blocks.EARTH_MANIPULATING);
                  }, (blockPos) -> {
                     return !blockPos.equals(this.m_20097_().m_7495_());
                  });
               }
            } else {
               LivingEntity passenger;
               LivingEntity target;
               if (this.getMiscAnimation() == 4) {
                  passenger = this.m_5448_();
                  if (this.miscAnimationTicks == 10 && this.m_146895_() == null) {
                     if (passenger != null && passenger.m_20270_(this) <= 4.0F && passenger != this) {
                        passenger.m_7998_(this, true);
                     }
                  } else if (this.miscAnimationTicks == 20) {
                     Entity var3 = this.m_146895_();
                     if (var3 instanceof LivingEntity) {
                        target = (LivingEntity)var3;
                        target.m_8127_();
                        target.m_6469_(DamageSource.m_19370_(this), (float)(this.m_21133_(Attributes.f_22281_) * 2.0D));
                        this.m_9236_().m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_11913_, SoundSource.HOSTILE, 1.0F, 1.0F);
                     }
                  }
               } else if (this.getMiscAnimation() == 7) {
                  if (this.miscAnimationTicks == 5 && this.m_146895_() == null) {
                     target = this.m_5448_();
                     if (target != null && target.m_20270_(this) <= 4.0F && target != this) {
                        target.m_7998_(this, true);
                     }
                  } else if (this.miscAnimationTicks == 15) {
                     Entity var2 = this.m_146895_();
                     if (var2 instanceof LivingEntity) {
                        passenger = (LivingEntity)var2;
                        passenger.m_8127_();
                        float HP = passenger.m_21223_();
                        passenger.m_6469_(DamageSource.m_19370_(this).m_19380_(), (float)(this.m_21133_(Attributes.f_22281_) * 3.0D));
                        if (!passenger.m_6084_()) {
                           this.m_5634_(HP * 2.0F);
                        } else {
                           this.m_5634_(50.0F);
                        }

                        this.m_9236_().m_6263_((Player)null, this.m_20185_(), this.m_20186_(), this.m_20189_(), SoundEvents.f_215769_, SoundSource.HOSTILE, 1.0F, 1.0F);
                     }
                  }
               }
            }
         }

         if (this.miscAnimationTicks >= this.getAnimationTick(this.getMiscAnimation())) {
            this.setMiscAnimation(0);
            this.miscAnimationTicks = 0;
         }
      }

   }

   public void areaAttack(float damageMultiplier, float upVector) {
      AABB aabb = this.m_20191_().m_82400_(this.m_21133_((Attribute)ForgeMod.ATTACK_RANGE.get()));
      List<LivingEntity> list = this.f_19853_.m_6443_(LivingEntity.class, aabb, (entity) -> {
         return !entity.m_7307_(this) && entity != this.m_21826_() && entity != this;
      });
      if (!list.isEmpty()) {
         Iterator var5 = list.iterator();

         while(true) {
            LivingEntity target;
            LivingEntity orcTarget;
            do {
               if (!var5.hasNext()) {
                  return;
               }

               target = (LivingEntity)var5.next();
               if (!(target instanceof OrcEntity)) {
                  break;
               }

               OrcEntity orc = (OrcEntity)target;
               if (orc.m_21824_() || this.m_5448_() == orc) {
                  break;
               }

               orcTarget = orc.m_5448_();
            } while(orcTarget == null || orcTarget != this && !orcTarget.m_7307_(this));

            target.m_6469_(DamageSource.m_19370_(this), (float)(this.m_21133_(Attributes.f_22281_) * (double)damageMultiplier));
            target.m_20184_().m_82520_(0.0D, (double)upVector, 0.0D);
         }
      }
   }

   private int getAnimationTick(int miscAnimation) {
      byte var10000;
      switch(miscAnimation) {
      case 2:
         var10000 = 15;
         break;
      case 3:
         var10000 = 22;
         break;
      case 4:
         var10000 = 35;
         break;
      case 5:
         var10000 = 25;
         break;
      case 6:
         var10000 = 30;
         break;
      case 7:
         var10000 = 29;
         break;
      case 8:
         var10000 = 27;
         break;
      default:
         var10000 = 11;
      }

      return var10000;
   }

   public void m_7334_(Entity pEntity) {
      if (!(pEntity instanceof OrcEntity)) {
         super.m_7334_(pEntity);
      }
   }

   protected void hurtShield(ItemStack stack, float pAmount) {
      if (pAmount >= 3.0F) {
         stack.m_41622_(1 + Mth.m_14143_(pAmount), this, (living) -> {
            living.m_21166_(EquipmentSlot.OFFHAND);
         });
      }

      this.m_5496_(SoundEvents.f_12346_, 1.0F, 1.0F);
      this.setMiscAnimation(1);
   }

   public boolean m_6469_(DamageSource pSource, float pAmount) {
      boolean hurt = super.m_6469_(pSource, pAmount);
      if (hurt) {
         Entity var5 = pSource.m_7639_();
         if (var5 instanceof LivingEntity) {
            LivingEntity damageSource = (LivingEntity)var5;
            if (!damageSource.m_6084_()) {
               return true;
            }

            if (damageSource instanceof Player) {
               Player player = (Player)damageSource;
               if (player.m_7500_() || player.m_5833_()) {
                  return true;
               }
            }

            List<OrcEntity> list = this.f_19853_.m_6443_(OrcEntity.class, this.m_20191_().m_82400_(32.0D), (entity) -> {
               return !entity.m_21824_() && !(entity instanceof OrcLordEntity);
            });
            if (!list.isEmpty()) {
               list.forEach((orc) -> {
                  orc.m_6710_(damageSource);
               });
            }
         }
      }

      return hurt;
   }

   public boolean m_7327_(Entity pEntity) {
      if (super.m_7327_(pEntity) && pEntity instanceof LivingEntity) {
         LivingEntity living = (LivingEntity)pEntity;
         if (this.getStarved() != null) {
            SkillHelper.addEffectWithSource(living, this, (MobEffect)TensuraMobEffects.CORROSION.get(), 200, this.getClass() == OrcLordEntity.class ? 3 : 2, true);
         }

         return true;
      } else {
         return false;
      }
   }

   public void m_8035_() {
      super.m_8035_();
      if (this.getMiscAnimation() == 0) {
         this.setMiscAnimation(6);
      }

   }

   public boolean m_214076_(ServerLevel pLevel, LivingEntity pEntity) {
      boolean wasKilled = super.m_214076_(pLevel, pEntity);
      if (wasKilled && this.m_6084_()) {
         if (this.m_5448_() == null || !this.m_5448_().m_6084_()) {
            this.setLaughing(20);
            this.m_5496_(SoundEvents.f_12241_, 10.0F, 1.0F);
         }

         TensuraParticleHelper.addServerParticlesAroundSelf(this, ParticleTypes.f_123749_);
         TensuraParticleHelper.addServerParticlesAroundSelf(this, ParticleTypes.f_123749_, 1.0D);
         float heal = 5.0F;
         AttributeInstance HP = this.m_21051_(Attributes.f_22276_);
         if (HP != null) {
            HP.m_22100_(HP.m_22115_() + (double)heal);
         }

         this.m_5634_(30.0F);
         this.m_7292_(new MobEffectInstance(MobEffects.f_19596_, 400, 0, false, false));
         List<OrcEntity> list = this.f_19853_.m_45976_(OrcEntity.class, this.m_20191_().m_82400_(16.0D));
         if (!list.isEmpty()) {
            Iterator var7 = list.iterator();

            while(var7.hasNext()) {
               OrcEntity sub = (OrcEntity)var7.next();
               if (sub != this && !sub.m_21824_()) {
                  sub.m_7292_(new MobEffectInstance((MobEffect)TensuraMobEffects.STRENGTHEN.get(), 400, 0, false, false));
                  sub.m_7292_(new MobEffectInstance(MobEffects.f_19596_, 400, 0, false, false));
               }
            }
         }

         TensuraEPCapability.getFrom(this).ifPresent((cap) -> {
            double targetEP = SkillUtils.getEPGain(pEntity, this);
            double percentage = (double)TensuraGameRules.getEPGain(this.f_19853_) > 0.5D ? (double)TensuraGameRules.getEPGain(this.f_19853_) : 0.5D - (double)TensuraGameRules.getEPGain(this.f_19853_);
            cap.setEP(this, cap.getEP() + targetEP * percentage);
            if (this.getClass() == OrcLordEntity.class && cap.getEP() >= 200000.0D) {
               this.m_21573_().m_26573_();
               this.m_20331_(true);
               this.setEvolving(40);
            }

         });
      }

      return wasKilled;
   }

   protected void evolveToDisaster() {
      CompoundTag tag = this.serializeNBT();
      this.m_146870_();
      OrcDisasterEntity orc = new OrcDisasterEntity((EntityType)TensuraEntityTypes.ORC_DISASTER.get(), this.f_19853_);
      orc.m_20258_(tag);
      Level var4 = this.f_19853_;
      if (var4 instanceof ServerLevel) {
         ServerLevel serverLevel = (ServerLevel)var4;
         orc.m_6518_(serverLevel, this.f_19853_.m_6436_(orc.m_20183_()), MobSpawnType.CONVERSION, (SpawnGroupData)null, (CompoundTag)null);
      }

      RaceHelper.applyBaseAttribute((AttributeSupplier)OrcDisasterEntity.setAttributes(), orc);
      orc.m_21153_(orc.m_21233_());
      RaceHelper.updateSpiritualHP(orc);
      RaceHelper.updateEntityEPCount(orc);
      orc.setEvolving(0);
      orc.m_20331_(false);
      this.f_19853_.m_7967_(orc);
      orc.m_6210_();
      this.f_19853_.m_5594_((Player)null, orc.m_20183_(), SoundEvents.f_12306_, SoundSource.PLAYERS, 10.0F, 1.0F);
      this.f_19853_.m_7967_(orc);
      TensuraParticleHelper.addServerParticlesAroundSelf(this, (ParticleOptions)TensuraParticles.CHAOS_EATER_EFFECT.get());
      TensuraParticleHelper.addServerParticlesAroundSelf(this, ParticleTypes.f_123765_, 2.0D);
      TensuraParticleHelper.addServerParticlesAroundSelf(this, ParticleTypes.f_123747_);
      TensuraParticleHelper.addServerParticlesAroundSelf(this, ParticleTypes.f_123812_);
   }

   protected void dropBlood() {
      if (!this.f_19796_.m_188499_()) {
         this.m_19998_((ItemLike)TensuraMobDropItems.ROYAL_BLOOD.get());
      }
   }

   public boolean m_6785_(double pDistanceToClosestPlayer) {
      return false;
   }

   protected boolean removeWhenNoAction() {
      return false;
   }

   protected void m_213945_(RandomSource pRandom, DifficultyInstance pDifficulty) {
      int i = pRandom.m_188503_(100);
      ItemStack stack = new ItemStack((ItemLike)TensuraToolItems.MEAT_CRUSHER.get());
      if (i < 5) {
         stack = new ItemStack((ItemLike)TensuraToolItems.BLADE_TIGER_SCYTHE.get());
      }

      this.m_8061_(EquipmentSlot.MAINHAND, stack);
      this.inventory.m_6836_(4, stack);
      this.inventory.m_6596_();
   }

   private <E extends IAnimatable> PlayState predicate(AnimationEvent<E> event) {
      if (this.getLaughing() <= 0 && this.getEvolving() <= 0) {
         if (event.isMoving()) {
            if (this.m_20096_() && this.m_21660_()) {
               event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.orc_lord.run", EDefaultLoopTypes.LOOP));
            } else if (this.m_20096_() || !this.isInFluidType()) {
               event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.orc_lord.walk", EDefaultLoopTypes.LOOP));
            }
         } else if (this.getLaughing() > 0) {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.orc_lord.laugh", EDefaultLoopTypes.LOOP));
         } else {
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.orc_lord.idle", EDefaultLoopTypes.LOOP));
         }

         return PlayState.CONTINUE;
      } else {
         event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.orc_lord.laugh", EDefaultLoopTypes.LOOP));
         return PlayState.CONTINUE;
      }
   }

   private <E extends IAnimatable> PlayState miscPredicate(AnimationEvent<E> event) {
      if (event.getController().getAnimationState().equals(AnimationState.Stopped)) {
         event.getController().markNeedsReload();
         switch(this.getMiscAnimation()) {
         case 1:
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.orc_lord.punch", EDefaultLoopTypes.PLAY_ONCE));
            break;
         case 2:
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.orc_lord.cleaver_swing", EDefaultLoopTypes.PLAY_ONCE));
            break;
         case 3:
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.orc_lord.cleaver_slam", EDefaultLoopTypes.PLAY_ONCE));
            break;
         case 4:
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.orc_lord.crush", EDefaultLoopTypes.PLAY_ONCE));
            break;
         case 5:
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.orc_lord.yell", EDefaultLoopTypes.PLAY_ONCE));
            break;
         case 6:
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.orc_lord.eat_item", EDefaultLoopTypes.PLAY_ONCE));
            break;
         case 7:
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.orc_lord.eat_mob", EDefaultLoopTypes.PLAY_ONCE));
            break;
         case 8:
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.orc_lord.recover", EDefaultLoopTypes.PLAY_ONCE));
            break;
         case 9:
            event.getController().setAnimation((new AnimationBuilder()).addAnimation("animation.orc_lord.shield", EDefaultLoopTypes.PLAY_ONCE));
         }
      }

      return PlayState.CONTINUE;
   }

   public void registerControllers(AnimationData data) {
      data.addAnimationController(new AnimationController(this, "controller", 0.0F, this::predicate));
      data.addAnimationController(new AnimationController(this, "miscController", 0.0F, this::miscPredicate));
   }

   static {
      LAUGHING = SynchedEntityData.m_135353_(OrcLordEntity.class, EntityDataSerializers.f_135028_);
   }

   public class OrcLordAttackGoal extends HumanoidNPCEntity.NPCMeleeAttackGoal {
      public final OrcLordEntity orc;

      public OrcLordAttackGoal(OrcLordEntity beast) {
         super(beast, 2.0D, true);
         this.orc = beast;
      }

      public void m_8037_() {
         if (this.orc.getMiscAnimation() <= 2) {
            super.m_8037_();
         }
      }

      protected void m_6739_(LivingEntity pEnemy, double pDistToEnemySqr) {
         double d0 = this.m_6639_(pEnemy);
         if (this.orc.getMiscAnimation() == 0 || this.orc.getMiscAnimation() == 1) {
            int randomAttack = this.randomAttack(pEnemy, pDistToEnemySqr);
            if (randomAttack == 0) {
               return;
            }

            double var10000;
            switch(randomAttack) {
            case 3:
               var10000 = d0 + 4.0D;
               break;
            case 4:
               var10000 = d0 + 9.0D;
               break;
            default:
               var10000 = d0;
            }

            double attackRange = var10000;
            if (pDistToEnemySqr <= attackRange && this.m_25564_()) {
               this.m_25563_();
               this.orc.setMiscAnimation(randomAttack);
               if (randomAttack == 3 || randomAttack == 7) {
                  this.orc.m_21573_().m_26573_();
               }

               if (randomAttack == 1 || randomAttack == 2) {
                  this.orc.m_7327_(pEnemy);
               }
            }
         }

      }

      protected int randomAttack(LivingEntity target, double distSqr) {
         if ((double)this.orc.f_19796_.m_188501_() <= 0.2D && distSqr <= 9.0D && !target.m_20159_() && !target.m_20160_() && target != this.orc) {
            return target.m_21223_() <= target.m_21233_() / 5.0F ? 7 : 4;
         } else if (!this.orc.m_21205_().m_41619_()) {
            return (double)this.orc.f_19796_.m_188501_() <= 0.5D ? 3 : 2;
         } else {
            return 1;
         }
      }

      protected double m_6639_(LivingEntity pAttackTarget) {
         double attackRange = this.orc.m_21133_((Attribute)ForgeMod.ATTACK_RANGE.get());
         return super.m_6639_(pAttackTarget) + attackRange * attackRange;
      }
   }

   static class SummonOrcsGoal extends BasicSpellGoal {
      private final TargetingConditions orcCountTargeting = TargetingConditions.m_148353_().m_26883_(32.0D).m_148355_().m_26893_();
      private final OrcLordEntity orcLord;
      protected boolean summonAtSpawn;

      public SummonOrcsGoal(OrcLordEntity creature, int castWarmUpTime, int castingInterval) {
         super(creature, castWarmUpTime, castingInterval);
         this.orcLord = creature;
      }

      public boolean m_8036_() {
         if (this.orcLord.m_21824_()) {
            return false;
         } else if (!super.m_8036_() && this.summonAtSpawn) {
            return false;
         } else {
            int i = this.orcLord.f_19853_.m_45971_(OrcEntity.class, this.orcCountTargeting, this.orcLord, this.orcLord.m_20191_().m_82400_(32.0D)).size();
            return this.orcLord.f_19796_.m_188503_(30) + 1 > i;
         }
      }

      public void m_8056_() {
         super.m_8056_();
         this.orcLord.setMiscAnimation(5);
      }

      protected void performSpellCasting() {
         if (!this.summonAtSpawn) {
            this.summonAtSpawn = true;
         }

         this.summonOrcRandomPos(4, 0, 7);
      }

      protected void summonOrcRandomPos(int amount, int minRadius, int maxRadius) {
         ServerLevel serverLevel = (ServerLevel)this.orcLord.f_19853_;
         int i = Mth.m_14107_(this.orcLord.m_20185_());
         int j = Mth.m_14107_(this.orcLord.m_20186_());
         int k = Mth.m_14107_(this.orcLord.m_20189_());

         for(int orcs = 0; orcs < amount; ++orcs) {
            OrcEntity orc = new OrcEntity((EntityType)TensuraEntityTypes.ORC.get(), serverLevel);

            for(int l = 0; l < 50; ++l) {
               int i1 = i + Mth.m_216271_(this.orcLord.f_19796_, minRadius, maxRadius) * Mth.m_216271_(this.orcLord.f_19796_, -1, 1);
               int j1 = j + Mth.m_216271_(this.orcLord.f_19796_, minRadius, maxRadius) * Mth.m_216271_(this.orcLord.f_19796_, -1, 1);
               int k1 = k + Mth.m_216271_(this.orcLord.f_19796_, minRadius, maxRadius) * Mth.m_216271_(this.orcLord.f_19796_, -1, 1);
               BlockPos blockpos = new BlockPos(i1, j1, k1);
               EntityType<?> entitytype = (EntityType)TensuraEntityTypes.ORC.get();
               net.minecraft.world.entity.SpawnPlacements.Type type = SpawnPlacements.m_21752_(entitytype);
               if (NaturalSpawner.m_47051_(type, serverLevel, blockpos, entitytype) && SpawnPlacements.m_217074_(entitytype, serverLevel, MobSpawnType.REINFORCEMENT, blockpos, serverLevel.f_46441_)) {
                  orc.m_6034_((double)i1, (double)j1, (double)k1);
                  if (serverLevel.m_45784_(orc) && serverLevel.m_45786_(orc)) {
                     orc.m_6518_(serverLevel, serverLevel.m_6436_(orc.m_20183_()), MobSpawnType.MOB_SUMMONED, (SpawnGroupData)null, (CompoundTag)null);
                     orc.m_6710_(this.orcLord.m_5448_());
                     this.makeKnight(orc);
                     serverLevel.m_47205_(orc);
                     break;
                  }
               }
            }
         }

      }

      protected void makeKnight(OrcEntity orc) {
         ItemStack helmet = new ItemStack(Items.f_42468_);
         ItemStack chestplate = new ItemStack(Items.f_42469_);
         ItemStack leggings = new ItemStack(Items.f_42470_);
         ItemStack boots = new ItemStack(Items.f_42471_);
         ItemStack weapon = new ItemStack((ItemLike)(this.orcLord.f_19796_.m_188499_() ? (ItemLike)TensuraToolItems.IRON_SPEAR.get() : Items.f_42386_));
         if ((double)this.orcLord.f_19796_.m_188501_() <= 0.3D) {
            if (this.orcLord.f_19796_.m_188499_()) {
               weapon = new ItemStack((ItemLike)TensuraToolItems.LONG_BOW.get());
            } else {
               weapon = new ItemStack(Items.f_42717_);
            }
         }

         if ((double)this.orcLord.f_19796_.m_188501_() <= 0.05D) {
            helmet = new ItemStack(Items.f_42480_);
            chestplate = new ItemStack(Items.f_42481_);
            leggings = new ItemStack(Items.f_42482_);
            boots = new ItemStack(Items.f_42483_);
            weapon = new ItemStack(Items.f_42396_);
         }

         orc.m_8061_(EquipmentSlot.HEAD, helmet);
         orc.m_8061_(EquipmentSlot.CHEST, chestplate);
         orc.m_8061_(EquipmentSlot.LEGS, leggings);
         orc.m_8061_(EquipmentSlot.FEET, boots);
         orc.m_8061_(EquipmentSlot.MAINHAND, weapon);
         if ((double)this.orcLord.f_19796_.m_188501_() <= 0.1D) {
            orc.m_8061_(EquipmentSlot.OFFHAND, new ItemStack(Items.f_42740_));
         }

      }

      protected boolean noTargetActivation() {
         return !this.summonAtSpawn;
      }

      protected SoundEvent getCastingSoundEvent() {
         return SoundEvents.f_215769_;
      }
   }

   static class RecoveryMagicGoal extends BasicSpellGoal {
      private final OrcLordEntity orcLord;
      private final float healAmount;

      public RecoveryMagicGoal(OrcLordEntity creature, int castWarmUpTime, int castingInterval, float healAmount) {
         super(creature, castWarmUpTime, castingInterval);
         this.orcLord = creature;
         this.healAmount = healAmount;
      }

      public boolean m_8036_() {
         return !super.m_8036_() ? false : this.orcLord.shouldHeal();
      }

      public void m_8056_() {
         super.m_8056_();
         this.orcLord.setMiscAnimation(8);
      }

      protected void performSpellCasting() {
         this.orcLord.m_5634_(this.healAmount);
         this.orcLord.m_7292_(new MobEffectInstance(MobEffects.f_19619_, 30, 0, false, false, false));
         TensuraParticleHelper.addServerParticlesAroundSelf(this.orcLord, ParticleTypes.f_123810_, 2.0D);
         TensuraParticleHelper.addServerParticlesAroundSelf(this.orcLord, ParticleTypes.f_123749_, 2.0D);
      }

      protected boolean noTargetActivation() {
         return true;
      }

      protected SoundEvent getSpellPrepareSound() {
         return SoundEvents.f_12053_;
      }

      protected SoundEvent getCastingSoundEvent() {
         return SoundEvents.f_11887_;
      }
   }
}
