package com.github.manasmods.tensura.client.particle;

import net.minecraft.client.multiplayer.ClientLevel;
import net.minecraft.client.particle.Particle;
import net.minecraft.client.particle.ParticleProvider;
import net.minecraft.client.particle.ParticleRenderType;
import net.minecraft.client.particle.SpriteSet;
import net.minecraft.client.particle.TextureSheetParticle;
import net.minecraft.core.particles.SimpleParticleType;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.api.distmarker.OnlyIn;

public class SimpleEffectParticle extends TextureSheetParticle {
   private final SpriteSet sprites;

   public SimpleEffectParticle(ClientLevel level, double xCoord, double yCoord, double zCoord, SpriteSet spriteSet, double xd, double yd, double zd, float rCol, float gCol, float bCol, float gravity) {
      super(level, xCoord, yCoord, zCoord, xd, yd, zd);
      this.f_107215_ = xd;
      this.f_107216_ = yd;
      this.f_107217_ = zd;
      this.f_107663_ *= 1.0F;
      this.m_6569_(1.15F);
      this.f_107226_ = gravity;
      this.f_107225_ = 5 + (int)(Math.random() * 25.0D);
      this.sprites = spriteSet;
      this.m_108339_(spriteSet);
      float f = this.f_107223_.m_188501_() * 0.3F + 0.7F;
      this.f_107227_ = rCol * f;
      this.f_107228_ = gCol * f;
      this.f_107229_ = bCol * f;
   }

   public void m_5989_() {
      super.m_5989_();
      this.m_108339_(this.sprites);
   }

   public ParticleRenderType m_7556_() {
      return ParticleRenderType.f_107430_;
   }

   @OnlyIn(Dist.CLIENT)
   public static class SmallGustProvider implements ParticleProvider<SimpleParticleType> {
      private final SpriteSet sprites;

      public SmallGustProvider(SpriteSet spriteSet) {
         this.sprites = spriteSet;
      }

      public Particle createParticle(SimpleParticleType particleType, ClientLevel level, double x, double y, double z, double dx, double dy, double dz) {
         return new SimpleBubbleParticle(level, x, y, z, this.sprites, dx, dy, dz, 1.0F, 1.0F, 1.0F, 0.0F);
      }
   }

   @OnlyIn(Dist.CLIENT)
   public static class ChaosEaterProvider implements ParticleProvider<SimpleParticleType> {
      private final SpriteSet sprites;

      public ChaosEaterProvider(SpriteSet spriteSet) {
         this.sprites = spriteSet;
      }

      public Particle createParticle(SimpleParticleType particleType, ClientLevel level, double x, double y, double z, double dx, double dy, double dz) {
         return new SimpleBubbleParticle(level, x, y, z, this.sprites, dx, dy, dz, 0.305F, 0.007F, 0.035F, -0.1F);
      }
   }

   @OnlyIn(Dist.CLIENT)
   public static class BogProvider implements ParticleProvider<SimpleParticleType> {
      private final SpriteSet sprites;

      public BogProvider(SpriteSet spriteSet) {
         this.sprites = spriteSet;
      }

      public Particle createParticle(SimpleParticleType particleType, ClientLevel level, double x, double y, double z, double dx, double dy, double dz) {
         return new SimpleBubbleParticle(level, x, y, z, this.sprites, dx, dy, dz, 0.2F, 0.17F, 0.14F, 0.55F);
      }
   }

   @OnlyIn(Dist.CLIENT)
   public static class MudProvider implements ParticleProvider<SimpleParticleType> {
      private final SpriteSet sprites;

      public MudProvider(SpriteSet spriteSet) {
         this.sprites = spriteSet;
      }

      public Particle createParticle(SimpleParticleType particleType, ClientLevel level, double x, double y, double z, double dx, double dy, double dz) {
         return new SimpleEffectParticle(level, x, y, z, this.sprites, dx, dy, dz, 0.64F, 0.39F, 0.2F, 0.45F);
      }
   }

   @OnlyIn(Dist.CLIENT)
   public static class SteamProvider implements ParticleProvider<SimpleParticleType> {
      private final SpriteSet sprites;

      public SteamProvider(SpriteSet spriteSet) {
         this.sprites = spriteSet;
      }

      public Particle createParticle(SimpleParticleType particleType, ClientLevel level, double x, double y, double z, double dx, double dy, double dz) {
         return new SimpleEffectParticle(level, x, y, z, this.sprites, dx, dy, dz, 0.83F, 0.93F, 0.98F, -0.35F);
      }
   }

   @OnlyIn(Dist.CLIENT)
   public static class WaterProvider implements ParticleProvider<SimpleParticleType> {
      private final SpriteSet sprites;

      public WaterProvider(SpriteSet spriteSet) {
         this.sprites = spriteSet;
      }

      public Particle createParticle(SimpleParticleType particleType, ClientLevel level, double x, double y, double z, double dx, double dy, double dz) {
         return new SimpleEffectParticle(level, x, y, z, this.sprites, dx, dy, dz, 0.18F, 0.7F, 0.95F, 0.35F);
      }
   }

   @OnlyIn(Dist.CLIENT)
   public static class PoisonProvider implements ParticleProvider<SimpleParticleType> {
      private final SpriteSet sprites;

      public PoisonProvider(SpriteSet spriteSet) {
         this.sprites = spriteSet;
      }

      public Particle createParticle(SimpleParticleType particleType, ClientLevel level, double x, double y, double z, double dx, double dy, double dz) {
         return new SimpleEffectParticle(level, x, y, z, this.sprites, dx, dy, dz, 0.54F, 0.16F, 0.71F, 0.35F);
      }
   }

   @OnlyIn(Dist.CLIENT)
   public static class AcidProvider implements ParticleProvider<SimpleParticleType> {
      private final SpriteSet sprites;

      public AcidProvider(SpriteSet spriteSet) {
         this.sprites = spriteSet;
      }

      public Particle createParticle(SimpleParticleType particleType, ClientLevel level, double x, double y, double z, double dx, double dy, double dz) {
         return new SimpleEffectParticle(level, x, y, z, this.sprites, dx, dy, dz, 0.42F, 0.87F, 0.23F, 0.35F);
      }
   }

   @OnlyIn(Dist.CLIENT)
   public static class ParalysingProvider implements ParticleProvider<SimpleParticleType> {
      private final SpriteSet sprites;

      public ParalysingProvider(SpriteSet spriteSet) {
         this.sprites = spriteSet;
      }

      public Particle createParticle(SimpleParticleType particleType, ClientLevel level, double x, double y, double z, double dx, double dy, double dz) {
         return new SimpleEffectParticle(level, x, y, z, this.sprites, dx, dy, dz, 0.95F, 0.95F, 0.2F, 0.2F);
      }
   }
}
