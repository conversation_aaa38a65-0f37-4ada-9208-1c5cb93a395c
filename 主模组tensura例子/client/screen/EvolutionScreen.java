package com.github.manasmods.tensura.client.screen;

import com.github.manasmods.manascore.api.client.gui.FontRenderHelper;
import com.github.manasmods.manascore.api.client.gui.widget.ImagePredicateButton;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.client.TensuraGUIHelper;
import com.github.manasmods.tensura.client.keybind.TensuraKeybinds;
import com.github.manasmods.tensura.menu.EvolutionMenu;
import com.github.manasmods.tensura.network.TensuraNetwork;
import com.github.manasmods.tensura.network.play2server.SetTrackedEvolutionPacket;
import com.github.manasmods.tensura.race.Race;
import com.github.manasmods.tensura.util.Cached;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.UnmodifiableIterator;
import com.mojang.blaze3d.platform.InputConstants;
import com.mojang.blaze3d.systems.RenderSystem;
import com.mojang.blaze3d.vertex.PoseStack;
import java.awt.Color;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import net.minecraft.ChatFormatting;
import net.minecraft.client.gui.components.EditBox;
import net.minecraft.client.gui.components.Button.OnPress;
import net.minecraft.client.gui.components.Button.OnTooltip;
import net.minecraft.client.gui.screens.Screen;
import net.minecraft.client.gui.screens.inventory.AbstractContainerScreen;
import net.minecraft.client.gui.screens.inventory.InventoryScreen;
import net.minecraft.client.renderer.GameRenderer;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.util.Mth;
import net.minecraft.world.entity.player.Inventory;
import net.minecraft.world.entity.player.Player;
import org.jetbrains.annotations.Nullable;

public class EvolutionScreen extends AbstractContainerScreen<EvolutionMenu> {
   private static final ResourceLocation BACKGROUND = new ResourceLocation("tensura", "textures/gui/evolution/evolution_gui.png");
   private static final ResourceLocation RACE_BUTTON = new ResourceLocation("tensura", "textures/gui/evolution/race_button.png");
   private static final ResourceLocation SCROLL_BAR = new ResourceLocation("tensura", "textures/gui/scroll_bar.png");
   private static final ResourceLocation TRACK_BUTTON = new ResourceLocation("tensura", "textures/gui/evolution/track_button.png");
   private static final ResourceLocation EVOLVE_BUTTON = new ResourceLocation("tensura", "textures/gui/evolution/evolve_button.png");
   private final Player player;
   private Cached<List<Race>, String> evolutions;
   private EditBox searchField;
   private boolean scrolling;
   private float scrollOffs;
   private int startIndex;
   private Race selectedRace;
   private Race trackedRace;
   private String evolutionProgress = "0%";
   private String selectedRaceEvolutionProgress;
   private final DecimalFormat roundDouble = new DecimalFormat("#");

   public EvolutionScreen(EvolutionMenu pMenu, Inventory pPlayerInventory, Component pTitle) {
      super(pMenu, pPlayerInventory, pTitle);
      this.f_97726_ = 256;
      this.f_97727_ = 163;
      this.player = pPlayerInventory.f_35978_;
   }

   protected void m_7856_() {
      super.m_7856_();
      ImmutableList<ImagePredicateButton> tabs = TensuraGUIHelper.addMenuTabs(this, 0, true);
      UnmodifiableIterator var2 = tabs.iterator();

      while(var2.hasNext()) {
         ImagePredicateButton button = (ImagePredicateButton)var2.next();
         this.m_142416_(button);
      }

      this.updateRaceVariables(TensuraPlayerCapability.getTrackedEvolution(this.player), true);
      if (this.selectedRace == null) {
         this.updateRaceVariables(this.trackedRace, false);
      }

      this.scrollOffs = 0.0F;
      this.startIndex = 0;
      this.searchField = new EditBox(this.f_96547_, this.getGuiLeft() + 116, this.getGuiTop() + 36, 103, 9, Component.m_237119_());
      this.searchField.m_94182_(false);
      this.searchField.m_94151_((s) -> {
         if (!s.isEmpty()) {
            this.scrollOffs = 0.0F;
            this.startIndex = 0;
         }
      });
      this.m_142416_(this.searchField);
      this.evolutions = new Cached(() -> {
         Race race = TensuraPlayerCapability.getRace(this.player);
         if (race != null && !race.getNextEvolutions(this.player).isEmpty()) {
            List<Race> filteredRaceList = new ArrayList(race.getNextEvolutions(this.player));
            if (!this.searchField.m_94155_().isEmpty() && !this.searchField.m_94155_().isBlank()) {
               String filterValue = this.searchField.m_94155_().toLowerCase();
               filteredRaceList.removeIf((race1) -> {
                  if (race1.getName() == null) {
                     return true;
                  } else {
                     return !race1.getName().getString().toLowerCase().contains(filterValue);
                  }
               });
            } else {
               filteredRaceList.removeIf((race1) -> {
                  return !(race1 instanceof Race);
               });
            }

            return filteredRaceList;
         } else {
            return new ArrayList();
         }
      }, (info) -> {
         if (info.lastCallbackReference == null || !((String)info.lastCallbackReference).equals(this.searchField.m_94155_())) {
            info.lastCallbackReference = this.searchField.m_94155_();
            info.needsUpdate = true;
         }

         return info;
      });
      ResourceLocation var10006 = TRACK_BUTTON;
      OnPress var10007 = (buttonx) -> {
         if (this.selectedRace != null && this.selectedRace != this.trackedRace) {
            TensuraNetwork.INSTANCE.sendToServer(new SetTrackedEvolutionPacket(this.selectedRace));
            this.updateRaceVariables(this.selectedRace, true);
         }

      };
      OnTooltip var10008 = (buttonx, poseStack, i, i1) -> {
      };
      EvolutionMenu var10009 = (EvolutionMenu)this.f_97732_;
      Objects.requireNonNull(var10009);
      ImagePredicateButton trackButton = new ImagePredicateButton(0, 0, 43, 13, var10006, var10007, var10008, var10009::check);
      trackButton.f_93620_ = this.getGuiLeft() + 98;
      trackButton.f_93621_ = this.getGuiTop() + 142;
      trackButton.hideToolTip();
      this.m_142416_(trackButton);
   }

   protected void m_7286_(PoseStack pPoseStack, float pPartialTick, int pMouseX, int pMouseY) {
      this.m_7333_(pPoseStack);
      RenderSystem.m_157427_(GameRenderer::m_172817_);
      RenderSystem.m_157429_(1.0F, 1.0F, 1.0F, 1.0F);
      RenderSystem.m_157456_(0, BACKGROUND);
      int x = (this.f_96543_ - this.f_97726_) / 2;
      int y = (this.f_96544_ - this.f_97727_) / 2;
      this.m_93228_(pPoseStack, x, y, 0, 0, this.f_97726_, this.f_97727_);
      TensuraGUIHelper.renderTabIcon(pPoseStack, this, 0, pMouseX, pMouseY);
      this.renderInfo(pPoseStack, pMouseX, pMouseY);
      InventoryScreen.m_98850_(this.getGuiLeft() + 33, this.getGuiTop() + 122, 30, (float)(this.getGuiLeft() + 33 - pMouseX), (float)(this.getGuiTop() + 122 - 50 - pMouseY), this.player);
      int k = (int)(25.0F * this.scrollOffs);
      RenderSystem.m_157456_(0, SCROLL_BAR);
      m_93133_(pPoseStack, this.f_97735_ + 211, this.f_97736_ + 49 + k, 0.0F, this.isScrollBarActive() ? 13.0F : 0.0F, 10, 13, 10, 26);
      List<Race> filteredRaces = (List)this.evolutions.getValue();
      int lastVisibleElementIndex = Math.min(this.startIndex + 3, filteredRaces.size());
      this.renderButtons(pPoseStack, pMouseX, pMouseY, lastVisibleElementIndex, filteredRaces);
   }

   private void renderButtons(PoseStack pPoseStack, int pMouseX, int pMouseY, int pLastVisibleElementIndex, List<Race> list) {
      int i;
      int x;
      boolean hovering;
      for(i = this.startIndex; i < pLastVisibleElementIndex && i < list.size(); ++i) {
         x = this.getGuiLeft() + 94;
         int y = this.getGuiTop() + 48 + (i - this.startIndex) * 13;
         int offset = 0;
         hovering = TensuraGUIHelper.mouseOver(pMouseX, pMouseY, x - 1, x + 113, y - 1, y + 13);
         if (hovering) {
            offset = 13;
         }

         RenderSystem.m_157456_(0, RACE_BUTTON);
         m_93133_(pPoseStack, x, y, 0.0F, (float)offset, 113, 13, 113, 26);
         Race race = (Race)list.get(i);
         MutableComponent name = Component.m_237115_("gui.none");
         if (race.getName() != null) {
            String raceName = race.getName().getString();
            if (race == this.trackedRace) {
               name = Component.m_237113_(raceName + " (TRACKED)");
            } else {
               name = Component.m_237113_(raceName);
            }
         }

         int color = race == this.trackedRace ? (Integer)Optional.ofNullable(ChatFormatting.GOLD.m_126665_()).orElse(Color.YELLOW.getRGB()) : Color.WHITE.getRGB();
         TensuraGUIHelper.renderScaledShadowText(pPoseStack, this.f_96547_, TensuraGUIHelper.shortenTextComponent((Component)name, 22), (float)(this.getGuiLeft() + 98), (float)(this.getGuiTop() + 52 + (i - this.startIndex) * 13), 104.0F, 9.0F, color, 2.0F, 0.01F);
         if (hovering) {
            this.m_96602_(pPoseStack, name, pMouseX, pMouseY);
         }
      }

      i = this.getGuiLeft() + 160;
      x = this.getGuiTop() + 142;
      boolean check1 = this.selectedRaceEvolutionProgress.equals("100%");
      boolean check2 = TensuraGUIHelper.mouseOver(pMouseX, pMouseY, i - 1, i + 43, x - 1, x + 13);
      hovering = this.selectedRace == null;
      boolean check4 = ((Race)Objects.requireNonNull(TensuraPlayerCapability.getRace(this.player))).getNextEvolutions(this.player).isEmpty();
      int offset = check1 && check2 ? 26 : (check1 ? 13 : 0);
      Component component = Component.m_237115_("tensura.evolution_menu.evolve").m_130940_(ChatFormatting.GREEN);
      Component tooltip = hovering ? Component.m_237115_("tensura.main_menu.evolution_progress.select").m_130940_(ChatFormatting.AQUA) : (check4 ? Component.m_237115_("tensura.main_menu.evolution_progress.none").m_130940_(ChatFormatting.GRAY) : (!check1 ? Component.m_237115_("tensura.evolution_menu.requirements_fail").m_130940_(ChatFormatting.RED) : component));
      RenderSystem.m_157456_(0, EVOLVE_BUTTON);
      m_93133_(pPoseStack, i, x, 0.0F, (float)offset, 43, 13, 43, 39);
      if (check2) {
         this.m_96602_(pPoseStack, tooltip, pMouseX, pMouseY);
      }

      Component text = Component.m_237115_("tensura.evolution_menu.track");
      int color = Color.WHITE.getRGB();
      this.f_96547_.m_92763_(pPoseStack, text, (float)(this.getGuiLeft() + 105), (float)(this.getGuiTop() + 145), color);
      text = Component.m_237115_("tensura.evolution_menu.evolve");
      color = check1 ? Color.GREEN.getRGB() : Color.GRAY.getRGB();
      this.f_96547_.m_92889_(pPoseStack, text, (float)(this.getGuiLeft() + 166), (float)(this.getGuiTop() + 145), color);
   }

   private void renderInfo(PoseStack stack, int pX, int pY) {
      this.renderProgressBars(stack);
      this.renderRaceInfo(stack);
      TensuraEPCapability.getFrom(this.player).ifPresent((cap) -> {
         String name = TensuraEPCapability.getName(this.player);
         int nameColor = TensuraPlayerCapability.isTrueDemonLord(this.player) ? ChatFormatting.DARK_PURPLE.m_126665_() : (TensuraPlayerCapability.isTrueHero(this.player) ? ChatFormatting.GOLD.m_126665_() : Color.WHITE.getRGB());
         TensuraGUIHelper.renderScaledCenteredXText(this.f_96547_, stack, (Component)(name != null ? Component.m_237113_(name) : this.player.m_7755_()), this.getGuiLeft() + 6, this.getGuiTop() + 35, 72, 19, nameColor, false);
         FontRenderHelper.renderScaledTextInArea(stack, this.f_96547_, Component.m_237110_("tensura.main_menu.existence_points", new Object[]{TensuraGUIHelper.shortenNumberComponent(cap.getEP())}), (float)(this.getGuiLeft() + 13), (float)(this.getGuiTop() + 144), 70.0F, 11.0F, Color.ORANGE);
         if (pX > this.getGuiLeft() + 8 && pX < this.getGuiLeft() + 82 && pY > this.getGuiTop() + 140 && pY < this.getGuiTop() + 155) {
            this.m_96602_(stack, Component.m_237113_(this.roundDouble.format(cap.getEP())).m_130940_(ChatFormatting.GOLD), pX, pY);
         }

      });
   }

   protected void m_7027_(PoseStack pPoseStack, int pMouseX, int pMouseY) {
   }

   public void m_6305_(PoseStack pPoseStack, int pMouseX, int pMouseY, float pPartialTick) {
      super.m_6305_(pPoseStack, pMouseX, pMouseY, pPartialTick);
      this.m_7025_(pPoseStack, pMouseX, pMouseY);
   }

   protected void m_7025_(PoseStack pPoseStack, int pX, int pY) {
      if (TensuraGUIHelper.mouseOver(pX, pY, this.getGuiLeft() + 61, this.getGuiLeft() + 78, this.getGuiTop() + 51, this.getGuiTop() + 130) && this.trackedRace != null) {
         Component tooltip = this.evolutionProgress.equals("-1") ? Component.m_237115_("tensura.main_menu.evolution_progress.none").m_130940_(ChatFormatting.RED) : Component.m_237110_("tensura.main_menu.evolution_progress", new Object[]{this.evolutionProgress});
         this.m_96602_(pPoseStack, tooltip, pX, pY);
      }

      if (TensuraGUIHelper.mouseOver(pX, pY, this.getGuiLeft() + 210, this.getGuiLeft() + 222, this.getGuiTop() + 90, this.getGuiTop() + 154) && this.selectedRace != null) {
         this.m_96602_(pPoseStack, Component.m_237110_("tensura.main_menu.evolution_progress", new Object[]{this.selectedRaceEvolutionProgress}), pX, pY);
      }

      if (TensuraGUIHelper.mouseOver(pX, pY, this.getGuiLeft() + 93, this.getGuiLeft() + 111, this.getGuiTop() + 34, this.getGuiTop() + 44)) {
         this.m_96602_(pPoseStack, Component.m_237115_("tooltip.tensura.return"), pX, pY);
      }

      if (TensuraGUIHelper.mouseOver(pX, pY, this.getGuiLeft() + 233, this.getGuiLeft() + 256, this.getGuiTop() + 30, this.getGuiTop() + 54)) {
         this.m_96602_(pPoseStack, Component.m_237115_("tensura.main_menu"), pX, pY);
      }

      super.m_7025_(pPoseStack, pX, pY);
   }

   public boolean m_7933_(int pKeyCode, int pScanCode, int pModifiers) {
      if (this.searchField.m_7933_(pKeyCode, pScanCode, pModifiers)) {
         return true;
      } else if (this.searchField.m_93696_() && this.searchField.m_94213_() && pKeyCode != 256) {
         return true;
      } else {
         if (this.f_96541_ != null) {
            if (TensuraKeybinds.MAIN_GUI.m_90832_(pKeyCode, pScanCode)) {
               this.f_96541_.m_91152_((Screen)null);
               this.f_96541_.f_91067_.m_91601_();
               return true;
            }

            if (this.f_96541_.f_91066_.f_92092_.isActiveAndMatches(InputConstants.m_84827_(pKeyCode, pScanCode))) {
               return true;
            }
         }

         return super.m_7933_(pKeyCode, pScanCode, pModifiers);
      }
   }

   public boolean m_6375_(double pMouseX, double pMouseY, int pButton) {
      this.scrolling = false;
      List<Race> evolutionList = (List)this.evolutions.getValue();
      int lastDisplayedIndex = Math.min(this.startIndex + 3, evolutionList.size());

      for(int i = this.startIndex; i < lastDisplayedIndex; ++i) {
         int x = this.getGuiLeft() + 94;
         int y = this.getGuiTop() + 48 + (i - this.startIndex) * 13;
         if (evolutionList.size() <= i) {
            break;
         }

         if (TensuraGUIHelper.mouseOver(pMouseX, pMouseY, x - 1, this.getGuiLeft() + 207, y - 1, y + 13)) {
            TensuraGUIHelper.playSound(SoundEvents.f_12490_, 1.0F);
            this.updateRaceVariables((Race)evolutionList.get(i), false);
            return true;
         }
      }

      if (TensuraGUIHelper.mouseOver(pMouseX, pMouseY, this.getGuiLeft() + 160, this.getGuiLeft() + 203, this.getGuiTop() + 142, this.getGuiTop() + 155) && this.selectedRace != null && this.selectedRaceEvolutionProgress.equals("100%")) {
         this.handleEvolution();
         return true;
      } else if (TensuraGUIHelper.mouseOver(pMouseX, pMouseY, this.getGuiLeft() + 93, this.getGuiLeft() + 111, this.getGuiTop() + 34, this.getGuiTop() + 44)) {
         return TensuraGUIHelper.buttonClick(this.player, this, SoundEvents.f_12490_, -1);
      } else {
         if (TensuraGUIHelper.mouseOver(pMouseX, pMouseY, this.getGuiLeft() + 210, this.getGuiLeft() + 220, this.getGuiTop() + 47, this.getGuiTop() + 88)) {
            this.scrolling = true;
         }

         return super.m_6375_(pMouseX, pMouseY, pButton);
      }
   }

   public boolean m_6050_(double pMouseX, double pMouseY, double pDelta) {
      if (this.isScrollBarActive()) {
         int i = this.getOffscreenRows();
         float f = (float)pDelta / (float)i;
         this.scrollOffs = Mth.m_14036_(this.scrollOffs - f, 0.0F, 1.0F);
         this.startIndex = Math.max(0, Math.min(this.startIndex - (int)pDelta, ((List)this.evolutions.getValue()).size() - 3));
      }

      return true;
   }

   public boolean m_7979_(double pMouseX, double pMouseY, int pButton, double pDragX, double pDragY) {
      if (this.scrolling && this.isScrollBarActive()) {
         int i = this.getGuiTop() + 49;
         int j = i + 38;
         this.scrollOffs = (float)((pMouseY - (double)i - 6.5D) / (double)((float)(j - i) - 13.0F));
         this.scrollOffs = Mth.m_14036_(this.scrollOffs, 0.0F, 1.0F);
         this.startIndex = (int)((double)(this.scrollOffs * (float)this.getOffscreenRows()) + 0.5D);
         return true;
      } else {
         return super.m_7979_(pMouseX, pMouseY, pButton, pDragX, pDragY);
      }
   }

   private boolean isScrollBarActive() {
      return ((List)this.evolutions.getValue()).size() > 3;
   }

   private int getOffscreenRows() {
      return ((List)this.evolutions.getValue()).size() - 3;
   }

   private void handleEvolution() {
      Race race = this.selectedRace;
      if (race == null) {
         race = this.trackedRace;
      }

      if (race != null) {
         if ((race != this.selectedRace || this.selectedRaceEvolutionProgress.equals("100%")) && (race != this.trackedRace || this.evolutionProgress.equals("100%"))) {
            int index = ((List)this.evolutions.getValue()).indexOf(race);
            if (TensuraGUIHelper.buttonClick(this.player, this, SoundEvents.f_12275_, index)) {
               this.updateRaceVariables((Race)null, true);
               this.updateRaceVariables((Race)null, false);
               this.searchField.m_94144_("");
            }

         }
      }
   }

   private void updateRaceVariables(@Nullable Race race, boolean tracked) {
      int evolution = race == null ? -2 : (race.getNextEvolutions(this.player).isEmpty() ? -1 : (int)race.getEvolutionPercentage(this.player));
      if (tracked) {
         this.trackedRace = race;
      } else {
         this.selectedRace = race;
      }

      this.updateEvolutionProgress(evolution, tracked);
   }

   private void updateEvolutionProgress(int percentage, boolean tracked) {
      if (tracked) {
         this.evolutionProgress = percentage >= 100 ? "100%" : percentage + "%";
      } else {
         this.selectedRaceEvolutionProgress = percentage >= 100 ? "100%" : percentage + "%";
      }

   }

   private void renderProgressBars(PoseStack stack) {
      RenderSystem.m_157456_(0, BACKGROUND);
      int evolutionPercentage;
      int fill;
      int length;
      int barYOffset;
      int pX;
      int pY;
      if (this.trackedRace == null) {
         this.updateEvolutionProgress(-2, true);
      } else {
         evolutionPercentage = Mth.m_14045_((int)this.trackedRace.getEvolutionPercentage(this.player), 0, 100);
         fill = (int)((float)(73 * evolutionPercentage) / 100.0F);
         length = 73 - fill;
         barYOffset = 164 + length;
         pX = this.getGuiLeft() + 65;
         pY = this.getGuiTop() + 54 + length;
         this.m_93228_(stack, pX, pY, 1, barYOffset, 11, fill);
         this.updateEvolutionProgress(evolutionPercentage, true);
      }

      if (this.selectedRace == null) {
         this.updateEvolutionProgress(-2, false);
      } else {
         evolutionPercentage = Mth.m_14045_((int)this.selectedRace.getEvolutionPercentage(this.player), 0, 100);
         fill = (int)((float)(58 * evolutionPercentage) / 100.0F);
         length = 58 - fill;
         barYOffset = 164 + length;
         pX = this.getGuiLeft() + 213;
         pY = this.getGuiTop() + 93 + length;
         this.m_93228_(stack, pX, pY, 13, barYOffset, 6, fill);
         this.updateEvolutionProgress(evolutionPercentage, false);
      }

   }

   private void renderRaceInfo(PoseStack stack) {
      if (this.selectedRace != null) {
         List<Component> requirements = this.selectedRace.getRequirementsForRendering(this.player);
         TensuraGUIHelper.renderScaledCenteredXText(this.f_96547_, stack, Component.m_237115_("tensura.evolution_menu.requirements"), this.getGuiLeft() + 95, this.getGuiTop() + 94, 111, 10, Color.WHITE, false);
         int i = 0;
         int j = 31 / requirements.size();

         for(Iterator var5 = requirements.iterator(); var5.hasNext(); i += j) {
            Component text = (Component)var5.next();
            TensuraGUIHelper.renderScaledShadowText(stack, this.f_96547_, text, (float)(this.getGuiLeft() + 99), (float)(this.getGuiTop() + 107 + i), 105.0F, 9.0F, Color.WHITE.getRGB(), 2.0F, 0.01F);
         }

      }
   }
}
