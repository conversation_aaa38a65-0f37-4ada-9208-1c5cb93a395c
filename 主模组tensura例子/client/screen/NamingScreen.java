package com.github.manasmods.tensura.client.screen;

import com.github.manasmods.tensura.client.TensuraGUIHelper;
import com.github.manasmods.tensura.client.keybind.TensuraKeybinds;
import com.github.manasmods.tensura.config.TensuraConfig;
import com.github.manasmods.tensura.menu.NamingMenu;
import com.github.manasmods.tensura.network.TensuraNetwork;
import com.github.manasmods.tensura.network.play2server.RequestNamingGUIPacket;
import com.mojang.blaze3d.platform.InputConstants;
import com.mojang.blaze3d.systems.RenderSystem;
import com.mojang.blaze3d.vertex.PoseStack;
import java.awt.Color;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import net.minecraft.client.gui.components.EditBox;
import net.minecraft.client.gui.screens.Screen;
import net.minecraft.client.gui.screens.inventory.AbstractContainerScreen;
import net.minecraft.client.renderer.GameRenderer;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.world.entity.player.Inventory;
import net.minecraft.world.level.Level;

public class NamingScreen extends AbstractContainerScreen<NamingMenu> {
   private static final ResourceLocation BACKGROUND = new ResourceLocation("tensura", "textures/gui/naming/naming_gui.png");
   private final Level level;
   private final List<String> names = new ArrayList();
   private EditBox editBox;
   private RequestNamingGUIPacket.NamingType type;

   public NamingScreen(NamingMenu pMenu, Inventory pPlayerInventory, Component pTitle) {
      super(pMenu, pPlayerInventory, pTitle);
      this.f_97726_ = 144;
      this.f_97727_ = 96;
      this.level = pPlayerInventory.f_35978_.m_9236_();
      this.names.addAll((Collection)TensuraConfig.INSTANCE.namingConfig.names.get());
   }

   protected void m_7856_() {
      super.m_7856_();
      this.editBox = new EditBox(this.f_96547_, this.getGuiLeft() + 19, this.getGuiTop() + 27, 85, 11, Component.m_237119_());
      this.editBox.m_94182_(false);
      this.editBox.m_94144_(((NamingMenu)this.f_97732_).getEntity().m_7755_().getString());
      this.m_142416_(this.editBox);
   }

   protected void m_7286_(PoseStack poseStack, float particleTick, int pX, int pY) {
      this.m_7333_(poseStack);
      RenderSystem.m_157427_(GameRenderer::m_172817_);
      RenderSystem.m_157429_(1.0F, 1.0F, 1.0F, 1.0F);
      RenderSystem.m_157456_(0, BACKGROUND);
      int centerX = (this.f_96543_ - this.f_97726_) / 2;
      int centerY = (this.f_96544_ - this.f_97727_) / 2;
      this.m_93228_(poseStack, centerX, centerY, 0, 0, this.f_97726_, this.f_97727_);
      int x = this.getGuiLeft() + 37;
      int y = this.getGuiTop() + 74;
      if (TensuraGUIHelper.mouseOver(pX, pY, x - 1, this.getGuiLeft() + 107, y - 1, this.getGuiTop() + 89)) {
         m_93133_(poseStack, x, y, 1.0F, 176.0F, 70, 15, 256, 256);
      }

      TensuraGUIHelper.renderScaledCenteredXText(this.f_96547_, poseStack, Component.m_237115_("tensura.naming.name"), x, y + 4, 70, 11, Color.WHITE, false);
      x = this.getGuiLeft() + 114;
      y = this.getGuiTop() + 23;
      if (TensuraGUIHelper.mouseOver(pX, pY, x - 1, this.getGuiLeft() + 128, y - 1, this.getGuiTop() + 39)) {
         RenderSystem.m_157456_(0, BACKGROUND);
         m_93133_(poseStack, x, y, 79.0F, 97.0F, 14, 16, 256, 256);
      }

      y = this.getGuiTop() + 47;

      for(int i = 0; i < 3; ++i) {
         x = this.getGuiLeft() + 31 + i * 30;
         int hOffset = i == 0 ? 1 : (i == 1 ? 22 : 43);
         RequestNamingGUIPacket.NamingType namingType = i == 0 ? RequestNamingGUIPacket.NamingType.LOW : (i == 1 ? RequestNamingGUIPacket.NamingType.MEDIUM : RequestNamingGUIPacket.NamingType.HIGH);
         int yOffset = 0;
         if (namingType == this.type) {
            yOffset = 139;
         } else if (TensuraGUIHelper.mouseOver(pX, pY, x, x + 21, y, y + 21)) {
            yOffset = 118;
         }

         if (yOffset != 0) {
            RenderSystem.m_157456_(0, BACKGROUND);
            m_93133_(poseStack, x + 1, y + 1, (float)hOffset, (float)yOffset, 20, 20, 256, 256);
         }
      }

      this.m_7025_(poseStack, pX, pY);
   }

   protected void m_7025_(PoseStack pPoseStack, int pX, int pY) {
      int y = this.getGuiTop() + 47;

      for(int i = 0; i < 3; ++i) {
         int x = this.getGuiLeft() + 31 + i * 30;
         MutableComponent var10000;
         switch(i) {
         case 0:
            var10000 = Component.m_237115_("tensura.naming.subdue");
            break;
         case 1:
            var10000 = Component.m_237115_("tensura.naming.evolve");
            break;
         default:
            var10000 = Component.m_237115_("tensura.naming.endow");
         }

         Component tooltip = var10000;
         if (TensuraGUIHelper.mouseOver(pX, pY, x, x + 21, y, y + 21)) {
            this.m_96602_(pPoseStack, tooltip, pX, pY);
         }
      }

      if (TensuraGUIHelper.mouseOver(pX, pY, this.getGuiLeft() + 114, this.getGuiLeft() + 128, this.getGuiTop() + 23, this.getGuiTop() + 39)) {
         this.m_96602_(pPoseStack, Component.m_237115_("tensura.naming.randomize"), pX, pY);
      }

      super.m_7025_(pPoseStack, pX, pY);
   }

   protected void m_7027_(PoseStack pPoseStack, int pMouseX, int pMouseY) {
   }

   public boolean m_6375_(double pMouseX, double pMouseY, int pButton) {
      if (this.handleTypeButtonsClick(pMouseX, pMouseY) != 0) {
         return true;
      } else {
         if (TensuraGUIHelper.mouseOver(pMouseX, pMouseY, this.getGuiLeft() + 114, this.getGuiLeft() + 128, this.getGuiTop() + 23, this.getGuiTop() + 39)) {
            TensuraGUIHelper.playSound(SoundEvents.f_12490_, 1.0F);
            this.editBox.m_94144_((String)this.names.get(this.level.f_46441_.m_216339_(0, this.names.size())));
            this.editBox.m_94196_(0);
            this.editBox.m_94208_(0);
            this.editBox.m_94178_(false);
         }

         if (TensuraGUIHelper.mouseOver(pMouseX, pMouseY, this.getGuiLeft() + 37, this.getGuiLeft() + 107, this.getGuiTop() + 74, this.getGuiTop() + 89)) {
            TensuraGUIHelper.playSound(SoundEvents.f_12490_, 1.0F);
            if (this.editBox.m_94155_().isBlank() || this.editBox.m_94155_().isEmpty()) {
               return true;
            }

            if (this.type == null) {
               return true;
            }

            int id = ((NamingMenu)this.f_97732_).getEntity().m_19879_();
            TensuraNetwork.INSTANCE.sendToServer(new RequestNamingGUIPacket(id, this.editBox.m_94155_(), this.type));
            this.m_7379_();
         }

         return super.m_6375_(pMouseX, pMouseY, pButton);
      }
   }

   private int handleTypeButtonsClick(double pX, double pY) {
      int y = this.getGuiTop() + 47;

      for(int i = 1; i < 4; ++i) {
         int x = this.getGuiLeft() + 31 + (i - 1) * 30;
         if (TensuraGUIHelper.mouseOver(pX, pY, x, x + 21, y, y + 21)) {
            TensuraGUIHelper.playSound(SoundEvents.f_12490_, 1.0F);
            this.type = i == 1 ? RequestNamingGUIPacket.NamingType.LOW : (i == 2 ? RequestNamingGUIPacket.NamingType.MEDIUM : RequestNamingGUIPacket.NamingType.HIGH);
            return i;
         }
      }

      return 0;
   }

   public boolean m_7933_(int pKeyCode, int pScanCode, int pModifiers) {
      if (this.editBox.m_7933_(pKeyCode, pScanCode, pModifiers)) {
         return true;
      } else if (this.editBox.m_93696_() && this.editBox.m_94213_() && pKeyCode != 256) {
         return true;
      } else {
         if (this.f_96541_ != null) {
            if (TensuraKeybinds.NAME.m_90832_(pKeyCode, pScanCode)) {
               this.f_96541_.m_91152_((Screen)null);
               this.f_96541_.f_91067_.m_91601_();
               return true;
            }

            if (this.f_96541_.f_91066_.f_92092_.isActiveAndMatches(InputConstants.m_84827_(pKeyCode, pScanCode))) {
               return true;
            }
         }

         return super.m_7933_(pKeyCode, pScanCode, pModifiers);
      }
   }
}
