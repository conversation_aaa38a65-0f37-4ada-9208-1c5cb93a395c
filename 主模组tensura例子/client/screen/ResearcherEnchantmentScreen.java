package com.github.manasmods.tensura.client.screen;

import com.github.manasmods.manascore.api.client.gui.widget.ImagePredicateButton;
import com.github.manasmods.tensura.ability.skill.unique.ResearcherSkill;
import com.github.manasmods.tensura.client.TensuraGUIHelper;
import com.github.manasmods.tensura.enchantment.EngravingEnchantment;
import com.github.manasmods.tensura.menu.ResearcherEnchantmentMenu;
import com.github.manasmods.tensura.network.TensuraNetwork;
import com.github.manasmods.tensura.network.play2server.RequestMenuSwitchPacket;
import com.github.manasmods.tensura.network.play2server.skill.RequestResearcherEnchantmentPacket;
import com.github.manasmods.tensura.util.ITensuraScrollbar;
import com.mojang.blaze3d.systems.RenderSystem;
import com.mojang.blaze3d.vertex.PoseStack;
import java.awt.Color;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import net.minecraft.ChatFormatting;
import net.minecraft.client.gui.components.Button.OnPress;
import net.minecraft.client.gui.components.Button.OnTooltip;
import net.minecraft.client.gui.screens.inventory.AbstractContainerScreen;
import net.minecraft.client.renderer.GameRenderer;
import net.minecraft.network.chat.Component;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.world.entity.player.Inventory;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;
import net.minecraft.world.item.enchantment.Enchantment;

public class ResearcherEnchantmentScreen extends AbstractContainerScreen<ResearcherEnchantmentMenu> implements ITensuraScrollbar {
   private final ResourceLocation BACKGROUND = new ResourceLocation("tensura", "textures/gui/researcher/researcher_enchant.png");
   private final ResourceLocation NAME_BAR = new ResourceLocation("tensura", "textures/gui/researcher/enchantment_name.png");
   private final ResourceLocation ENCHANT_BUTTON = new ResourceLocation("tensura", "textures/gui/researcher/enchant_button.png");
   private final ResourceLocation LEVEL_BOX = new ResourceLocation("tensura", "textures/gui/researcher/enchantment_level.png");
   private final ResourceLocation CHECKBOX = new ResourceLocation("tensura", "textures/gui/checkbox.png");
   private float scrollOffs;
   private int startIndex;
   private int offscreenRows;

   public ResearcherEnchantmentScreen(ResearcherEnchantmentMenu pMenu, Inventory pPlayerInventory, Component pTitle) {
      super(pMenu, pPlayerInventory, pTitle);
      this.f_97726_ = 176;
      this.f_97727_ = 227;
   }

   protected void m_7856_() {
      super.m_7856_();
      this.scrollOffs = 0.0F;
      this.startIndex = 0;
      this.offscreenRows = ResearcherSkill.getAllEnchantments(((ResearcherEnchantmentMenu)this.f_97732_).getPlayer(), ((ResearcherEnchantmentMenu)this.f_97732_).getSkill()).size() - 5;
      this.initScrollbar(this.f_97735_ + 126, this.f_97736_ + 67, 65, this.scrollOffs, false, false);
      int var10002 = this.f_97735_ + 145;
      int var10003 = this.f_97736_ + 44;
      ResourceLocation var10006 = this.ENCHANT_BUTTON;
      OnPress var10007 = (onPress) -> {
         if (!((ResearcherEnchantmentMenu)this.f_97732_).getItemInput().m_7983_() && (long)((ResearcherEnchantmentMenu)this.f_97732_).getExperienceCost() <= ResearcherEnchantmentMenu.getTotalXp(((ResearcherEnchantmentMenu)this.f_97732_).getPlayer())) {
            TensuraNetwork.INSTANCE.sendToServer(new RequestResearcherEnchantmentPacket());
            TensuraGUIHelper.playSound(SoundEvents.f_11887_, 1.0F);
         }
      };
      OnTooltip var10008 = (button, poseStack, x, y) -> {
         this.m_96602_(poseStack, Component.m_237115_("container.enchant"), x, y);
      };
      ResearcherEnchantmentMenu var10009 = (ResearcherEnchantmentMenu)this.f_97732_;
      Objects.requireNonNull(var10009);
      ImagePredicateButton enchantButton = new ImagePredicateButton(var10002, var10003, 20, 18, var10006, var10007, var10008, var10009::check);
      this.m_142416_(enchantButton);
   }

   public void m_6305_(PoseStack pPoseStack, int pMouseX, int pMouseY, float pPartialTick) {
      this.m_7333_(pPoseStack);
      super.m_6305_(pPoseStack, pMouseX, pMouseY, pPartialTick);
      this.m_7025_(pPoseStack, pMouseX, pMouseY);
      this.renderTabIcons();
   }

   private void renderTabIcons() {
      if (this.f_96541_ != null) {
         this.f_96541_.m_91291_().m_115203_(new ItemStack(Items.f_42108_), this.getGuiLeft() + 8, this.getGuiTop() + 6);
         this.f_96541_.m_91291_().m_115203_(new ItemStack(Items.f_42100_), this.getGuiLeft() + 34, this.getGuiTop() + 5);
      }
   }

   public void m_7027_(PoseStack pPoseStack, int pMouseX, int pMouseY) {
      TensuraGUIHelper.renderScaledCenteredXText(this.f_96547_, pPoseStack, Component.m_237115_("tensura.researcher_menu.enchantment_tab"), 2, 31, 145, 9, Color.WHITE, false);
   }

   protected void m_7286_(PoseStack poseStack, float partialTick, int mX, int mY) {
      RenderSystem.m_157427_(GameRenderer::m_172817_);
      RenderSystem.m_157429_(1.0F, 1.0F, 1.0F, 1.0F);
      RenderSystem.m_157456_(0, this.BACKGROUND);
      int x = (this.f_96543_ - this.f_97726_) / 2;
      int y = (this.f_96544_ - this.f_97727_) / 2;
      this.m_93228_(poseStack, x, y, 0, 0, this.f_97726_, this.f_97727_);
      this.renderScrollbar(poseStack);
      SCROLLBAR_DATA.setActive(this.offscreenRows >= 1);
      this.renderEnchantments(poseStack, mX, mY);
   }

   protected void m_7025_(PoseStack pPoseStack, int pX, int pY) {
      if (TensuraGUIHelper.mouseOver(pX, pY, this.f_97735_ + 3, this.f_97735_ + 28, this.f_97736_ + 1, this.f_97736_ + 23)) {
         this.m_96602_(pPoseStack, Component.m_237115_("tensura.researcher_menu.storage_tab"), pX, pY);
      } else if (TensuraGUIHelper.mouseOver(pX, pY, this.f_97735_ + 29, this.f_97735_ + 54, this.f_97736_ - 1, this.f_97736_ + 23)) {
         this.m_96602_(pPoseStack, Component.m_237115_("tensura.researcher_menu.enchantment_tab"), pX, pY);
      } else {
         int slotClicked;
         if (TensuraGUIHelper.mouseOver(pX, pY, this.f_97735_ + 149, this.f_97735_ + 160, this.f_97736_ + 91, this.f_97736_ + 106)) {
            slotClicked = ((ResearcherEnchantmentMenu)this.f_97732_).getExperienceCost();
            ChatFormatting formatting = ResearcherEnchantmentMenu.getTotalXp(((ResearcherEnchantmentMenu)this.f_97732_).getPlayer()) >= (long)slotClicked ? ChatFormatting.GREEN : ChatFormatting.RED;
            this.m_96602_(pPoseStack, Component.m_237110_("tensura.researcher_menu.enchantment_tab.xp_cost", new Object[]{slotClicked}).m_130940_(formatting), pX, pY);
         } else {
            slotClicked = this.getSlotUnderMouse((double)pX, (double)pY);
            if (slotClicked >= 0 && pX <= this.f_97735_ + 106) {
               int id = slotClicked + this.startIndex;
               Map<Enchantment, Integer> map = ResearcherSkill.getAllEnchantments(((ResearcherEnchantmentMenu)this.f_97732_).getPlayer(), ((ResearcherEnchantmentMenu)this.f_97732_).getSkill());
               if (!map.isEmpty() && id < map.entrySet().size()) {
                  Enchantment enchantment = (Enchantment)map.keySet().stream().toList().get(id);
                  this.m_96602_(pPoseStack, Component.m_237115_(enchantment.m_44704_()).m_130940_(this.getColor(enchantment)), pX, pY);
                  return;
               }
            }

            super.m_7025_(pPoseStack, pX, pY);
         }
      }
   }

   private ChatFormatting getColor(Enchantment enchantment) {
      ChatFormatting color;
      if (enchantment.m_6589_()) {
         color = ChatFormatting.RED;
      } else if (enchantment instanceof EngravingEnchantment) {
         color = ChatFormatting.GOLD;
      } else {
         ChatFormatting var10000;
         switch(enchantment.m_44699_()) {
         case COMMON:
            var10000 = ChatFormatting.YELLOW;
            break;
         case UNCOMMON:
            var10000 = ChatFormatting.GREEN;
            break;
         case RARE:
            var10000 = ChatFormatting.AQUA;
            break;
         case VERY_RARE:
            var10000 = ChatFormatting.LIGHT_PURPLE;
            break;
         default:
            throw new IncompatibleClassChangeError();
         }

         color = var10000;
      }

      return color;
   }

   private void renderEnchantments(PoseStack poseStack, int mX, int mY) {
      List<Enchantment> enchantments = new ArrayList();
      List<Integer> levels = new ArrayList();
      this.getAllEnchantments().forEach((key, value) -> {
         enchantments.add(key);
         levels.add(value);
      });
      int pos = 0;
      int endIndex = this.startIndex + 5;

      for(int i = this.startIndex; i < endIndex && i < enchantments.size(); ++i) {
         Enchantment enchantment = (Enchantment)enchantments.get(i);
         int level = (Integer)levels.get(i);
         Component name = Component.m_237115_(enchantment.m_44704_());
         ChatFormatting color = this.getColor(enchantment);
         Map<Enchantment, Integer> map = ResearcherSkill.getSelectedEnchantments(((ResearcherEnchantmentMenu)this.f_97732_).getPlayer(), ((ResearcherEnchantmentMenu)this.f_97732_).getSkill());
         boolean flag = TensuraGUIHelper.mouseOver(mX, mY, this.f_97735_ + 21, this.f_97735_ + 107, this.f_97736_ + 66 + pos * 13, this.f_97736_ + 80 + pos * 13);
         RenderSystem.m_157456_(0, this.NAME_BAR);
         m_93133_(poseStack, this.f_97735_ + 22, this.f_97736_ + 67 + pos * 13, 0.0F, flag ? 13.0F : 0.0F, 86, 13, 85, 26);
         m_93243_(poseStack, this.f_96547_, TensuraGUIHelper.shortenTextComponent((Component)name.m_6881_().m_130940_(color), 14), this.f_97735_ + 27, this.f_97736_ + 70 + pos * 13, color.m_126665_() == null ? 0 : color.m_126665_());
         int offsetX = 0;
         boolean hovering = TensuraGUIHelper.mouseOver(mX, mY, this.f_97735_ + 8, this.f_97735_ + 22, this.f_97736_ + 67 + pos * 13, this.f_97736_ + 81 + pos * 13);
         if (hovering) {
            offsetX = 13;
         }

         int offsetY = map.containsKey(enchantment) ? 13 : 0;
         RenderSystem.m_157456_(0, this.CHECKBOX);
         m_93133_(poseStack, this.f_97735_ + 8, this.f_97736_ + 67 + pos * 13, (float)offsetX, (float)offsetY, 13, 13, 26, 26);
         hovering = TensuraGUIHelper.mouseOver(mX, mY, this.f_97735_ + 107, this.f_97735_ + 123, this.f_97736_ + 67 + pos * 13, this.f_97736_ + 81 + pos * 13);
         offsetY = hovering ? 13 : 0;
         RenderSystem.m_157456_(0, this.LEVEL_BOX);
         m_93133_(poseStack, this.f_97735_ + 107, this.f_97736_ + 67 + pos * 13, 0.0F, (float)offsetY, 15, 13, 15, 26);
         int selectedLevel = (Integer)map.getOrDefault(enchantment, level);
         Component levelComponent = selectedLevel <= 10 ? Component.m_237115_("enchantment.level." + selectedLevel) : Component.m_237113_(String.valueOf(selectedLevel));
         TensuraGUIHelper.renderCenteredXText(this.f_96547_, poseStack, levelComponent, this.f_97735_ + 108, this.f_97736_ + 70 + pos * 13, 15, Color.LIGHT_GRAY, false);
         ++pos;
      }

   }

   private int getSlotUnderMouse(double pX, double pY) {
      for(int i = 0; i < 5; ++i) {
         if (TensuraGUIHelper.mouseOver(pX, pY, this.f_97735_ + 8, this.f_97735_ + 123, this.f_97736_ + 66 + i * 13, this.f_97736_ + 80 + i * 13)) {
            return i;
         }
      }

      if (TensuraGUIHelper.mouseOver(pX, pY, this.f_97735_ + 145, this.f_97735_ + 163, this.f_97736_ + 44, this.f_97736_ + 62)) {
         return -2;
      } else {
         return -1;
      }
   }

   private Map<Enchantment, Integer> getAllEnchantments() {
      Map<Enchantment, Integer> enchantments = ResearcherSkill.getAllEnchantments(((ResearcherEnchantmentMenu)this.f_97732_).getPlayer(), ((ResearcherEnchantmentMenu)this.f_97732_).getSkill());
      this.offscreenRows = enchantments.size() - 5;
      if (this.startIndex > enchantments.size()) {
         this.startIndex = 0;
      }

      return enchantments;
   }

   public boolean m_6375_(double pX, double pY, int pButton) {
      this.handleScrollbarMouseClicked(pX, pY);
      int slotClicked = this.getSlotUnderMouse(pX, pY);
      if (!((ResearcherEnchantmentMenu)this.f_97732_).getItemInput().m_7983_() && slotClicked >= 0) {
         int id = slotClicked + this.startIndex;
         Map<Enchantment, Integer> mapAll = ResearcherSkill.getAllEnchantments(((ResearcherEnchantmentMenu)this.f_97732_).getPlayer(), ((ResearcherEnchantmentMenu)this.f_97732_).getSkill());
         if (!mapAll.isEmpty() && id < mapAll.entrySet().size()) {
            Enchantment enchantment = (Enchantment)mapAll.keySet().stream().toList().get(id);
            if (pX > (double)(this.f_97735_ + 106)) {
               Map<Enchantment, Integer> map = ResearcherSkill.getSelectedEnchantments(((ResearcherEnchantmentMenu)this.f_97732_).getPlayer(), ((ResearcherEnchantmentMenu)this.f_97732_).getSkill());
               if (enchantment != null && map.containsKey(enchantment)) {
                  int level = (Integer)map.get(enchantment) + 1;
                  int maxEnchantLevel = Math.min(((ResearcherEnchantmentMenu)this.f_97732_).getMaxStoredLevel(enchantment), ResearcherEnchantmentMenu.getMaxEnchantLevel(enchantment, ((ResearcherEnchantmentMenu)this.f_97732_).getPlayer()));
                  if (level > maxEnchantLevel) {
                     level = maxEnchantLevel;
                  }

                  TensuraNetwork.INSTANCE.sendToServer(new RequestResearcherEnchantmentPacket(id, level));
                  TensuraGUIHelper.playSound(SoundEvents.f_12088_, 1.0F);
               }

               return true;
            }
         }

         TensuraNetwork.INSTANCE.sendToServer(new RequestResearcherEnchantmentPacket(id));
         TensuraGUIHelper.playSound(SoundEvents.f_12013_, 1.0F);
         return true;
      } else if (TensuraGUIHelper.mouseOver(pX, pY, this.f_97735_ + 4, this.f_97735_ + 28, this.f_97736_ + 2, this.f_97736_ + 23)) {
         TensuraNetwork.INSTANCE.sendToServer(new RequestMenuSwitchPacket(RequestMenuSwitchPacket.SwitchType.RESEARCHER_TAB_2_TO_TAB_1));
         return true;
      } else {
         return super.m_6375_(pX, pY, pButton);
      }
   }

   public boolean m_6050_(double pMouseX, double pMouseY, double pDelta) {
      this.handleScrollbarMouseScrolled(pDelta, 1);
      return true;
   }

   public boolean m_7979_(double pMouseX, double pMouseY, int pButton, double pDragX, double pDragY) {
      return this.handleScrollbarMouseDragged(pMouseY, 1) ? true : super.m_7979_(pMouseX, pMouseY, pButton, pDragX, pDragY);
   }

   public float getScrollOffs() {
      return this.scrollOffs;
   }

   public void setScrollOffs(float scrollOffs) {
      this.scrollOffs = scrollOffs;
   }

   public int getStartIndex() {
      return this.startIndex;
   }

   public int getOffscreenRows() {
      return this.offscreenRows;
   }

   public void setStartIndex(int startIndex) {
      this.startIndex = startIndex;
   }

   public void setOffscreenRows(int offscreenRows) {
      this.offscreenRows = offscreenRows;
   }
}
