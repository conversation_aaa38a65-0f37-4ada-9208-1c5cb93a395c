package com.github.manasmods.tensura.client.screen;

import com.github.manasmods.manascore.api.client.gui.FontRenderHelper;
import com.github.manasmods.manascore.api.client.gui.widget.ImagePredicateButton;
import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.client.TensuraGUIHelper;
import com.github.manasmods.tensura.menu.SkillCreatorMenu;
import com.github.manasmods.tensura.network.TensuraNetwork;
import com.github.manasmods.tensura.network.play2server.skill.RequestSkillCreationPacket;
import com.github.manasmods.tensura.util.Cached;
import com.mojang.blaze3d.platform.InputConstants;
import com.mojang.blaze3d.systems.RenderSystem;
import com.mojang.blaze3d.vertex.PoseStack;
import java.awt.Color;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import javax.annotation.Nullable;
import net.minecraft.ChatFormatting;
import net.minecraft.client.Minecraft;
import net.minecraft.client.gui.components.EditBox;
import net.minecraft.client.gui.components.Button.OnPress;
import net.minecraft.client.gui.components.Button.OnTooltip;
import net.minecraft.client.gui.screens.inventory.AbstractContainerScreen;
import net.minecraft.client.renderer.GameRenderer;
import net.minecraft.client.resources.sounds.SimpleSoundInstance;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.util.Mth;
import net.minecraft.world.entity.player.Inventory;
import net.minecraft.world.entity.player.Player;

public class SkillCreatorScreen extends AbstractContainerScreen<SkillCreatorMenu> {
   private static final ResourceLocation BACKGROUND = new ResourceLocation("tensura", "textures/gui/skill_creator/skill_creator.png");
   private static final ResourceLocation SKILL_BAR = new ResourceLocation("tensura", "textures/gui/skill_button.png");
   private static final ResourceLocation SCROLL_BAR = new ResourceLocation("tensura", "textures/gui/scroll_bar.png");
   private static final ResourceLocation GAIN_BUTTON = new ResourceLocation("tensura", "textures/gui/skill_creator/gain_button.png");
   private Cached<List<ManasSkill>, String> filteredSkills;
   private ManasSkill selectedSkill = null;
   private boolean scrolling;
   private float scrollOffs;
   private int startIndex;
   private EditBox searchField;
   private final List<ManasSkill> skills;
   private final Player player;

   public SkillCreatorScreen(SkillCreatorMenu pMenu, Inventory pPlayerInventory, Component pTitle) {
      super(pMenu, pPlayerInventory, pTitle.m_6881_().m_130940_(ChatFormatting.WHITE));
      this.f_97726_ = 233;
      this.f_97727_ = 140;
      this.player = pPlayerInventory.f_35978_;
      this.skills = ((SkillCreatorMenu)this.f_97732_).getSkills().stream().map((skill) -> {
         return (ManasSkill)SkillAPI.getSkillRegistry().getValue(skill);
      }).toList();
   }

   protected void m_7856_() {
      super.m_7856_();
      this.scrollOffs = 0.0F;
      this.startIndex = 0;
      this.searchField = new EditBox(this.f_96547_, this.getGuiLeft() + 19, this.getGuiTop() + 27, 79, 9, Component.m_237119_());
      this.searchField.m_94182_(false);
      this.searchField.m_94151_((s) -> {
         if (!s.isEmpty()) {
            this.scrollOffs = 0.0F;
            this.startIndex = 0;
         }
      });
      this.m_142416_(this.searchField);
      this.filteredSkills = new Cached(() -> {
         List<ManasSkill> filteredSkillList = new ArrayList(List.copyOf(this.skills));
         if (!this.searchField.m_94155_().isEmpty() && !this.searchField.m_94155_().isBlank()) {
            String filterValue = this.searchField.m_94155_().toLowerCase();
            filteredSkillList.removeIf((skill) -> {
               if (skill.getName() == null) {
                  return true;
               } else {
                  return !skill.getName().getString().toLowerCase().contains(filterValue);
               }
            });
         }

         return filteredSkillList;
      }, (info) -> {
         if (info.lastCallbackReference == null || !((String)info.lastCallbackReference).equals(this.searchField.m_94155_())) {
            info.lastCallbackReference = this.searchField.m_94155_();
            info.needsUpdate = true;
         }

         return info;
      });
      int var10002 = this.getGuiLeft() + 162;
      int var10003 = this.getGuiTop() + 116;
      ResourceLocation var10006 = GAIN_BUTTON;
      OnPress var10007 = (pButton) -> {
         TensuraNetwork.INSTANCE.sendToServer(new RequestSkillCreationPacket(SkillUtils.getSkillId(this.selectedSkill)));
      };
      OnTooltip var10008 = (button, poseStack, x, y) -> {
         this.m_96602_(poseStack, Component.m_237115_("tensura.skill_creator.create_skill"), x, y);
      };
      SkillCreatorMenu var10009 = (SkillCreatorMenu)this.f_97732_;
      Objects.requireNonNull(var10009);
      ImagePredicateButton gainSkillButton = new ImagePredicateButton(var10002, var10003, 20, 20, var10006, var10007, var10008, var10009::check);
      this.m_142416_(gainSkillButton);
   }

   protected void m_7286_(PoseStack poseStack, float pPartialTick, int pX, int pY) {
      this.m_7333_(poseStack);
      RenderSystem.m_157427_(GameRenderer::m_172817_);
      RenderSystem.m_157429_(1.0F, 1.0F, 1.0F, 1.0F);
      RenderSystem.m_157456_(0, BACKGROUND);
      int x = (this.f_96543_ - this.f_97726_) / 2;
      int y = (this.f_96544_ - this.f_97727_) / 2;
      this.m_93228_(poseStack, x, y, 0, 0, this.f_97726_, this.f_97727_);
      TensuraGUIHelper.renderScaledCenteredXText(this.f_96547_, poseStack, Component.m_237115_("tensura.skill_creator"), this.getGuiLeft() + 3, this.getGuiTop() + 7, 112, 11, Color.LIGHT_GRAY, false);
      int k = (int)(78.0F * this.scrollOffs);
      RenderSystem.m_157456_(0, SCROLL_BAR);
      m_93133_(poseStack, this.f_97735_ + 98, this.f_97736_ + 43 + k, 0.0F, this.isScrollBarActive() ? 13.0F : 0.0F, 10, 13, 10, 26);
      List<ManasSkill> filteredSkills = (List)this.filteredSkills.getValue();
      int lastVisibleElementIndex = Math.min(this.startIndex + 7, filteredSkills.size());
      this.renderButtons(poseStack, pX, pY, lastVisibleElementIndex, filteredSkills);
      if (this.selectedSkill != null) {
         ResourceLocation location = this.selectedSkill.getSkillIcon();
         if (location != null) {
            RenderSystem.m_157456_(0, location);
            m_93133_(poseStack, this.getGuiLeft() + 162, this.getGuiTop() + 9, 0.0F, 0.0F, 20, 20, 20, 20);
         }

         Component description = this.selectedSkill.getSkillDescription();
         FontRenderHelper.renderScaledTextInArea(poseStack, this.f_96547_, description, (float)(this.getGuiLeft() + 125), (float)(this.getGuiTop() + 46), 94.0F, 66.0F, Color.LIGHT_GRAY);
         boolean hovering = pX > this.getGuiLeft() + 158 && pX < this.getGuiLeft() + 185 && pY > this.getGuiTop() + 5 && pY < this.getGuiTop() + 32;
         if (hovering && this.selectedSkill.getName() != null) {
            this.m_96602_(poseStack, this.selectedSkill.getName(), pX, pY);
         }
      }

   }

   private void renderButtons(PoseStack pPoseStack, int pMouseX, int pMouseY, int pLastVisibleElementIndex, List<ManasSkill> list) {
      for(int i = this.startIndex; i < pLastVisibleElementIndex && i < list.size(); ++i) {
         int x = this.getGuiLeft() + 6;
         int y = this.getGuiTop() + 43 + (i - this.startIndex) * 13;
         int offset = 0;
         boolean hovering = pMouseX >= x && pMouseY >= y && pMouseX < x + 89 && pMouseY < y + 13;
         if (hovering) {
            offset = 13;
         }

         RenderSystem.m_157456_(0, SKILL_BAR);
         m_93133_(pPoseStack, x, y, 0.0F, (float)offset, 89, 13, 89, 26);
         ManasSkill manasSkill = (ManasSkill)list.get(i);
         MutableComponent name = this.skillName(manasSkill).m_130940_(Skill.SkillType.UNIQUE.getChatFormatting());
         TensuraGUIHelper.renderScaledShadowText(pPoseStack, this.f_96547_, TensuraGUIHelper.shortenTextComponent((Component)name, 14), (float)(this.getGuiLeft() + 11), (float)(this.getGuiTop() + 46 + (i - this.startIndex) * 13), 85.0F, 13.0F, Color.WHITE.getRGB(), 2.0F, 0.01F);
         if (hovering) {
            this.m_96602_(pPoseStack, name, pMouseX, pMouseY);
         }
      }

   }

   protected void m_7027_(PoseStack pPoseStack, int pMouseX, int pMouseY) {
   }

   public void m_6305_(PoseStack pPoseStack, int pMouseX, int pMouseY, float pPartialTick) {
      super.m_6305_(pPoseStack, pMouseX, pMouseY, pPartialTick);
      this.m_7025_(pPoseStack, pMouseX, pMouseY);
   }

   protected void m_7025_(PoseStack pPoseStack, int pX, int pY) {
      super.m_7025_(pPoseStack, pX, pY);
   }

   public boolean m_7933_(int pKeyCode, int pScanCode, int pModifiers) {
      if (this.searchField.m_7933_(pKeyCode, pScanCode, pModifiers)) {
         return true;
      } else if (this.searchField.m_93696_() && this.searchField.m_94213_() && pKeyCode != 256) {
         return true;
      } else {
         return this.f_96541_ != null && this.f_96541_.f_91066_.f_92092_.isActiveAndMatches(InputConstants.m_84827_(pKeyCode, pScanCode)) ? true : super.m_7933_(pKeyCode, pScanCode, pModifiers);
      }
   }

   public boolean m_6375_(double pX, double pY, int pButton) {
      this.scrolling = false;
      List<ManasSkill> skills = (List)this.filteredSkills.getValue();
      int lastDisplayedIndex = Math.min(this.startIndex + 7, skills.size());

      for(int i = this.startIndex; i < lastDisplayedIndex; ++i) {
         int x = this.getGuiLeft() + 6;
         int y = this.getGuiTop() + 43 + (i - this.startIndex) * 13;
         if (skills.size() <= i) {
            break;
         }

         if (pX >= (double)x && pY >= (double)y && pX < (double)(x + 89) && pY < (double)(y + 13)) {
            Minecraft.m_91087_().m_91106_().m_120367_(SimpleSoundInstance.m_119752_(SoundEvents.f_12490_, 1.0F));
            this.selectedSkill = (ManasSkill)skills.get(i);
            return true;
         }
      }

      if (pX >= (double)(this.getGuiLeft() + 98) && pX < (double)(this.getGuiLeft() + 109) && pY >= (double)(this.getGuiTop() + 43) && pY < (double)(this.getGuiTop() + 135)) {
         this.scrolling = true;
      }

      return super.m_6375_(pX, pY, pButton);
   }

   public boolean m_6050_(double pMouseX, double pMouseY, double pDelta) {
      if (this.isScrollBarActive()) {
         int i = this.getOffscreenRows();
         float f = (float)pDelta / (float)i;
         this.scrollOffs = Mth.m_14036_(this.scrollOffs - f, 0.0F, 1.0F);
         this.startIndex = Math.max(0, Math.min(this.startIndex - (int)pDelta, ((List)this.filteredSkills.getValue()).size() - 7));
      }

      return true;
   }

   public boolean m_7979_(double pMouseX, double pMouseY, int pButton, double pDragX, double pDragY) {
      if (this.scrolling && this.isScrollBarActive()) {
         int i = this.getGuiTop() + 43;
         int j = i + 91;
         this.scrollOffs = (float)((pMouseY - (double)i - 6.5D) / (double)((float)(j - i) - 13.0F));
         this.scrollOffs = Mth.m_14036_(this.scrollOffs, 0.0F, 1.0F);
         this.startIndex = (int)((double)(this.scrollOffs * (float)this.getOffscreenRows()) + 0.5D);
         return true;
      } else {
         return super.m_7979_(pMouseX, pMouseY, pButton, pDragX, pDragY);
      }
   }

   private MutableComponent skillName(@Nullable ManasSkill skill) {
      return skill != null && skill.getName() != null ? skill.getName() : Component.m_237115_("tensura.race.selection.skills.empty");
   }

   private boolean isScrollBarActive() {
      return ((List)this.filteredSkills.getValue()).size() > 7;
   }

   private int getOffscreenRows() {
      return ((List)this.filteredSkills.getValue()).size() - 7;
   }
}
