package com.github.manasmods.tensura.client.screen;

import com.github.manasmods.manascore.api.client.gui.FontRenderHelper;
import com.github.manasmods.manascore.api.client.gui.widget.ImagePredicateButton;
import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.tensura.ability.skill.Skill;
import com.github.manasmods.tensura.capability.skill.TensuraSkillCapability;
import com.github.manasmods.tensura.client.TensuraGUIHelper;
import com.github.manasmods.tensura.client.keybind.TensuraKeybinds;
import com.github.manasmods.tensura.menu.SkillMenu;
import com.github.manasmods.tensura.network.TensuraNetwork;
import com.github.manasmods.tensura.network.play2server.GUISwitchPacket;
import com.github.manasmods.tensura.network.play2server.skill.RequestAbilityPresetPacket;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.UnmodifiableIterator;
import com.mojang.blaze3d.platform.InputConstants;
import com.mojang.blaze3d.systems.RenderSystem;
import com.mojang.blaze3d.vertex.PoseStack;
import java.awt.Color;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import javax.annotation.Nullable;
import net.minecraft.ChatFormatting;
import net.minecraft.client.Minecraft;
import net.minecraft.client.gui.components.EditBox;
import net.minecraft.client.gui.components.Button.OnPress;
import net.minecraft.client.gui.components.Button.OnTooltip;
import net.minecraft.client.gui.screens.Screen;
import net.minecraft.client.gui.screens.inventory.AbstractContainerScreen;
import net.minecraft.client.renderer.GameRenderer;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.world.entity.player.Inventory;
import net.minecraft.world.entity.player.Player;

public class SkillScreen extends AbstractContainerScreen<SkillMenu> {
   private static final ResourceLocation BACKGROUND = new ResourceLocation("tensura", "textures/gui/skill/skill_gui.png");
   public static final ResourceLocation PRESET_BUTTON = new ResourceLocation("tensura", "textures/gui/skill/preset_button.png");
   private static final ResourceLocation RESISTANCE = new ResourceLocation("tensura", "textures/gui/skill/resistance.png");
   private static final ResourceLocation COMMON = new ResourceLocation("tensura", "textures/gui/skill/common.png");
   private static final ResourceLocation INTRINSIC = new ResourceLocation("tensura", "textures/gui/skill/intrinsic.png");
   private static final ResourceLocation EXTRA = new ResourceLocation("tensura", "textures/gui/skill/extra.png");
   private static final ResourceLocation UNIQUE = new ResourceLocation("tensura", "textures/gui/skill/unique.png");
   private static final ResourceLocation ULTIMATE = new ResourceLocation("tensura", "textures/gui/skill/ultimate.png");
   private List<String> presetNames = new ArrayList();
   private ManasSkill slot1;
   private ManasSkill slot2;
   private ManasSkill slot3;
   private int selectedPreset = 0;
   private final EditBox presetName;
   private final Player player;

   public SkillScreen(SkillMenu pMenu, Inventory pPlayerInventory, Component pTitle) {
      super(pMenu, pPlayerInventory, pTitle.m_6881_().m_130940_(ChatFormatting.WHITE));
      this.presetName = new EditBox(Minecraft.m_91087_().f_91062_, 0, 0, 94, 13, Component.m_237119_());
      this.f_97726_ = 254;
      this.f_97727_ = 163;
      this.player = pPlayerInventory.f_35978_;
   }

   protected void m_7856_() {
      super.m_7856_();
      TensuraSkillCapability.getFrom(this.player).ifPresent((cap) -> {
         this.selectedPreset = cap.getActivePreset();
         this.presetNames = cap.getPresetNames();
      });
      this.m_142416_(this.presetName);
      this.presetName.m_94182_(false);
      this.presetName.m_94202_(16777215);
      this.presetName.m_94199_(20);
      this.presetName.f_93620_ = this.getGuiLeft() + 130;
      this.presetName.f_93621_ = this.getGuiTop() + 63;

      ImagePredicateButton button;
      for(int i = 0; i < 9; ++i) {
         ResourceLocation var10006 = PRESET_BUTTON;
         OnPress var10007 = (pButton) -> {
            if (this.selectedPreset == i) {
               TensuraNetwork.INSTANCE.sendToServer(new RequestAbilityPresetPacket(i, true));
            } else {
               this.selectedPreset = i;
               this.loadPreset();
            }
         };
         OnTooltip var10008 = (pButton, pPoseStack1, pMouseX1, pMouseY1) -> {
            this.m_96602_(pPoseStack1, Component.m_237113_((String)this.presetNames.get(i)), pMouseX1, pMouseY1);
         };
         SkillMenu var10009 = (SkillMenu)this.f_97732_;
         Objects.requireNonNull(var10009);
         button = new ImagePredicateButton(0, 0, 10, 11, var10006, var10007, var10008, var10009::check);
         button.f_93620_ = this.getGuiLeft() + 128 + i * 10;
         button.f_93621_ = this.getGuiTop() + 40;
         this.m_142416_(button);
         if (i < 6) {
            Skill.SkillType type;
            ResourceLocation texture;
            switch(i) {
            case 0:
               type = Skill.SkillType.RESISTANCE;
               texture = RESISTANCE;
               break;
            case 1:
               type = Skill.SkillType.INTRINSIC;
               texture = INTRINSIC;
               break;
            case 2:
               type = Skill.SkillType.COMMON;
               texture = COMMON;
               break;
            case 3:
               type = Skill.SkillType.EXTRA;
               texture = EXTRA;
               break;
            case 4:
               type = Skill.SkillType.UNIQUE;
               texture = UNIQUE;
               break;
            default:
               type = Skill.SkillType.ULTIMATE;
               texture = ULTIMATE;
            }

            var10007 = (pButton) -> {
               TensuraNetwork.INSTANCE.sendToServer(new GUISwitchPacket(type));
            };
            var10008 = (pButton, pPoseStack1, pMouseX, pMouseY) -> {
               this.m_96602_(pPoseStack1, type.getName(), pMouseX, pMouseY);
            };
            var10009 = (SkillMenu)this.f_97732_;
            Objects.requireNonNull(var10009);
            ImagePredicateButton skillTab = new ImagePredicateButton(0, 0, 32, 32, texture, var10007, var10008, var10009::check);
            skillTab.f_93620_ = i % 2 == 0 ? this.getGuiLeft() + 19 : this.getGuiLeft() + 63;
            skillTab.f_93621_ = i < 2 ? this.getGuiTop() + 45 : (i < 4 ? this.getGuiTop() + 83 : this.getGuiTop() + 121);
            this.m_142416_(skillTab);
         }
      }

      ImmutableList<ImagePredicateButton> buttons = TensuraGUIHelper.addMenuTabs(this, 4, true);
      UnmodifiableIterator var2 = buttons.iterator();

      while(var2.hasNext()) {
         button = (ImagePredicateButton)var2.next();
         this.m_142416_(button);
      }

      this.loadPreset();
   }

   protected void m_7286_(PoseStack pPoseStack, float pPartialTick, int pMouseX, int pMouseY) {
      this.m_7333_(pPoseStack);
      RenderSystem.m_157427_(GameRenderer::m_172817_);
      RenderSystem.m_157429_(1.0F, 1.0F, 1.0F, 1.0F);
      RenderSystem.m_157456_(0, BACKGROUND);
      int x = (this.f_96543_ - this.f_97726_) / 2;
      int y = (this.f_96544_ - this.f_97727_) / 2;
      this.m_93228_(pPoseStack, x, y, 0, 0, this.f_97726_, this.f_97727_);
      TensuraGUIHelper.renderTabIcon(pPoseStack, this, 4, pMouseX, pMouseY);

      for(int i = 0; i < 9; ++i) {
         if (i < 3) {
            ManasSkill manasSkill = i == 0 ? this.slot1 : (i == 1 ? this.slot2 : this.slot3);
            if (manasSkill != null) {
               MutableComponent name = this.skillName(manasSkill);
               ManasSkillInstance instance = this.getSkillInstance(manasSkill);
               if (instance != null && instance.getMastery() < 0) {
                  name = name.m_130940_(ChatFormatting.GRAY);
               } else if (manasSkill instanceof Skill) {
                  Skill skill = (Skill)manasSkill;
                  name = name.m_130940_(skill.getType().getChatFormatting());
               }

               int xName = this.getGuiLeft() + 149;
               int yName = this.getGuiTop() + 88 + i * 22;
               FontRenderHelper.renderScaledTextInArea(pPoseStack, this.f_96547_, TensuraGUIHelper.shortenTextComponent((Component)name, 11), (float)xName, (float)yName, 69.0F, 10.0F, Color.CYAN);
               ResourceLocation icon = manasSkill.getSkillIcon();
               if (icon != null) {
                  RenderSystem.m_157456_(0, icon);
                  m_93133_(pPoseStack, this.getGuiLeft() + 128, this.getGuiTop() + 83 + i * 22, 0.0F, 0.0F, 16, 16, 16, 16);
               }

               boolean hovering = pMouseX >= xName - 22 && pMouseY >= yName - 6 && pMouseX < xName + 71 && pMouseY < yName + 16;
               if (hovering) {
                  this.m_96602_(pPoseStack, name, pMouseX, pMouseY);
               }
            }
         }

         TensuraGUIHelper.renderScaledCenteredXText(this.f_96547_, pPoseStack, Component.m_237113_(String.valueOf(i + 1)), this.getGuiLeft() + 128 + i * 10, this.getGuiTop() + 42, 10, 11, Color.LIGHT_GRAY, true);
      }

      this.m_7025_(pPoseStack, pMouseX, pMouseY);
   }

   protected void m_7027_(PoseStack pPoseStack, int pMouseX, int pMouseY) {
   }

   public boolean m_7933_(int pKeyCode, int pScanCode, int pModifiers) {
      if (this.presetName.m_7933_(pKeyCode, pScanCode, pModifiers)) {
         return true;
      } else if (this.presetName.m_93696_() && this.presetName.m_94213_() && pKeyCode != 256) {
         return true;
      } else {
         if (this.f_96541_ != null) {
            if (TensuraKeybinds.MAIN_GUI.m_90832_(pKeyCode, pScanCode)) {
               this.f_96541_.m_91152_((Screen)null);
               this.f_96541_.f_91067_.m_91601_();
               return true;
            }

            if (this.f_96541_.f_91066_.f_92092_.isActiveAndMatches(InputConstants.m_84827_(pKeyCode, pScanCode))) {
               return true;
            }
         }

         return super.m_7933_(pKeyCode, pScanCode, pModifiers);
      }
   }

   public boolean m_6375_(double pMouseX, double pMouseY, int pButton) {
      if (!(pMouseX > (double)this.presetName.f_93620_) || !(pMouseX < (double)(this.presetName.f_93620_ + this.presetName.m_5711_())) || !(pMouseY > (double)this.presetName.f_93621_) || !(pMouseY < (double)(this.presetName.f_93621_ + this.presetName.m_93694_()))) {
         this.savePresetName();
      }

      if (TensuraGUIHelper.mouseOver(pMouseX, pMouseY, this.getGuiLeft() + 5, this.getGuiLeft() + 23, this.getGuiTop() + 28, this.getGuiTop() + 38)) {
         TensuraGUIHelper.playSound(SoundEvents.f_12490_, 1.0F);
         TensuraNetwork.INSTANCE.sendToServer(new GUISwitchPacket(0));
      }

      return super.m_6375_(pMouseX, pMouseY, pButton);
   }

   protected void m_7025_(PoseStack pPoseStack, int pX, int pY) {
      if (TensuraGUIHelper.mouseOver(pX, pY, this.getGuiLeft() + 5, this.getGuiLeft() + 23, this.getGuiTop() + 28, this.getGuiTop() + 38)) {
         this.m_96602_(pPoseStack, Component.m_237115_("tooltip.tensura.return"), pX, pY);
      }

      super.m_7025_(pPoseStack, pX, pY);
   }

   public void m_7379_() {
      this.savePresetName();
      super.m_7379_();
   }

   private MutableComponent skillName(@Nullable ManasSkill skill) {
      return skill != null && skill.getName() != null ? skill.getName() : Component.m_237115_("tensura.race.selection.skills.empty");
   }

   private void loadPreset() {
      this.presetName.m_94144_((String)this.presetNames.get(this.selectedPreset));
      TensuraSkillCapability.getFrom(this.player).ifPresent((cap) -> {
         this.slot1 = cap.getSkillInPresetSlot(this.selectedPreset, 0);
         this.slot2 = cap.getSkillInPresetSlot(this.selectedPreset, 1);
         this.slot3 = cap.getSkillInPresetSlot(this.selectedPreset, 2);
      });
   }

   private void savePresetName() {
      TensuraSkillCapability.getFrom(this.player).ifPresent((cap) -> {
         if (!this.presetName.m_94155_().equals(cap.getPresetName(this.selectedPreset))) {
            TensuraNetwork.INSTANCE.sendToServer(new RequestAbilityPresetPacket(this.presetName.m_94155_(), this.selectedPreset));
            this.presetNames.set(this.selectedPreset, this.presetName.m_94155_());
         }

      });
   }

   @Nullable
   private ManasSkillInstance getSkillInstance(ManasSkill skill) {
      Optional<ManasSkillInstance> instance = SkillAPI.getSkillsFrom(this.player).getSkill(skill);
      return (ManasSkillInstance)instance.orElse((Object)null);
   }
}
