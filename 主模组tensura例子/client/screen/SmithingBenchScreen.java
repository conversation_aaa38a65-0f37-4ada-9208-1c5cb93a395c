package com.github.manasmods.tensura.client.screen;

import com.github.manasmods.tensura.client.TensuraGUIHelper;
import com.github.manasmods.tensura.client.screen.widget.MenuTab;
import com.github.manasmods.tensura.data.recipe.SmithingBenchRecipe;
import com.github.manasmods.tensura.menu.SmithingBenchMenu;
import com.github.manasmods.tensura.network.play2server.RequestMenuSwitchPacket;
import com.github.manasmods.tensura.registry.blocks.TensuraBlocks;
import com.github.manasmods.tensura.util.Cached;
import com.mojang.blaze3d.platform.InputConstants;
import com.mojang.blaze3d.systems.RenderSystem;
import com.mojang.blaze3d.vertex.PoseStack;
import java.util.ArrayList;
import java.util.List;
import net.minecraft.client.Minecraft;
import net.minecraft.client.gui.components.EditBox;
import net.minecraft.client.gui.screens.inventory.AbstractContainerScreen;
import net.minecraft.client.renderer.GameRenderer;
import net.minecraft.client.renderer.entity.ItemRenderer;
import net.minecraft.network.chat.Component;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.util.Mth;
import net.minecraft.world.entity.player.Inventory;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.crafting.Ingredient;
import net.minecraft.world.level.block.Blocks;

public class SmithingBenchScreen extends AbstractContainerScreen<SmithingBenchMenu> {
   private static final ResourceLocation BACKGROUND = new ResourceLocation("tensura", "textures/gui/smithing/background.png");
   private static final ResourceLocation SLOT = new ResourceLocation("tensura", "textures/gui/slot.png");
   private static final ResourceLocation STONE_CUTTER = new ResourceLocation("textures/gui/container/stonecutter.png");
   private static final ResourceLocation CHECK = new ResourceLocation("tensura", "textures/gui/smithing/check.png");
   private final EditBox searchField;
   private final MenuTab smithingBenchTab;
   private final MenuTab smithingTableTab;
   private final MenuTab anvilTab;
   private ItemRenderer itemRenderer;
   private final Player player;
   private boolean scrolling;
   private float scrollOffs;
   private int startIndex;
   private final Cached<List<SmithingBenchRecipe>, String> filteredRecipes;

   public SmithingBenchScreen(SmithingBenchMenu pMenu, Inventory pPlayerInventory, Component pTitle) {
      super(pMenu, pPlayerInventory, pTitle);
      this.player = pPlayerInventory.f_35978_;
      this.f_97726_ = 195;
      this.f_97727_ = 194;
      this.searchField = new EditBox(Minecraft.m_91087_().f_91062_, 0, 0, 80, 20, Component.m_237119_());
      this.searchField.m_94182_(false);
      this.searchField.m_94202_(16777215);
      this.searchField.m_94151_((s) -> {
         if (!s.isEmpty()) {
            this.scrollOffs = 0.0F;
            this.startIndex = 0;
         }
      });
      this.smithingBenchTab = new MenuTab(0, 0, (RequestMenuSwitchPacket.SwitchType)null, (Item)TensuraBlocks.Items.SMITHING_BENCH.get(), true, (pButton, pPoseStack, pMouseX, pMouseY) -> {
         this.m_96602_(pPoseStack, Component.m_237115_("tensura.smithing_bench.menu.open"), pMouseX, pMouseY);
      });
      this.smithingTableTab = new MenuTab(0, 0, RequestMenuSwitchPacket.SwitchType.SMITHING_TO_VANILLA_SMITHING, Blocks.f_50625_.m_5456_(), false, (pButton, pPoseStack, pMouseX, pMouseY) -> {
         this.m_96602_(pPoseStack, Component.m_237115_("tensura.smithing_table.menu.open"), pMouseX, pMouseY);
      });
      this.anvilTab = new MenuTab(0, 0, RequestMenuSwitchPacket.SwitchType.SMITHING_TO_ANVIL, Blocks.f_50322_.m_5456_(), false, (pButton, pPoseStack, pMouseX, pMouseY) -> {
         this.m_96602_(pPoseStack, Component.m_237115_("tensura.anvil.menu.open"), pMouseX, pMouseY);
      });
      this.filteredRecipes = new Cached(() -> {
         List<SmithingBenchRecipe> filteredRecipeList = new ArrayList(((SmithingBenchMenu)this.f_97732_).getRecipes());
         if (!this.searchField.m_94155_().isEmpty() && !this.searchField.m_94155_().isBlank()) {
            String filterValue = this.searchField.m_94155_().toLowerCase();
            filteredRecipeList.removeIf((recipe) -> {
               return !recipe.m_8043_().m_41786_().getString().toLowerCase().contains(filterValue);
            });
         }

         return filteredRecipeList;
      }, (info) -> {
         if (info.lastCallbackReference == null || !((String)info.lastCallbackReference).equals(this.searchField.m_94155_())) {
            info.lastCallbackReference = this.searchField.m_94155_();
            info.needsUpdate = true;
         }

         return info;
      });
   }

   protected void m_7856_() {
      super.m_7856_();
      this.f_97728_ = 6;
      this.f_97730_ = 17;
      this.f_97731_ = this.f_97727_ - 92;
      this.scrollOffs = 0.0F;
      this.startIndex = 0;
      this.m_142416_(this.searchField);
      this.searchField.f_93620_ = this.getGuiLeft() + 82;
      this.searchField.f_93621_ = this.getGuiTop() + 5;
      this.m_142416_(this.smithingBenchTab);
      this.smithingBenchTab.f_93620_ = this.getGuiLeft() + 5;
      this.smithingBenchTab.f_93621_ = this.getGuiTop() - this.smithingBenchTab.m_93694_() + 4;
      this.m_142416_(this.smithingTableTab);
      this.smithingTableTab.f_93620_ = this.smithingBenchTab.f_93620_ + this.smithingBenchTab.m_5711_();
      this.smithingTableTab.f_93621_ = this.smithingBenchTab.f_93621_;
      this.m_142416_(this.anvilTab);
      this.anvilTab.f_93620_ = this.smithingTableTab.f_93620_ + this.smithingTableTab.m_5711_();
      this.anvilTab.f_93621_ = this.smithingTableTab.f_93621_;
   }

   public void m_6305_(PoseStack pPoseStack, int pMouseX, int pMouseY, float pPartialTick) {
      super.m_6305_(pPoseStack, pMouseX, pMouseY, pPartialTick);
      this.m_7025_(pPoseStack, pMouseX, pMouseY);
   }

   protected void m_7286_(PoseStack pPoseStack, float pPartialTick, int pMouseX, int pMouseY) {
      this.m_7333_(pPoseStack);
      RenderSystem.m_157427_(GameRenderer::m_172817_);
      RenderSystem.m_157429_(1.0F, 1.0F, 1.0F, 1.0F);
      RenderSystem.m_157456_(0, BACKGROUND);
      int x = (this.f_96543_ - this.f_97726_) / 2;
      int y = (this.f_96544_ - this.f_97727_) / 2;
      this.m_93228_(pPoseStack, x, y, 0, 0, this.f_97726_, this.f_97727_);
      int k = (int)(37.0F * this.scrollOffs);
      RenderSystem.m_157456_(0, STONE_CUTTER);
      this.m_93228_(pPoseStack, this.f_97735_ + 175, this.f_97736_ + 21 + k, 176 + (this.isScrollBarActive() ? 0 : 12), 0, 12, 15);
      int recipeXPos = this.f_97735_ + 8;
      int recipeYPos = this.f_97736_ + 18;
      int lastVisibleElementIndex = this.startIndex + 27;
      this.itemRenderer = this.f_96541_.m_91291_();
      List<SmithingBenchRecipe> filteredRecipeList = (List)this.filteredRecipes.getValue();
      this.renderButtons(pPoseStack, pMouseX, pMouseY, recipeXPos, recipeYPos, lastVisibleElementIndex, filteredRecipeList);
      this.renderRecipes(pPoseStack, recipeXPos, recipeYPos, lastVisibleElementIndex, filteredRecipeList, pMouseX, pMouseY);
   }

   private void renderButtons(PoseStack pPoseStack, int pMouseX, int pMouseY, int pX, int pY, int pLastVisibleElementIndex, List<SmithingBenchRecipe> list) {
      RenderSystem.m_157456_(0, SLOT);

      for(int i = this.startIndex; i < pLastVisibleElementIndex && i < list.size(); ++i) {
         int j = i - this.startIndex;
         int x = pX + j % 9 * 18;
         int l = j / 9;
         int y = pY + l * 18 + 3;
         int stonecutterImageHeight = 0;
         int filteredIndex = ((SmithingBenchMenu)this.f_97732_).getSelectedRecipeIndex() == -1 ? -1 : list.indexOf(((SmithingBenchMenu)this.f_97732_).getRecipes().get(((SmithingBenchMenu)this.f_97732_).getSelectedRecipeIndex()));
         if (i == filteredIndex) {
            stonecutterImageHeight += 18;
         } else if (pMouseX >= x && pMouseY >= y - 1 && pMouseX < x + 18 && pMouseY < y + 17) {
            stonecutterImageHeight += 36;
         }

         m_93133_(pPoseStack, x, y - 1, 0.0F, (float)stonecutterImageHeight, 18, 18, 18, 54);
      }

   }

   private void renderRecipes(PoseStack pPoseStack, int pLeft, int pTop, int pRecipeIndexOffsetMax, List<SmithingBenchRecipe> list, int pMouseX, int pMouseY) {
      int index;
      for(int i = this.startIndex; i < pRecipeIndexOffsetMax && i < list.size(); ++i) {
         int j = i - this.startIndex;
         index = pLeft + j % 9 * 18 + 1;
         int l = j / 9;
         int y = pTop + l * 18 + 3;
         SmithingBenchRecipe recipe = (SmithingBenchRecipe)list.get(i);
         this.itemRenderer.m_115203_(recipe.m_8043_(), index, y);
         if (TensuraGUIHelper.mouseOver(pMouseX, pMouseY, index - 2, index + 17, y - 2, y + 17)) {
            this.m_6057_(pPoseStack, recipe.m_8043_(), pMouseX, pMouseY);
         }
      }

      if (((SmithingBenchMenu)this.f_97732_).getSelectedRecipeIndex() != -1) {
         SmithingBenchRecipe recipe = (SmithingBenchRecipe)((SmithingBenchMenu)this.f_97732_).getRecipes().get(((SmithingBenchMenu)this.f_97732_).getSelectedRecipeIndex());
         boolean recipeMatches = recipe.m_5818_(this.player.m_150109_(), this.player.f_19853_);
         RenderSystem.m_157456_(0, CHECK);
         RenderSystem.m_69478_();
         RenderSystem.m_69453_();
         m_93160_(pPoseStack, this.getGuiLeft() + 112, this.getGuiTop() + 85, 8, 8, 0.0F, recipeMatches ? 8.0F : 0.0F, 8, 8, 8, 16);
         RenderSystem.m_69461_();

         for(index = 0; index < 5; ++index) {
            Ingredient ingredient = (Ingredient)recipe.m_7527_().get(index);
            if (!ingredient.m_43947_()) {
               ItemStack stack = ingredient.m_43908_()[0];
               stack.m_41764_((Integer)recipe.getIngedientAmount().get(index));
               this.itemRenderer.m_115169_(this.f_96547_, stack, this.getGuiLeft() + 17 + 18 * index, this.getGuiTop() + 80);
               this.itemRenderer.m_115123_(stack, this.getGuiLeft() + 18 + 18 * index, this.getGuiTop() + 81);
               int x = this.getGuiLeft() + 16 + 18 * index;
               int y = this.getGuiTop() + 79;
               if (TensuraGUIHelper.mouseOver(pMouseX, pMouseY, x, x + 19, y, y + 19)) {
                  this.m_6057_(pPoseStack, stack, pMouseX, pMouseY);
               }
            }
         }

      }
   }

   public boolean m_7933_(int pKeyCode, int pScanCode, int pModifiers) {
      if (this.searchField.m_7933_(pKeyCode, pScanCode, pModifiers)) {
         return true;
      } else if (this.searchField.m_93696_() && this.searchField.m_94213_() && pKeyCode != 256) {
         return true;
      } else {
         return this.f_96541_ != null && this.f_96541_.f_91066_.f_92092_.isActiveAndMatches(InputConstants.m_84827_(pKeyCode, pScanCode)) ? true : super.m_7933_(pKeyCode, pScanCode, pModifiers);
      }
   }

   public boolean m_6375_(double pMouseX, double pMouseY, int pButton) {
      List<SmithingBenchRecipe> filteredRecipeList = (List)this.filteredRecipes.getValue();
      this.scrolling = false;
      int recipeAreaLeft = this.f_97735_ + 8;
      int recipeAreaTop = this.f_97736_ + 20;
      int lastDisplayedRecipeIndex = this.startIndex + 27;

      for(int l = this.startIndex; l < lastDisplayedRecipeIndex; ++l) {
         int i1 = l - this.startIndex;
         double d0 = pMouseX - (double)(recipeAreaLeft + i1 % 9 * 18);
         double d1 = pMouseY - (double)(recipeAreaTop + i1 / 9 * 18);
         if (filteredRecipeList.size() <= l) {
            break;
         }

         int unfilteredIndex = ((SmithingBenchMenu)this.f_97732_).getRecipes().indexOf(filteredRecipeList.get(l));
         if (d0 >= 0.0D && d1 >= 0.0D && d0 < 18.0D && d1 < 18.0D && TensuraGUIHelper.buttonClick(this.player, this, SoundEvents.f_12490_, unfilteredIndex)) {
            return true;
         }
      }

      recipeAreaLeft = this.f_97735_ + 175;
      recipeAreaTop = this.f_97736_ + 21;
      if (pMouseX >= (double)recipeAreaLeft && pMouseX < (double)(recipeAreaLeft + 12) && pMouseY >= (double)recipeAreaTop && pMouseY < (double)(recipeAreaTop + 54)) {
         this.scrolling = true;
      }

      return super.m_6375_(pMouseX, pMouseY, pButton);
   }

   public boolean m_7979_(double pMouseX, double pMouseY, int pButton, double pDragX, double pDragY) {
      if (this.scrolling && this.isScrollBarActive()) {
         int i = this.f_97736_ + 18;
         int j = i + 54;
         this.scrollOffs = ((float)pMouseY - (float)i - 7.5F) / ((float)(j - i) - 15.0F);
         this.scrollOffs = Mth.m_14036_(this.scrollOffs, 0.0F, 1.0F);
         this.startIndex = (int)((double)(this.scrollOffs * (float)this.getOffscreenRows()) + 0.5D) * 9;
         return true;
      } else {
         return super.m_7979_(pMouseX, pMouseY, pButton, pDragX, pDragY);
      }
   }

   public boolean m_6050_(double pMouseX, double pMouseY, double pDelta) {
      if (this.isScrollBarActive()) {
         int i = this.getOffscreenRows();
         float f = (float)pDelta / (float)i;
         this.scrollOffs = Mth.m_14036_(this.scrollOffs - f, 0.0F, 1.0F);
         this.startIndex = (int)((double)(this.scrollOffs * (float)i) + 0.5D) * 9;
      }

      return true;
   }

   private boolean isScrollBarActive() {
      return ((List)this.filteredRecipes.getValue()).size() > 27;
   }

   protected int getOffscreenRows() {
      return (((List)this.filteredRecipes.getValue()).size() + 9 - 1) / 9 - 3;
   }
}
