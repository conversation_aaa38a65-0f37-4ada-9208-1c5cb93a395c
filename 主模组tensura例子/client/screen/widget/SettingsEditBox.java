package com.github.manasmods.tensura.client.screen.widget;

import com.mojang.blaze3d.vertex.PoseStack;
import net.minecraft.client.gui.Font;
import net.minecraft.client.gui.components.Button;
import net.minecraft.client.gui.components.EditBox;
import net.minecraft.network.chat.Component;
import org.jetbrains.annotations.NotNull;

public class SettingsEditBox extends EditBox {
   public final int originalX;
   public final int originalY;
   public final int centerX;
   public final int centerY;
   public Button resetButton;

   public SettingsEditBox(Font pFont, int pX, int pY, int centerX, int centerY, int pWidth, int pHeight, Component pMessage) {
      super(pFont, pX, pY, pWidth, pHeight, pMessage);
      this.originalX = pX;
      this.originalY = pY;
      this.centerX = centerX;
      this.centerY = centerY;
   }

   public void m_6305_(@NotNull PoseStack pPoseStack, int pMouseX, int pMouseY, float pPartialTick) {
      super.m_6305_(pPoseStack, pMouseX, pMouseY, pPartialTick);
      if (this.resetButton != null && !this.m_93696_()) {
         this.resetButton.m_6305_(pPoseStack, pMouseX, pMouseY, pPartialTick);
      }

   }

   public void moveBox(boolean focused) {
      if (focused) {
         this.f_93620_ = this.centerX - this.f_93618_ / 2;
         this.f_93621_ = this.centerY - this.f_93619_ / 2;
      } else {
         this.f_93620_ = this.originalX;
         this.f_93621_ = this.originalY;
      }
   }

   public void switchStates(boolean newState) {
      this.f_93623_ = newState;
      this.f_93624_ = newState;
      this.resetButton.f_93623_ = newState;
      this.resetButton.f_93624_ = newState;
   }

   protected void m_93692_(boolean pFocused) {
      super.m_93692_(pFocused);
      this.moveBox(pFocused);
   }
}
