package com.github.manasmods.tensura.client.screen;

import com.github.manasmods.manascore.api.client.gui.widget.ImagePredicateButton;
import com.github.manasmods.tensura.block.entity.KilnBlockEntity;
import com.github.manasmods.tensura.data.pack.KilnMoltenMaterial;
import com.github.manasmods.tensura.data.pack.TensuraData;
import com.github.manasmods.tensura.data.recipe.KilnMixingRecipe;
import com.github.manasmods.tensura.menu.KilnMenu;
import com.github.manasmods.tensura.network.TensuraNetwork;
import com.github.manasmods.tensura.network.play2server.RequestKilnActionPacket;
import com.mojang.blaze3d.platform.InputConstants;
import com.mojang.blaze3d.systems.RenderSystem;
import com.mojang.blaze3d.vertex.BufferBuilder;
import com.mojang.blaze3d.vertex.BufferUploader;
import com.mojang.blaze3d.vertex.DefaultVertexFormat;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.blaze3d.vertex.Tesselator;
import com.mojang.blaze3d.vertex.VertexFormat.Mode;
import com.mojang.math.Matrix4f;
import java.awt.Color;
import java.util.Objects;
import java.util.Optional;
import net.minecraft.client.gui.components.Button.OnPress;
import net.minecraft.client.gui.components.Button.OnTooltip;
import net.minecraft.client.gui.screens.inventory.AbstractContainerScreen;
import net.minecraft.client.renderer.GameRenderer;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.network.chat.Style;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.entity.player.Inventory;

public class KilnScreen extends AbstractContainerScreen<KilnMenu> {
   private static final ResourceLocation TEXTURE = new ResourceLocation("tensura", "textures/gui/kiln/kiln_gui.png");
   private static final ResourceLocation LEFT_BUTTON = new ResourceLocation("tensura", "textures/gui/kiln/kiln_mixing_left.png");
   private static final ResourceLocation RIGHT_BUTTON = new ResourceLocation("tensura", "textures/gui/kiln/kiln_mixing_right.png");
   private static final ResourceLocation FLUID = new ResourceLocation("tensura", "textures/gui/kiln/molten.png");
   private final ImagePredicateButton mixingLeft;
   private final ImagePredicateButton mixingRight;
   private Optional<KilnMoltenMaterial> leftBarMaterial;
   private Optional<KilnMoltenMaterial> rightBarMaterial;

   public KilnScreen(KilnMenu menu, Inventory inventory, Component component) {
      super(menu, inventory, component);
      this.leftBarMaterial = menu.blockEntity.getLeftBarId().flatMap((location) -> {
         return TensuraData.getKilnMoltenMaterials().stream().filter((moltenMaterial) -> {
            return moltenMaterial.getMoltenType().equals(location);
         }).findFirst();
      });
      this.rightBarMaterial = menu.blockEntity.getRightBarId().flatMap((location) -> {
         return TensuraData.getKilnMoltenMaterials().stream().filter((moltenMaterial) -> {
            return moltenMaterial.getMoltenType().equals(location);
         }).findFirst();
      });
      ResourceLocation var10007 = LEFT_BUTTON;
      OnPress var10008 = (pButton) -> {
         TensuraNetwork.INSTANCE.sendToServer(new RequestKilnActionPacket(RequestKilnActionPacket.Action.MIXING_LEFT));
      };
      OnTooltip var10009 = (pButton, pPoseStack, pMouseX, pMouseY) -> {
         this.m_96602_(pPoseStack, Component.m_237115_("tooltip.tensura.kiln.mixing_left"), pMouseX, pMouseY);
      };
      KilnBlockEntity var10010 = menu.blockEntity;
      Objects.requireNonNull(var10010);
      this.mixingLeft = new ImagePredicateButton(0, 0, 4, 8, var10007, var10008, var10009, var10010::hasPrevMixingRecipe);
      var10007 = RIGHT_BUTTON;
      var10008 = (pButton) -> {
         TensuraNetwork.INSTANCE.sendToServer(new RequestKilnActionPacket(RequestKilnActionPacket.Action.MIXING_RIGHT));
      };
      var10009 = (pButton, pPoseStack, pMouseX, pMouseY) -> {
         this.m_96602_(pPoseStack, Component.m_237115_("tooltip.tensura.kiln.mixing_right"), pMouseX, pMouseY);
      };
      var10010 = menu.blockEntity;
      Objects.requireNonNull(var10010);
      this.mixingRight = new ImagePredicateButton(0, 0, 4, 8, var10007, var10008, var10009, var10010::hasNextMixingRecipe);
      this.f_97726_ = 256;
      this.f_97727_ = 145;
   }

   protected void m_7856_() {
      super.m_7856_();
      this.m_169411_(this.mixingLeft);
      this.mixingLeft.f_93620_ = this.getGuiLeft() + 69;
      this.mixingLeft.f_93621_ = this.getGuiTop() + 40;
      this.m_142416_(this.mixingLeft);
      this.m_169411_(this.mixingRight);
      this.mixingRight.f_93620_ = this.getGuiLeft() + 103;
      this.mixingRight.f_93621_ = this.getGuiTop() + 40;
      this.m_142416_(this.mixingRight);
   }

   protected void m_181908_() {
      super.m_181908_();
      if (hasChanged(((KilnMenu)this.f_97732_).blockEntity.getLeftBarId(), this.leftBarMaterial)) {
         this.leftBarMaterial = materialOf(((KilnMenu)this.f_97732_).blockEntity.getLeftBarId());
      }

      if (hasChanged(((KilnMenu)this.f_97732_).blockEntity.getRightBarId(), this.rightBarMaterial)) {
         this.rightBarMaterial = materialOf(((KilnMenu)this.f_97732_).blockEntity.getRightBarId());
      }

   }

   private static Optional<KilnMoltenMaterial> materialOf(Optional<ResourceLocation> id) {
      return id.flatMap((location) -> {
         return TensuraData.getKilnMoltenMaterials().stream().filter((moltenMaterial) -> {
            return moltenMaterial.getMoltenType().equals(location);
         }).findFirst();
      });
   }

   private static boolean hasChanged(Optional<ResourceLocation> barId, Optional<KilnMoltenMaterial> material) {
      if (barId.isEmpty()) {
         return material.isPresent();
      } else if (material.isEmpty()) {
         return true;
      } else {
         return !((KilnMoltenMaterial)material.get()).getMoltenType().equals(barId.get());
      }
   }

   protected void m_7286_(PoseStack pPoseStack, float pPartialTick, int pMouseX, int pMouseY) {
      RenderSystem.m_157427_(GameRenderer::m_172817_);
      RenderSystem.m_157429_(1.0F, 1.0F, 1.0F, 1.0F);
      RenderSystem.m_157456_(0, TEXTURE);
      int x = (this.f_96543_ - this.f_97726_) / 2;
      int y = (this.f_96544_ - this.f_97727_) / 2;
      this.m_93228_(pPoseStack, x, y, 0, 0, 256, 168);
      this.renderProgress(pPoseStack, x, y);
      this.renderFire(pPoseStack, x, y);
      this.renderMolten(pPoseStack, x, y);
      this.renderMagic(pPoseStack, x, y);
   }

   private void renderProgress(PoseStack pPoseStack, int x, int y) {
      if (((KilnMenu)this.f_97732_).isSmelting()) {
         this.m_93228_(pPoseStack, x + 199, y + 72 - ((KilnMenu)this.f_97732_).getScaledProgress(), 0, 246 - ((KilnMenu)this.f_97732_).getScaledProgress(), 24, ((KilnMenu)this.f_97732_).getScaledProgress());
      }

   }

   private void renderMolten(PoseStack stack, int x, int y) {
      this.leftBarMaterial.ifPresent((moltenMaterial) -> {
         int width = 13;
         int height = ((KilnMenu)this.f_97732_).getMoltenProgress();
         if (((KilnMenu)this.f_97732_).getMoltenProgress() > 0 && height < 1) {
            height = 1;
         }

         int renderX = x + 18;
         int renderY = y + 80 - height;
         float u = 0.07692308F * (float)width;
         float v = 0.013513514F * (float)height;
         Matrix4f pMatrix = stack.m_85850_().m_85861_();
         BufferBuilder bufferbuilder = Tesselator.m_85913_().m_85915_();
         RenderSystem.m_69478_();
         RenderSystem.m_69453_();
         RenderSystem.m_157456_(0, FLUID);
         RenderSystem.m_157427_(GameRenderer::m_172814_);
         bufferbuilder.m_166779_(Mode.QUADS, DefaultVertexFormat.f_85818_);
         bufferbuilder.m_85982_(pMatrix, (float)renderX, (float)renderY + (float)height, 0.0F).m_6122_(moltenMaterial.getRed(), moltenMaterial.getGreen(), moltenMaterial.getBlue(), moltenMaterial.getAlpha()).m_7421_(0.0F, v).m_5752_();
         bufferbuilder.m_85982_(pMatrix, (float)renderX + (float)width, (float)renderY + (float)height, 0.0F).m_6122_(moltenMaterial.getRed(), moltenMaterial.getGreen(), moltenMaterial.getBlue(), moltenMaterial.getAlpha()).m_7421_(u, v).m_5752_();
         bufferbuilder.m_85982_(pMatrix, (float)renderX + (float)width, (float)renderY, 0.0F).m_6122_(moltenMaterial.getRed(), moltenMaterial.getGreen(), moltenMaterial.getBlue(), moltenMaterial.getAlpha()).m_7421_(u, 0.0F).m_5752_();
         bufferbuilder.m_85982_(pMatrix, (float)renderX, (float)renderY, 0.0F).m_6122_(moltenMaterial.getRed(), moltenMaterial.getGreen(), moltenMaterial.getBlue(), moltenMaterial.getAlpha()).m_7421_(0.0F, 0.0F).m_5752_();
         BufferUploader.m_231202_(bufferbuilder.m_231175_());
         RenderSystem.m_69461_();
      });
   }

   private void renderMagic(PoseStack stack, int x, int y) {
      this.rightBarMaterial.ifPresent((moltenMaterial) -> {
         int width = 13;
         int height = ((KilnMenu)this.f_97732_).getMagisteelProgress();
         if (((KilnMenu)this.f_97732_).getMagisteelProgress() > 0 && height < 1) {
            height = 1;
         }

         int renderX = x + 145;
         int renderY = y + 80 - height;
         float u = 0.07692308F * (float)width;
         float v = 0.013513514F * (float)height;
         Matrix4f pMatrix = stack.m_85850_().m_85861_();
         BufferBuilder bufferbuilder = Tesselator.m_85913_().m_85915_();
         RenderSystem.m_69478_();
         RenderSystem.m_69453_();
         RenderSystem.m_157456_(0, FLUID);
         RenderSystem.m_157427_(GameRenderer::m_172814_);
         bufferbuilder.m_166779_(Mode.QUADS, DefaultVertexFormat.f_85818_);
         bufferbuilder.m_85982_(pMatrix, (float)renderX, (float)renderY + (float)height, 0.0F).m_6122_(moltenMaterial.getRed(), moltenMaterial.getGreen(), moltenMaterial.getBlue(), moltenMaterial.getAlpha()).m_7421_(0.0F, v).m_5752_();
         bufferbuilder.m_85982_(pMatrix, (float)renderX + (float)width, (float)renderY + (float)height, 0.0F).m_6122_(moltenMaterial.getRed(), moltenMaterial.getGreen(), moltenMaterial.getBlue(), moltenMaterial.getAlpha()).m_7421_(u, v).m_5752_();
         bufferbuilder.m_85982_(pMatrix, (float)renderX + (float)width, (float)renderY, 0.0F).m_6122_(moltenMaterial.getRed(), moltenMaterial.getGreen(), moltenMaterial.getBlue(), moltenMaterial.getAlpha()).m_7421_(u, 0.0F).m_5752_();
         bufferbuilder.m_85982_(pMatrix, (float)renderX, (float)renderY, 0.0F).m_6122_(moltenMaterial.getRed(), moltenMaterial.getGreen(), moltenMaterial.getBlue(), moltenMaterial.getAlpha()).m_7421_(0.0F, 0.0F).m_5752_();
         BufferUploader.m_231202_(bufferbuilder.m_231175_());
         RenderSystem.m_69461_();
      });
   }

   private void renderFire(PoseStack pPoseStack, int x, int y) {
      if (((KilnMenu)this.f_97732_).hasFuel()) {
         this.m_93228_(pPoseStack, x + 204, y + 90 - ((KilnMenu)this.f_97732_).getScaledFuelProgress(), 0, 181 - ((KilnMenu)this.f_97732_).getScaledFuelProgress(), 13, ((KilnMenu)this.f_97732_).getScaledFuelProgress());
      }

   }

   public void m_6305_(PoseStack pPoseStack, int mouseX, int mouseY, float delta) {
      this.m_7333_(pPoseStack);
      super.m_6305_(pPoseStack, mouseX, mouseY, delta);
      this.m_7025_(pPoseStack, mouseX, mouseY);
   }

   protected void m_7027_(PoseStack pPoseStack, int pMouseX, int pMouseY) {
      this.f_96547_.m_92889_(pPoseStack, this.f_96539_, 80.0F, (float)this.f_97729_ + 1.0F, (new Color(225, 225, 225)).getRGB());
      this.f_96547_.m_92889_(pPoseStack, Component.m_237115_("tensura.kiln.smeltery_label"), 190.0F, (float)this.f_97729_ + 9.0F, (new Color(198, 198, 198)).getRGB());
      this.f_96547_.m_92889_(pPoseStack, this.f_169604_, 64.0F, (float)this.f_97731_ + 2.0F, (new Color(63, 63, 64)).getRGB());
   }

   protected void m_7025_(PoseStack pPoseStack, int pMouseX, int pMouseY) {
      if (((KilnMenu)this.f_97732_).m_142621_().m_41619_() && this.f_97734_ != null && this.f_97734_.m_6657_()) {
         this.m_6057_(pPoseStack, this.f_97734_.m_7993_(), pMouseX, pMouseY);
      }

      String valueText;
      if (this.m_6774_(18, 5, 15, 76, (double)pMouseX, (double)pMouseY)) {
         if (!((KilnMenu)this.f_97732_).blockEntity.getLeftBarId().equals(Optional.of(KilnMixingRecipe.EMPTY)) && ((KilnMenu)this.f_97732_).blockEntity.getMoltenAmount() > 0) {
            valueText = ((KilnMenu)this.f_97732_).blockEntity.getMoltenAmount() + "/144";
            this.leftBarMaterial.ifPresent((moltenMaterial) -> {
               this.renderMaterialTooltip(pPoseStack, pMouseX, pMouseY, moltenMaterial, valueText);
            });
         } else {
            this.renderEmptyMaterialToolTip(pPoseStack, pMouseX, pMouseY);
         }
      }

      if (this.m_6774_(143, 5, 15, 76, (double)pMouseX, (double)pMouseY)) {
         if (!((KilnMenu)this.f_97732_).blockEntity.getRightBarId().equals(Optional.of(KilnMixingRecipe.EMPTY)) && ((KilnMenu)this.f_97732_).blockEntity.getMagicMaterialAmount() > 0) {
            float var10000 = (float)((KilnMenu)this.f_97732_).blockEntity.getMagicMaterialAmount();
            valueText = var10000 / 4.0F + "/36";
            this.rightBarMaterial.ifPresent((moltenMaterial) -> {
               this.renderMaterialTooltip(pPoseStack, pMouseX, pMouseY, moltenMaterial, valueText);
            });
         } else {
            this.renderEmptyMaterialToolTip(pPoseStack, pMouseX, pMouseY);
         }
      }

   }

   private void renderEmptyMaterialToolTip(PoseStack pPoseStack, int pMouseX, int pMouseY) {
      this.m_96602_(pPoseStack, Component.m_237115_("tooltip.tensura.kiln.empty"), pMouseX, pMouseY);
   }

   private void renderMaterialTooltip(PoseStack poseStack, int mouseX, int mouseY, KilnMoltenMaterial material, String valueText) {
      MutableComponent materialNameComponent = Component.m_237115_(String.format("%s.molten.%s.material", material.getMoltenType().m_135827_(), material.getMoltenType().m_135815_()));
      MutableComponent component = Component.m_237110_("tooltip.tensura.kiln.molten_item", new Object[]{valueText, materialNameComponent}).m_130948_(emtpyStyleWithMaterialColor(material));
      this.m_96602_(poseStack, component, mouseX, mouseY);
   }

   private static Style emtpyStyleWithMaterialColor(KilnMoltenMaterial material) {
      return Style.f_131099_.m_178520_((new Color(material.getRed(), material.getGreen(), material.getBlue())).getRGB());
   }

   public boolean m_7933_(int pKeyCode, int pScanCode, int pModifiers) {
      return this.f_96541_ != null && this.f_96541_.f_91066_.f_92092_.isActiveAndMatches(InputConstants.m_84827_(pKeyCode, pScanCode)) ? true : super.m_7933_(pKeyCode, pScanCode, pModifiers);
   }
}
