package com.github.manasmods.tensura.api.magicule;

import com.github.manasmods.tensura.capability.magicule.MagiculeChunkCapability;
import com.github.manasmods.tensura.capability.magicule.MagiculeChunkCapabilityImpl;
import com.google.common.collect.ImmutableList;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import net.minecraft.core.BlockPos;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.chunk.LevelChunk;
import net.minecraftforge.eventbus.api.Cancelable;
import net.minecraftforge.eventbus.api.Event;

public class MagiculeEvent extends Event {
   public final LevelChunk chunk;
   public final Level level;
   protected final MagiculeChunkCapability capability;

   public MagiculeEvent(LevelChunk chunk) {
      this(chunk, MagiculeChunkCapabilityImpl.get(chunk));
   }

   public MagiculeEvent(LevelChunk chunk, MagiculeChunkCapability capability) {
      this.chunk = chunk;
      this.capability = capability;
      this.level = chunk.m_62953_();
   }

   public double getMaxMagicule() {
      return this.capability.getMaxMagicule();
   }

   public double getRegenerationRate() {
      return this.capability.getRegenerationRate();
   }

   public double getMagicule() {
      return this.capability.getMagicule();
   }

   public LevelChunk getChunk() {
      return this.chunk;
   }

   @Cancelable
   public static class Consume extends MagiculeEvent {
      private final BlockPos position;
      private double amount;

      public Consume(LevelChunk chunk, MagiculeChunkCapability capability, BlockPos position, double amount) {
         super(chunk, capability);
         this.position = position;
         this.amount = amount;
      }

      public BlockPos getPosition() {
         return this.position;
      }

      public double getAmount() {
         return this.amount;
      }

      public void setAmount(double amount) {
         this.amount = amount;
      }
   }

   public static class RegisterModifier extends MagiculeEvent {
      private final BlockPos position;
      private final List<MagiculeModifier> modifiers;

      public RegisterModifier(LevelChunk chunk, MagiculeChunkCapability capability, BlockPos position) {
         super(chunk, capability);
         this.position = position;
         this.modifiers = new ArrayList();
      }

      public void addModifier(MagiculeModifier modifier) {
         this.modifiers.add(modifier);
      }

      public List<MagiculeModifier> getModifiers() {
         return ImmutableList.sortedCopyOf(Comparator.reverseOrder(), this.modifiers);
      }

      public BlockPos getPosition() {
         return this.position;
      }
   }

   @Cancelable
   public static class Regeneration extends MagiculeEvent {
      private double amount = this.getRegenerationRate() / 20.0D;

      public Regeneration(LevelChunk chunk, MagiculeChunkCapability capability) {
         super(chunk, capability);
      }

      public double getNewMagicule() {
         return Math.min(this.getMagicule() + this.amount, this.getMaxMagicule());
      }

      public double getAmount() {
         return this.amount;
      }

      public void setAmount(double amount) {
         this.amount = amount;
      }
   }

   public static class Initialization extends MagiculeEvent {
      private double newMaxMagicule = this.getMaxMagicule();
      private double newRegenerationRate = this.getRegenerationRate();

      public Initialization(LevelChunk chunk, MagiculeChunkCapability capability) {
         super(chunk, capability);
      }

      public double getNewMaxMagicule() {
         return this.newMaxMagicule;
      }

      public void setNewMaxMagicule(double newMaxMagicule) {
         this.newMaxMagicule = newMaxMagicule;
      }

      public double getNewRegenerationRate() {
         return this.newRegenerationRate;
      }

      public void setNewRegenerationRate(double newRegenerationRate) {
         this.newRegenerationRate = newRegenerationRate;
      }
   }
}
