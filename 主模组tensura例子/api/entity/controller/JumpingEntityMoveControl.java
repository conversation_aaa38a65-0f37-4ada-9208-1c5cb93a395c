package com.github.manasmods.tensura.api.entity.controller;

import com.github.manasmods.tensura.api.entity.subclass.IJumpingEntity;
import net.minecraft.world.entity.PathfinderMob;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.control.MoveControl;
import net.minecraft.world.entity.ai.control.MoveControl.Operation;

public class JumpingEntityMoveControl extends MoveControl {
   public final PathfinderMob mob;
   public int jumpDelay;
   public final float walkingSpeed;
   public final float jumpingSpeedMultiplier;

   public JumpingEntityMoveControl(PathfinderMob entity, float walkingSpeed, float jumpSpeed) {
      super(entity);
      this.mob = entity;
      this.walkingSpeed = walkingSpeed;
      this.jumpingSpeedMultiplier = jumpSpeed;
   }

   public JumpingEntityMoveControl(PathfinderMob entity, float walkingSpeed) {
      this(entity, walkingSpeed, 0.1F);
   }

   public JumpingEntityMoveControl(PathfinderMob entity) {
      this(entity, 0.0F);
   }

   public void setSpeed(double speedIn) {
      this.f_24978_ = speedIn;
      this.f_24981_ = Operation.MOVE_TO;
   }

   public void m_8126_() {
      PathfinderMob var2 = this.mob;
      if (var2 instanceof IJumpingEntity) {
         IJumpingEntity jumpingEntity = (IJumpingEntity)var2;
         if (this.mob.m_20096_()) {
            this.mob.m_7910_((float)(this.f_24978_ * (double)this.jumpingSpeedMultiplier));
            if (this.jumpDelay-- <= 0 && this.f_24981_ != Operation.WAIT && !this.mob.m_20160_()) {
               this.mob.m_7910_((float)(this.f_24978_ * this.mob.m_21133_(Attributes.f_22279_)));
               jumpingEntity.setJumpAnimation(Boolean.TRUE);
               this.setJumpDelay(jumpingEntity);
               this.mob.m_21569_().m_24901_();
               this.mob.m_5496_(jumpingEntity.getJumpSound(), jumpingEntity.getJumpSoundVolume(), this.mob.m_6100_());
            } else {
               this.mob.f_20900_ = 0.0F;
               this.mob.f_20902_ = 0.0F;
               this.mob.m_7910_(this.walkingSpeed);
            }
         }

         super.m_8126_();
      }
   }

   public void setJumpDelay(IJumpingEntity jumpingEntity) {
      this.jumpDelay = jumpingEntity.getJumpDelay();
      if (this.mob.m_5448_() != null) {
         this.jumpDelay /= 2;
      }
   }
}
