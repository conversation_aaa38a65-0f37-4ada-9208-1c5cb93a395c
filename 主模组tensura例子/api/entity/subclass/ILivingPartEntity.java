package com.github.manasmods.tensura.api.entity.subclass;

import java.util.UUID;
import net.minecraft.util.Mth;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.phys.Vec3;

public interface ILivingPartEntity {
   default UUID getHeadId() {
      return null;
   }

   default Entity getHead() {
      return null;
   }

   void onServerHurt(LivingEntity var1);

   default Vec3 getOffsetVec(float offsetZ, float xRot, float yRot) {
      return (new Vec3(0.0D, 0.0D, (double)offsetZ)).m_82496_(xRot * 0.017453292F).m_82524_(-yRot * 0.017453292F);
   }

   default float getLimitAngle(float sourceAngle, float targetAngle, float maximumChange) {
      float f = Mth.m_14177_(targetAngle - sourceAngle);
      if (f > maximumChange) {
         f = maximumChange;
      }

      if (f < -maximumChange) {
         f = -maximumChange;
      }

      float f1 = sourceAngle + f;
      if (f1 < 0.0F) {
         f1 += 360.0F;
      } else if (f1 > 360.0F) {
         f1 -= 360.0F;
      }

      return f1;
   }

   static LivingEntity checkForHead(LivingEntity entity) {
      if (!entity.m_9236_().m_5776_() && entity instanceof ILivingPartEntity) {
         ILivingPartEntity part = (ILivingPartEntity)entity;
         Entity var3 = part.getHead();
         if (var3 instanceof LivingEntity) {
            LivingEntity head = (LivingEntity)var3;
            return head;
         }
      }

      return entity;
   }
}
