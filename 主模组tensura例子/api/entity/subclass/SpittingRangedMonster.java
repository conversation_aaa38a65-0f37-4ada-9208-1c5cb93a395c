package com.github.manasmods.tensura.api.entity.subclass;

import com.github.manasmods.tensura.entity.projectile.MonsterSpitProjectile;
import net.minecraft.core.BlockPos;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.util.Mth;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.monster.RangedAttackMob;
import org.jetbrains.annotations.NotNull;

public interface SpittingRangedMonster extends RangedAttackMob {
   void spitHit(LivingEntity var1);

   default void performRangedAttack(BlockPos target, double xzOffset, double yOffset) {
      if (this instanceof LivingEntity) {
         LivingEntity living = (LivingEntity)this;
         MonsterSpitProjectile spit = new MonsterSpitProjectile(living.f_19853_, living);
         float angle = 0.017453292F * living.f_20883_;
         double xOffset = xzOffset * (double)Mth.m_14031_((float)(3.141592653589793D + (double)angle));
         double zOffset = xzOffset * (double)Mth.m_14089_(angle);
         spit.m_7678_(living.m_20185_() + xOffset, living.m_20186_() + yOffset, living.m_20189_() + zOffset, living.m_146908_(), living.m_146909_());
         double d0 = (double)((float)target.m_123342_() + 0.5F);
         double d1 = (double)target.m_123341_() - living.m_20185_();
         double d2 = d0 - spit.m_20186_();
         double d3 = (double)target.m_123343_() - living.m_20189_();
         double f = Math.sqrt(d1 * d1 + d3 * d3) * 0.20000000298023224D;
         spit.m_6686_(d1, d2 + f, d3, 1.2F, 0.0F);
         living.f_19853_.m_7967_(spit);
      }
   }

   default void performRangedAttack(@NotNull LivingEntity target, double xzOffset, double yOffset) {
      if (this instanceof LivingEntity) {
         LivingEntity living = (LivingEntity)this;
         MonsterSpitProjectile spit = new MonsterSpitProjectile(living.f_19853_, living);
         float angle = 0.017453292F * living.f_20883_;
         double xOffset = xzOffset * (double)Mth.m_14031_((float)(3.141592653589793D + (double)angle));
         double zOffset = xzOffset * (double)Mth.m_14089_(angle);
         spit.m_7678_(living.m_20185_() + xOffset, living.m_20186_() + yOffset, living.m_20189_() + zOffset, living.m_146908_(), living.m_146909_());
         double d0 = target.m_20186_() + (double)(target.m_20206_() / 2.0F);
         double d1 = target.m_20185_() - living.m_20185_();
         double d2 = d0 - spit.m_20186_();
         double d3 = target.m_20189_() - living.m_20189_();
         double f = Math.sqrt(d1 * d1 + d3 * d3) * 0.20000000298023224D;
         spit.m_6686_(d1, d2 + f, d3, 1.2F, 0.0F);
         living.f_19853_.m_7967_(spit);
      }
   }

   default void m_6504_(@NotNull LivingEntity target, float pDistanceFactor) {
      if (this instanceof LivingEntity) {
         LivingEntity living = (LivingEntity)this;
         MonsterSpitProjectile spit = new MonsterSpitProjectile(living.f_19853_, living);
         spit.m_7678_(living.m_20185_(), living.m_20186_() + 1.0D, living.m_20189_(), living.m_146908_(), living.m_146909_());
         double d0 = target.m_20186_() + (double)(target.m_20206_() / 2.0F);
         double d1 = target.m_20185_() - living.m_20185_();
         double d2 = d0 - spit.m_20186_();
         double d3 = target.m_20189_() - living.m_20189_();
         double f = Math.sqrt(d1 * d1 + d3 * d3) * 0.20000000298023224D;
         spit.m_6686_(d1, d2 + f, d3, 1.2F, 0.0F);
         living.f_19853_.m_7967_(spit);
      }
   }

   default void particleSpawning(MonsterSpitProjectile projectile, ParticleOptions particleOptions, int particleAmount) {
      float radius = projectile.m_20205_();
      double x = projectile.m_20185_() + (projectile.f_19853_.f_46441_.m_188500_() - 0.5D) * (double)radius;
      double y = projectile.m_20186_() + (projectile.f_19853_.f_46441_.m_188500_() - 0.5D) * (double)radius;
      double z = projectile.m_20189_() + (projectile.f_19853_.f_46441_.m_188500_() - 0.5D) * (double)radius;

      for(int j = 0; j < particleAmount; ++j) {
         double newX = x + projectile.getRandom().m_188583_() / 4.0D;
         double newY = y + projectile.getRandom().m_188583_() / 4.0D;
         double newZ = z + projectile.getRandom().m_188583_() / 4.0D;
         projectile.f_19853_.m_7106_(particleOptions, newX, newY, newZ, 0.0D, -0.1D, 0.0D);
      }

   }

   default void spitParticle(MonsterSpitProjectile projectile) {
   }

   default void impactEffect(MonsterSpitProjectile spit, double x, double y, double z) {
   }
}
