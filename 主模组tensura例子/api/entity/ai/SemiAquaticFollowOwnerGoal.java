package com.github.manasmods.tensura.api.entity.ai;

import com.github.manasmods.tensura.api.entity.subclass.IFollower;
import com.github.manasmods.tensura.entity.LandfishEntity;
import com.github.manasmods.tensura.entity.template.TensuraTamableEntity;
import java.util.EnumSet;
import net.minecraft.core.BlockPos;
import net.minecraft.tags.FluidTags;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.TamableAnimal;
import net.minecraft.world.entity.ai.goal.Goal;
import net.minecraft.world.entity.ai.goal.Goal.Flag;
import net.minecraft.world.level.LevelReader;
import net.minecraft.world.level.block.LeavesBlock;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.level.pathfinder.BlockPathTypes;
import net.minecraft.world.level.pathfinder.WalkNodeEvaluator;

public class SemiAquaticFollowOwnerGoal extends Goal {
   private final TamableAnimal tameable;
   private final LevelReader world;
   private final double followSpeed;
   private final float maxDist;
   private final float minDist;
   private final boolean teleportToLeaves;
   private final boolean avoidLand;
   private LivingEntity owner;
   private int timeToRecalcPath;
   private float oldWaterCost;

   public SemiAquaticFollowOwnerGoal(TamableAnimal tamed, double speed, float minDist, float maxDist, boolean leaves, boolean avoidLand) {
      this.tameable = tamed;
      this.world = tamed.f_19853_;
      this.followSpeed = speed;
      this.minDist = minDist;
      this.maxDist = maxDist;
      this.teleportToLeaves = leaves;
      this.avoidLand = avoidLand;
      this.m_7021_(EnumSet.of(Flag.MOVE, Flag.LOOK));
   }

   public boolean m_8036_() {
      LivingEntity owner = this.tameable.m_21826_();
      if (owner == null) {
         return false;
      } else if (owner.m_5833_()) {
         return false;
      } else if (((IFollower)this.tameable).shouldFollow() && !this.isInCombat()) {
         if (this.tameable.m_20280_(owner) < (double)(this.minDist * this.minDist)) {
            return false;
         } else {
            TamableAnimal var3 = this.tameable;
            if (var3 instanceof TensuraTamableEntity) {
               TensuraTamableEntity tensuraTamable = (TensuraTamableEntity)var3;
               if (tensuraTamable.isWandering()) {
                  return false;
               }
            }

            if (this.tameable.m_5448_() != null && this.tameable.m_5448_().m_6084_()) {
               return false;
            } else {
               this.owner = owner;
               return true;
            }
         }
      } else {
         return false;
      }
   }

   public boolean m_8045_() {
      if (!this.tameable.m_21573_().m_26571_() && !this.isInCombat()) {
         if (!((IFollower)this.tameable).shouldFollow()) {
            return false;
         } else {
            TamableAnimal var2 = this.tameable;
            if (var2 instanceof TensuraTamableEntity) {
               TensuraTamableEntity tensuraTamable = (TensuraTamableEntity)var2;
               if (tensuraTamable.isWandering()) {
                  return false;
               }
            }

            if (this.tameable.m_5448_() != null && this.tameable.m_5448_().m_6084_()) {
               return false;
            } else {
               return this.tameable.m_20280_(this.owner) > (double)(this.maxDist * this.maxDist);
            }
         }
      } else {
         return false;
      }
   }

   private boolean isInCombat() {
      Entity owner = this.tameable.m_21826_();
      if (owner == null) {
         return false;
      } else {
         return this.tameable.m_20270_(owner) < 30.0F && this.tameable.m_5448_() != null && this.tameable.m_5448_().m_6084_();
      }
   }

   public void m_8056_() {
      this.timeToRecalcPath = 0;
      this.oldWaterCost = this.tameable.m_21439_(BlockPathTypes.WATER);
      this.tameable.m_21441_(BlockPathTypes.WATER, 0.0F);
   }

   public void m_8041_() {
      this.owner = null;
      this.tameable.m_21573_().m_26573_();
      this.tameable.m_21441_(BlockPathTypes.WATER, this.oldWaterCost);
   }

   public void m_8037_() {
      this.tameable.m_21563_().m_24960_(this.owner, 10.0F, (float)this.tameable.m_8132_());
      if (--this.timeToRecalcPath <= 0) {
         this.timeToRecalcPath = 10;
         if (!this.tameable.m_21523_() && !this.tameable.m_20159_()) {
            if (this.tameable.m_20280_(this.owner) >= 144.0D) {
               this.tryToTeleportNearEntity();
            } else {
               this.tameable.m_21573_().m_5624_(this.owner, this.followSpeed);
            }

         }
      }
   }

   private void tryToTeleportNearEntity() {
      BlockPos pos = this.owner.m_20183_();

      for(int i = 0; i < 10; ++i) {
         int xRan = this.getRandomNumber(-3, 3);
         int yRan = this.getRandomNumber(-1, 1);
         int zRan = this.getRandomNumber(-3, 3);
         boolean teleport = this.tryToTeleportToLocation(pos.m_123341_() + xRan, pos.m_123342_() + yRan, pos.m_123343_() + zRan);
         if (teleport) {
            return;
         }
      }

   }

   private boolean tryToTeleportToLocation(int x, int y, int z) {
      if (Math.abs((double)x - this.owner.m_20185_()) < 2.0D && Math.abs((double)z - this.owner.m_20189_()) < 2.0D) {
         return false;
      } else if (!this.isTeleportFriendlyBlock(new BlockPos(x, y, z))) {
         return false;
      } else {
         this.tameable.m_7678_((double)x + 0.5D, (double)y, (double)z + 0.5D, this.tameable.m_146908_(), this.tameable.m_146909_());
         this.tameable.m_21573_().m_26573_();
         return true;
      }
   }

   private boolean isTeleportFriendlyBlock(BlockPos pos) {
      BlockPathTypes blockPathType = WalkNodeEvaluator.m_77604_(this.world, pos.m_122032_());
      if (!this.world.m_6425_(pos).m_205070_(FluidTags.f_13131_) && (this.world.m_6425_(pos).m_205070_(FluidTags.f_13131_) || !this.world.m_6425_(pos.m_7495_()).m_205070_(FluidTags.f_13131_))) {
         if (blockPathType == BlockPathTypes.WALKABLE && !this.avoidsLand()) {
            BlockState state = this.world.m_8055_(pos.m_7495_());
            if (!this.teleportToLeaves && state.m_60734_() instanceof LeavesBlock) {
               return false;
            } else {
               BlockPos blockPos = pos.m_121996_(this.tameable.m_20183_());
               return this.world.m_45756_(this.tameable, this.tameable.m_20191_().m_82338_(blockPos));
            }
         } else {
            return false;
         }
      } else {
         return true;
      }
   }

   public boolean avoidsLand() {
      TamableAnimal var2 = this.tameable;
      if (var2 instanceof LandfishEntity) {
         LandfishEntity landfishEntity = (LandfishEntity)var2;
         return landfishEntity.getMoistnessLevel() < 300;
      } else {
         return this.avoidLand;
      }
   }

   private int getRandomNumber(int i, int in) {
      return this.tameable.m_217043_().m_188503_(in - i + 1) + i;
   }
}
