package com.github.manasmods.tensura.api.entity.ai;

import java.util.EnumSet;
import net.minecraft.util.TimeUtil;
import net.minecraft.util.valueproviders.UniformInt;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.ai.goal.Goal;
import net.minecraft.world.entity.ai.goal.Goal.Flag;
import net.minecraft.world.entity.monster.CrossbowAttackMob;
import net.minecraft.world.entity.monster.RangedAttackMob;
import net.minecraft.world.entity.projectile.ProjectileUtil;
import net.minecraft.world.item.CrossbowItem;
import net.minecraft.world.item.ItemStack;

public class CrossbowAttackGoal<T extends Mob & RangedAttackMob & CrossbowAttackMob> extends Goal {
   public static final UniformInt PATHFINDING_DELAY_RANGE = TimeUtil.m_145020_(1, 2);
   private final T mob;
   private CrossbowAttackGoal.CrossbowState crossbowState;
   private final double speedModifier;
   private final float attackRadiusSqr;
   private int seeTime;
   private int attackDelay;
   private int updatePathDelay;

   public CrossbowAttackGoal(T pMob, double pSpeedModifier, float pAttackRadius) {
      this.crossbowState = CrossbowAttackGoal.CrossbowState.UNCHARGED;
      this.mob = pMob;
      this.speedModifier = pSpeedModifier;
      this.attackRadiusSqr = pAttackRadius * pAttackRadius;
      this.m_7021_(EnumSet.of(Flag.MOVE, Flag.LOOK));
   }

   public boolean m_8036_() {
      return this.isValidTarget() && this.isHoldingCrossbow();
   }

   private boolean isHoldingCrossbow() {
      return this.mob.m_21093_((is) -> {
         return is.m_41720_() instanceof CrossbowItem;
      });
   }

   public boolean m_8045_() {
      return this.isValidTarget() && (this.m_8036_() || !this.mob.m_21573_().m_26571_()) && this.isHoldingCrossbow();
   }

   private boolean isValidTarget() {
      return this.mob.m_5448_() != null && this.mob.m_5448_().m_6084_();
   }

   public void m_8041_() {
      super.m_8041_();
      this.mob.m_21561_(false);
      this.mob.m_6710_((LivingEntity)null);
      this.seeTime = 0;
      if (this.mob.m_6117_()) {
         this.mob.m_5810_();
         ((CrossbowAttackMob)this.mob).m_6136_(false);
         CrossbowItem.m_40884_(this.mob.m_21211_(), false);
      }

   }

   public boolean m_183429_() {
      return true;
   }

   public void m_8037_() {
      LivingEntity livingentity = this.mob.m_5448_();
      if (livingentity != null) {
         boolean flag = this.mob.m_21574_().m_148306_(livingentity);
         boolean flag1 = this.seeTime > 0;
         if (flag != flag1) {
            this.seeTime = 0;
         }

         if (flag) {
            ++this.seeTime;
         } else {
            --this.seeTime;
         }

         double d0 = this.mob.m_20280_(livingentity);
         boolean flag2 = (d0 > (double)this.attackRadiusSqr || this.seeTime < 5) && this.attackDelay == 0;
         if (flag2) {
            --this.updatePathDelay;
            if (this.updatePathDelay <= 0) {
               this.mob.m_21573_().m_5624_(livingentity, this.canRun() ? this.speedModifier : this.speedModifier * 0.5D);
               this.updatePathDelay = PATHFINDING_DELAY_RANGE.m_214085_(this.mob.m_217043_());
            }
         } else {
            this.updatePathDelay = 0;
            this.mob.m_21573_().m_26573_();
         }

         this.mob.m_21563_().m_24960_(livingentity, 30.0F, 30.0F);
         if (this.crossbowState == CrossbowAttackGoal.CrossbowState.UNCHARGED) {
            if (!flag2) {
               this.mob.m_6672_(ProjectileUtil.getWeaponHoldingHand(this.mob, (item) -> {
                  return item instanceof CrossbowItem;
               }));
               this.crossbowState = CrossbowAttackGoal.CrossbowState.CHARGING;
               ((CrossbowAttackMob)this.mob).m_6136_(true);
            }
         } else if (this.crossbowState == CrossbowAttackGoal.CrossbowState.CHARGING) {
            if (!this.mob.m_6117_()) {
               this.crossbowState = CrossbowAttackGoal.CrossbowState.UNCHARGED;
            }

            int i = this.mob.m_21252_();
            ItemStack itemstack = this.mob.m_21211_();
            if (i >= CrossbowItem.m_40939_(itemstack)) {
               this.mob.m_21253_();
               this.crossbowState = CrossbowAttackGoal.CrossbowState.CHARGED;
               this.attackDelay = 20 + this.mob.m_217043_().m_188503_(20);
               ((CrossbowAttackMob)this.mob).m_6136_(false);
            }
         } else if (this.crossbowState == CrossbowAttackGoal.CrossbowState.CHARGED) {
            --this.attackDelay;
            if (this.attackDelay == 0) {
               this.crossbowState = CrossbowAttackGoal.CrossbowState.READY_TO_ATTACK;
            }
         } else if (this.crossbowState == CrossbowAttackGoal.CrossbowState.READY_TO_ATTACK && flag) {
            ((RangedAttackMob)this.mob).m_6504_(livingentity, 1.0F);
            ItemStack weapon = this.mob.m_21120_(ProjectileUtil.getWeaponHoldingHand(this.mob, (item) -> {
               return item instanceof CrossbowItem;
            }));
            CrossbowItem.m_40884_(weapon, false);
            this.crossbowState = CrossbowAttackGoal.CrossbowState.UNCHARGED;
         }
      }

   }

   private boolean canRun() {
      return this.crossbowState == CrossbowAttackGoal.CrossbowState.UNCHARGED;
   }

   static enum CrossbowState {
      UNCHARGED,
      CHARGING,
      CHARGED,
      READY_TO_ATTACK;

      // $FF: synthetic method
      private static CrossbowAttackGoal.CrossbowState[] $values() {
         return new CrossbowAttackGoal.CrossbowState[]{UNCHARGED, CHARGING, CHARGED, READY_TO_ATTACK};
      }
   }
}
