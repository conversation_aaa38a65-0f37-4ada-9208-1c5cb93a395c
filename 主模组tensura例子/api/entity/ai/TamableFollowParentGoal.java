package com.github.manasmods.tensura.api.entity.ai;

import com.github.manasmods.tensura.entity.template.TensuraHorseEntity;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import javax.annotation.Nullable;
import net.minecraft.world.entity.OwnableEntity;
import net.minecraft.world.entity.TamableAnimal;
import net.minecraft.world.entity.ai.goal.Goal;
import net.minecraft.world.entity.animal.Animal;

public class TamableFollowParentGoal extends Goal {
   private final Animal animal;
   @Nullable
   private Animal parent;
   private final double speedModifier;
   private int timeToRecalcPath;

   public TamableFollowParentGoal(Animal pAnimal, double pSpeedModifier) {
      this.animal = pAnimal;
      this.speedModifier = pSpeedModifier;
   }

   public boolean m_8036_() {
      if (this.animal.m_146764_() >= 0) {
         return false;
      } else if (!this.animal.m_21523_() && !this.animal.m_20159_()) {
         Animal parents = this.animal;
         if (parents instanceof TamableAnimal) {
            TamableAnimal tamableAnimal = (TamableAnimal)parents;
            if (tamableAnimal.m_21827_()) {
               return false;
            }
         }

         parents = this.animal;
         if (parents instanceof TensuraHorseEntity) {
            TensuraHorseEntity horse = (TensuraHorseEntity)parents;
            if (horse.isSitting()) {
               return false;
            }
         }

         List<? extends Animal> list = this.animal.f_19853_.m_45976_(this.animal.getClass(), this.animal.m_20191_().m_82377_(8.0D, 4.0D, 8.0D));
         parents = null;
         double d0 = Double.MAX_VALUE;
         Iterator var5 = list.iterator();

         while(var5.hasNext()) {
            Animal parent;
            boolean var10000;
            label51: {
               parent = (Animal)var5.next();
               Animal var10 = this.animal;
               if (var10 instanceof OwnableEntity) {
                  OwnableEntity tamable = (OwnableEntity)var10;
                  if (parent instanceof OwnableEntity) {
                     OwnableEntity tamableParent = (OwnableEntity)parent;
                     if (Objects.equals(tamable.m_21826_(), tamableParent.m_21826_())) {
                        var10000 = true;
                        break label51;
                     }
                  }
               }

               var10000 = false;
            }

            boolean tamableFlag = var10000;
            if (parent.m_146764_() >= 0 && tamableFlag) {
               double distance = this.animal.m_20280_(parent);
               if (!(distance > d0)) {
                  d0 = distance;
                  parents = parent;
               }
            }
         }

         if (parents == null) {
            return false;
         } else if (d0 < 9.0D) {
            return false;
         } else {
            this.parent = parents;
            return true;
         }
      } else {
         return false;
      }
   }

   public boolean m_8045_() {
      if (this.animal.m_146764_() >= 0) {
         return false;
      } else if (!this.animal.m_21523_() && !this.animal.m_20159_()) {
         Animal var2 = this.animal;
         if (var2 instanceof TamableAnimal) {
            TamableAnimal tamableAnimal = (TamableAnimal)var2;
            if (tamableAnimal.m_21827_()) {
               return false;
            }
         }

         var2 = this.animal;
         if (var2 instanceof TensuraHorseEntity) {
            TensuraHorseEntity horse = (TensuraHorseEntity)var2;
            if (horse.isSitting()) {
               return false;
            }
         }

         if (this.parent == null) {
            return false;
         } else if (!this.parent.m_6084_()) {
            return false;
         } else {
            double d0 = this.animal.m_20280_(this.parent);
            return !(d0 < 9.0D) && !(d0 > 256.0D);
         }
      } else {
         return false;
      }
   }

   public void m_8056_() {
      this.timeToRecalcPath = 0;
   }

   public void m_8041_() {
      this.parent = null;
   }

   public void m_8037_() {
      if (--this.timeToRecalcPath <= 0) {
         if (this.parent != null) {
            this.timeToRecalcPath = this.m_183277_(10);
            this.animal.m_21573_().m_5624_(this.parent, this.speedModifier);
         }
      }
   }
}
