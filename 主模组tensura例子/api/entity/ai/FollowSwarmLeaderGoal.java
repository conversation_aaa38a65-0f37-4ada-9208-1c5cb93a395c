package com.github.manasmods.tensura.api.entity.ai;

import net.minecraft.world.entity.ai.goal.Goal;
import net.minecraft.world.entity.ai.targeting.TargetingConditions;
import net.minecraft.world.entity.animal.AbstractSchoolingFish;
import org.jetbrains.annotations.Nullable;

public class FollowSwarmLeaderGoal extends Goal {
   private static final int INTERVAL_TICKS = 200;
   private final AbstractSchoolingFish mob;
   private int timeToRecalcPath;
   private int nextStartTick;
   private final Class<? extends AbstractSchoolingFish> leaderEntityType;

   public FollowSwarmLeaderGoal(AbstractSchoolingFish self, Class<? extends AbstractSchoolingFish> leaderEntityType) {
      this.mob = self;
      this.nextStartTick = this.nextStartTick(self);
      this.leaderEntityType = leaderEntityType;
   }

   public boolean m_8036_() {
      if (this.mob.m_27540_()) {
         return true;
      } else if (this.nextStartTick-- > 0) {
         return false;
      } else {
         this.nextStartTick = this.nextStartTick(this.mob);
         AbstractSchoolingFish leaderEntity = this.nearestLeader();
         if (leaderEntity == null) {
            return false;
         } else {
            this.mob.m_27525_(leaderEntity);
            return this.mob.m_27540_();
         }
      }
   }

   @Nullable
   private AbstractSchoolingFish nearestLeader() {
      return (AbstractSchoolingFish)this.mob.f_19853_.m_45963_(this.leaderEntityType, TargetingConditions.m_148353_(), this.mob, this.mob.m_20185_(), this.mob.m_20186_(), this.mob.m_20189_(), this.mob.m_20191_().m_82377_(60.0D, 60.0D, 60.0D));
   }

   public boolean m_8045_() {
      return this.mob.m_27540_() && this.mob.m_27544_();
   }

   public void m_8056_() {
      this.timeToRecalcPath = 0;
   }

   public void m_8041_() {
      if (this.mob.m_27540_()) {
         this.mob.m_27541_();
      }

   }

   public void m_8037_() {
      if (--this.timeToRecalcPath <= 0) {
         this.timeToRecalcPath = this.m_183277_(10);
         this.mob.m_27545_();
      }
   }

   protected int nextStartTick(AbstractSchoolingFish pTaskOwner) {
      return m_186073_(200 + pTaskOwner.m_217043_().m_188503_(200) % 20);
   }
}
