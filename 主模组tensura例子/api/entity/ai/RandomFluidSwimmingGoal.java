package com.github.manasmods.tensura.api.entity.ai;

import com.github.manasmods.tensura.config.TensuraConfig;
import com.github.manasmods.tensura.entity.template.TensuraHorseEntity;
import com.github.manasmods.tensura.entity.template.TensuraTamableEntity;
import java.util.function.Predicate;
import javax.annotation.Nullable;
import net.minecraft.core.BlockPos;
import net.minecraft.world.entity.PathfinderMob;
import net.minecraft.world.entity.ai.goal.RandomStrollGoal;
import net.minecraft.world.entity.ai.util.DefaultRandomPos;
import net.minecraft.world.level.material.FluidState;
import net.minecraft.world.level.pathfinder.PathComputationType;
import net.minecraft.world.phys.Vec3;

public class RandomFluidSwimmingGoal extends RandomStrollGoal {
   private Predicate<FluidState> predicate;

   public RandomFluidSwimmingGoal(PathfinderMob creature, double speed, int chance, Predicate<FluidState> predicate) {
      super(creature, speed, chance, false);
      this.predicate = predicate;
   }

   public boolean m_8036_() {
      PathfinderMob var2 = this.f_25725_;
      if (var2 instanceof TensuraTamableEntity) {
         TensuraTamableEntity tamable = (TensuraTamableEntity)var2;
         if (tamable.shouldFollowOwner() || tamable.m_21827_()) {
            return false;
         }
      }

      var2 = this.f_25725_;
      if (var2 instanceof TensuraHorseEntity) {
         TensuraHorseEntity horse = (TensuraHorseEntity)var2;
         if (!horse.isWandering() || horse.isSitting()) {
            return false;
         }
      }

      if (!this.f_25725_.m_20160_() && this.f_25725_.m_5448_() == null) {
         if (!this.f_25731_) {
            int i = !this.f_25725_.m_20077_() && !this.f_25725_.m_20069_() ? this.f_25730_ * 2 : this.f_25730_;
            if (this.f_25725_.m_217043_().m_188503_(i) != 0) {
               return false;
            }
         }

         Vec3 vector3d = this.m_7037_();
         if (vector3d == null) {
            return false;
         } else {
            this.f_25726_ = vector3d.f_82479_;
            this.f_25727_ = vector3d.f_82480_;
            this.f_25728_ = vector3d.f_82481_;
            this.f_25731_ = false;
            return true;
         }
      } else {
         return false;
      }
   }

   @Nullable
   protected Vec3 m_7037_() {
      PathfinderMob var2 = this.f_25725_;
      if (var2 instanceof TensuraTamableEntity) {
         TensuraTamableEntity entity = (TensuraTamableEntity)var2;
         Vec3 pos = Vec3.m_82512_(entity.getWanderPos());
         double distance = (Double)TensuraConfig.INSTANCE.entitiesConfig.tamedWanderRadius.get();
         if (entity.isWandering() && entity.m_20238_(pos) >= distance * distance) {
            return pos;
         }
      }

      Vec3 vector3d;
      if (this.f_25725_.m_217043_().m_188501_() < (this.f_25725_.m_20077_() ? 0.7F : 0.3F)) {
         vector3d = this.findSurfaceTarget(this.f_25725_, 32, 16);
         if (vector3d != null) {
            return vector3d;
         }
      }

      vector3d = DefaultRandomPos.m_148403_(this.f_25725_, 32, 16);

      for(int var7 = 0; vector3d != null && !this.f_25725_.f_19853_.m_8055_(new BlockPos(vector3d)).m_60647_(this.f_25725_.f_19853_, new BlockPos(vector3d), PathComputationType.WATER) && var7++ < 10; vector3d = DefaultRandomPos.m_148403_(this.f_25725_, 10, 7)) {
      }

      return vector3d;
   }

   private boolean canJumpTo(BlockPos pos, int dx, int dz, int scale) {
      BlockPos blockpos = pos.m_7918_(dx * scale, 0, dz * scale);
      return this.predicate.test(this.f_25725_.f_19853_.m_6425_(blockpos)) && !this.f_25725_.f_19853_.m_8055_(blockpos).m_60767_().m_76334_();
   }

   private boolean isAirAbove(BlockPos pos, int dx, int dz, int scale) {
      return this.f_25725_.f_19853_.m_8055_(pos.m_7918_(dx * scale, 1, dz * scale)).m_60795_() && this.f_25725_.f_19853_.m_8055_(pos.m_7918_(dx * scale, 2, dz * scale)).m_60795_();
   }

   protected Vec3 findSurfaceTarget(PathfinderMob creature, int i, int i1) {
      BlockPos upPos;
      for(upPos = creature.m_20183_(); this.predicate.test(creature.f_19853_.m_6425_(upPos)); upPos = upPos.m_7494_()) {
      }

      return this.isAirAbove(upPos.m_7495_(), 0, 0, 0) && this.canJumpTo(upPos.m_7495_(), 0, 0, 0) ? new Vec3((double)((float)upPos.m_123341_() + 0.5F), (double)((float)upPos.m_123342_() - 0.5F), (double)((float)upPos.m_123343_() + 0.5F)) : null;
   }
}
