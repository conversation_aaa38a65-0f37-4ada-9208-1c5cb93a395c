package com.github.manasmods.tensura.race.ogre;

import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.tensura.ability.TensuraSkill;
import com.github.manasmods.tensura.ability.magic.MagicElemental;
import com.github.manasmods.tensura.capability.skill.TensuraSkillCapability;
import com.github.manasmods.tensura.race.Race;
import com.github.manasmods.tensura.registry.race.TensuraRaces;
import com.github.manasmods.tensura.registry.skill.IntrinsicSkills;
import com.mojang.datafixers.util.Pair;
import java.util.ArrayList;
import java.util.List;
import net.minecraft.network.chat.Component;
import net.minecraft.util.RandomSource;
import net.minecraft.world.entity.player.Player;
import org.jetbrains.annotations.Nullable;

public class KijinRace extends OgreRace {
   public KijinRace() {
      super(Race.Difficulty.EASY);
   }

   public double getBaseHealth() {
      return 30.0D;
   }

   public double getBaseAttackDamage() {
      return 4.2D;
   }

   public double getSprintSpeed() {
      return 0.2D;
   }

   public Pair<Double, Double> getBaseAuraRange() {
      return Pair.of(5000.0D, 5000.0D);
   }

   public Pair<Double, Double> getBaseMagiculeRange() {
      return Pair.of(5000.0D, 5000.0D);
   }

   public double getAdditionalSpiritualHealth() {
      return 20.0D;
   }

   public double getSpiritualHealthMultiplier() {
      return 2.5D;
   }

   @Nullable
   public Race getDefaultEvolution(Player player) {
      return (Race)TensuraRaces.MYSTIC_ONI.get();
   }

   @Nullable
   public Race getHarvestFestivalEvolution(Player player) {
      return (Race)TensuraRaces.MYSTIC_ONI.get();
   }

   public List<TensuraSkill> getIntrinsicSkills(Player player) {
      List<TensuraSkill> list = super.getIntrinsicSkills(player);
      switch(RandomSource.m_216327_().m_188503_(7)) {
      case 0:
         list.add((TensuraSkill)IntrinsicSkills.DARKNESS_TRANSFORM.get());
         break;
      case 1:
         list.add((TensuraSkill)IntrinsicSkills.EARTH_TRANSFORM.get());
         break;
      case 2:
         list.add((TensuraSkill)IntrinsicSkills.FLAME_TRANSFORM.get());
         break;
      case 3:
         list.add((TensuraSkill)IntrinsicSkills.LIGHT_TRANSFORM.get());
         break;
      case 4:
         list.add((TensuraSkill)IntrinsicSkills.SPACE_TRANSFORM.get());
         break;
      case 5:
         list.add((TensuraSkill)IntrinsicSkills.WATER_TRANSFORM.get());
         break;
      case 6:
         list.add((TensuraSkill)IntrinsicSkills.WIND_TRANSFORM.get());
      }

      return list;
   }

   public boolean isIntrinsicSkill(Player player, ManasSkill skill) {
      if (skill == IntrinsicSkills.DARKNESS_TRANSFORM.get()) {
         return true;
      } else if (skill == IntrinsicSkills.EARTH_TRANSFORM.get()) {
         return true;
      } else if (skill == IntrinsicSkills.FLAME_TRANSFORM.get()) {
         return true;
      } else if (skill == IntrinsicSkills.LIGHT_TRANSFORM.get()) {
         return true;
      } else if (skill == IntrinsicSkills.SPACE_TRANSFORM.get()) {
         return true;
      } else if (skill == IntrinsicSkills.WATER_TRANSFORM.get()) {
         return true;
      } else {
         return skill == IntrinsicSkills.WIND_TRANSFORM.get() ? true : super.isIntrinsicSkill(player, skill);
      }
   }

   public List<Race> getNextEvolutions(Player player) {
      List<Race> list = new ArrayList();
      list.add((Race)TensuraRaces.MYSTIC_ONI.get());
      list.add((Race)TensuraRaces.WICKED_ONI.get());
      return list;
   }

   public List<Race> getPreviousEvolutions(Player player) {
      List<Race> list = new ArrayList();
      list.add((Race)TensuraRaces.OGRE.get());
      return list;
   }

   public double getAuraEvolutionReward() {
      return 2500.0D;
   }

   public double getManaEvolutionReward() {
      return 2500.0D;
   }

   public double getEvolutionPercentage(Player player) {
      MagicElemental[] var2 = MagicElemental.values();
      int var3 = var2.length;

      for(int var4 = 0; var4 < var3; ++var4) {
         MagicElemental elemental = var2[var4];
         if (TensuraSkillCapability.getSpiritLevel(player, elemental.getId()) > 0) {
            return 100.0D;
         }
      }

      return 0.0D;
   }

   public List<Component> getRequirementsForRendering(Player player) {
      List<Component> list = new ArrayList();
      list.add(Component.m_237115_("tensura.evolution_menu.spirit_requirement"));
      return list;
   }
}
