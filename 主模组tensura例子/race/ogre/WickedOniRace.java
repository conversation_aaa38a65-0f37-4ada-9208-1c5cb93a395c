package com.github.manasmods.tensura.race.ogre;

import com.github.manasmods.tensura.ability.TensuraSkill;
import com.github.manasmods.tensura.config.TensuraConfig;
import com.github.manasmods.tensura.race.Race;
import com.github.manasmods.tensura.registry.items.TensuraMobDropItems;
import com.github.manasmods.tensura.registry.race.TensuraRaces;
import com.github.manasmods.tensura.registry.skill.CommonSkills;
import com.github.manasmods.tensura.registry.skill.ExtraSkills;
import com.github.manasmods.tensura.registry.skill.IntrinsicSkills;
import com.github.manasmods.tensura.util.JumpPowerHelper;
import com.mojang.datafixers.util.Pair;
import java.util.ArrayList;
import java.util.List;
import net.minecraft.client.player.LocalPlayer;
import net.minecraft.network.chat.Component;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.stats.Stats;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.Item;
import org.jetbrains.annotations.Nullable;

public class WickedOniRace extends KijinRace {
   public double getBaseHealth() {
      return 100.0D;
   }

   public double getBaseAttackDamage() {
      return 4.5D;
   }

   public double getBaseAttackSpeed() {
      return 6.0D;
   }

   public double getKnockbackResistance() {
      return 0.4D;
   }

   public double getJumpHeight() {
      return JumpPowerHelper.defaultPlayer(1.0D);
   }

   public double getSprintSpeed() {
      return 0.5D;
   }

   public Pair<Double, Double> getBaseAuraRange() {
      return Pair.of(40000.0D, 100000.0D);
   }

   public Pair<Double, Double> getBaseMagiculeRange() {
      return Pair.of(40000.0D, 100000.0D);
   }

   public double getAdditionalSpiritualHealth() {
      return 40.0D;
   }

   public double getSpiritualHealthMultiplier() {
      return 3.0D;
   }

   public boolean isMajin() {
      return true;
   }

   public List<TensuraSkill> getIntrinsicSkills(Player player) {
      List<TensuraSkill> list = new ArrayList();
      list.add((TensuraSkill)CommonSkills.STRENGTH.get());
      list.add((TensuraSkill)IntrinsicSkills.OGRE_BERSERKER.get());
      list.add((TensuraSkill)ExtraSkills.ULTRASPEED_REGENERATION.get());
      return list;
   }

   @Nullable
   public Race getDefaultEvolution(Player player) {
      return (Race)TensuraRaces.DEATH_ONI.get();
   }

   @Nullable
   public Race getAwakeningEvolution(Player player) {
      return (Race)TensuraRaces.DEATH_ONI.get();
   }

   @Nullable
   public Race getHarvestFestivalEvolution(Player player) {
      return null;
   }

   public List<Race> getNextEvolutions(Player player) {
      List<Race> list = new ArrayList();
      list.add((Race)TensuraRaces.DEATH_ONI.get());
      return list;
   }

   public List<Race> getPreviousEvolutions(Player player) {
      List<Race> list = new ArrayList();
      list.add((Race)TensuraRaces.KIJIN.get());
      list.add((Race)TensuraRaces.ENLIGHTENED_OGRE.get());
      return list;
   }

   public double getManaEvolutionReward() {
      return 80000.0D;
   }

   public boolean isSpiritual() {
      return true;
   }

   public double getEvolutionPercentage(Player player) {
      double essence = 0.0D;
      if (player instanceof LocalPlayer) {
         LocalPlayer localPlayer = (LocalPlayer)player;
         essence = (double)localPlayer.m_108630_().m_13015_(Stats.f_12982_.m_12902_((Item)TensuraMobDropItems.DEMON_ESSENCE.get()));
      } else if (player instanceof ServerPlayer) {
         ServerPlayer serverPlayer = (ServerPlayer)player;
         essence = (double)serverPlayer.m_8951_().m_13015_(Stats.f_12982_.m_12902_((Item)TensuraMobDropItems.DEMON_ESSENCE.get()));
      }

      return essence * 100.0D / (double)(Integer)TensuraConfig.INSTANCE.racesConfig.essenceForWicked.get();
   }

   public List<Component> getRequirementsForRendering(Player player) {
      List<Component> list = new ArrayList();
      list.add(Component.m_237110_("tensura.evolution_menu.consume_requirement", new Object[]{((Item)TensuraMobDropItems.DEMON_ESSENCE.get()).m_7968_().m_41611_()}));
      return list;
   }
}
