package com.github.manasmods.tensura.race.ogre;

import com.github.manasmods.tensura.ability.TensuraSkill;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.config.TensuraConfig;
import com.github.manasmods.tensura.race.Race;
import com.github.manasmods.tensura.registry.race.TensuraRaces;
import com.github.manasmods.tensura.registry.skill.IntrinsicSkills;
import com.github.manasmods.tensura.registry.skill.UniqueSkills;
import com.github.manasmods.tensura.util.JumpPowerHelper;
import com.mojang.datafixers.util.Pair;
import java.util.ArrayList;
import java.util.List;
import net.minecraft.network.chat.Component;
import net.minecraft.world.entity.player.Player;

public class DivineFighterRace extends DeathOniRace {
   public double getBaseHealth() {
      return 5000.0D;
   }

   public double getBaseAttackDamage() {
      return 12.0D;
   }

   public double getBaseAttackSpeed() {
      return 5.0D;
   }

   public double getKnockbackResistance() {
      return 0.6D;
   }

   public double getJumpHeight() {
      return JumpPowerHelper.defaultPlayer(3.0D);
   }

   public double getSprintSpeed() {
      return 1.2D;
   }

   public Pair<Double, Double> getBaseAuraRange() {
      return Pair.of(1000000.0D, 1000000.0D);
   }

   public Pair<Double, Double> getBaseMagiculeRange() {
      return Pair.of(1000000.0D, 1000000.0D);
   }

   public double getSpiritualHealthMultiplier() {
      return 6.0D;
   }

   public List<Race> getNextEvolutions(Player player) {
      return new ArrayList();
   }

   public List<Race> getPreviousEvolutions(Player player) {
      List<Race> list = new ArrayList();
      list.add((Race)TensuraRaces.DEATH_ONI.get());
      return list;
   }

   public double getEvolutionPercentage(Player player) {
      return TensuraPlayerCapability.getBaseEP(player) * 100.0D / (Double)TensuraConfig.INSTANCE.racesConfig.epToDivine.get();
   }

   public List<Component> getRequirementsForRendering(Player player) {
      List<Component> list = new ArrayList();
      list.add(Component.m_237115_("tensura.evolution_menu.ep_requirement"));
      return list;
   }

   public boolean isDivine() {
      return true;
   }

   public List<TensuraSkill> getIntrinsicSkills(Player player) {
      List<TensuraSkill> list = super.getIntrinsicSkills(player);
      list.add((TensuraSkill)IntrinsicSkills.DIVINE_KI_RELEASE.get());
      list.add((TensuraSkill)UniqueSkills.DIVINE_BERSERKER.get());
      return list;
   }
}
