package com.github.manasmods.tensura.race.daemon;

import com.github.manasmods.tensura.ability.TensuraSkill;
import com.github.manasmods.tensura.race.Race;
import com.github.manasmods.tensura.registry.dimensions.TensuraDimensions;
import com.github.manasmods.tensura.registry.race.TensuraRaces;
import com.github.manasmods.tensura.registry.skill.IntrinsicSkills;
import com.github.manasmods.tensura.registry.skill.ResistanceSkills;
import com.github.manasmods.tensura.util.JumpPowerHelper;
import com.mojang.datafixers.util.Pair;
import java.util.ArrayList;
import java.util.List;
import net.minecraft.resources.ResourceKey;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import org.jetbrains.annotations.Nullable;

public class LesserDaemonRace extends Race {
   public LesserDaemonRace() {
      super(Race.Difficulty.EASY);
   }

   public LesserDaemonRace(Race.Difficulty difficulty) {
      super(difficulty);
   }

   public double getBaseHealth() {
      return 40.0D;
   }

   public float getPlayerSize() {
      return 3.0F;
   }

   public double getBaseAttackDamage() {
      return 1.4D;
   }

   public double getBaseAttackSpeed() {
      return 3.5D;
   }

   public double getKnockbackResistance() {
      return 0.1D;
   }

   public double getJumpHeight() {
      return JumpPowerHelper.defaultPlayer();
   }

   public double getMovementSpeed() {
      return 0.1D;
   }

   public double getSprintSpeed() {
      return 0.2D;
   }

   public Pair<Double, Double> getBaseAuraRange() {
      return Pair.of(2000.0D, 3000.0D);
   }

   public Pair<Double, Double> getBaseMagiculeRange() {
      return Pair.of(5000.0D, 6000.0D);
   }

   @Nullable
   public Race getDefaultEvolution(Player player) {
      return (Race)TensuraRaces.GREATER_DAEMON.get();
   }

   @Nullable
   public Race getAwakeningEvolution(Player player) {
      return (Race)TensuraRaces.DAEMON_LORD.get();
   }

   @Nullable
   public Race getHarvestFestivalEvolution(Player player) {
      return (Race)TensuraRaces.GREATER_DAEMON.get();
   }

   public double getAdditionalSpiritualHealth() {
      return 20.0D;
   }

   public double getSpiritualHealthMultiplier() {
      return 2.5D;
   }

   public boolean isMajin() {
      return true;
   }

   public boolean isSpiritual() {
      return true;
   }

   public ResourceKey<Level> getRespawnDimension() {
      return TensuraDimensions.HELL;
   }

   public List<TensuraSkill> getIntrinsicSkills(Player player) {
      List<TensuraSkill> list = new ArrayList();
      list.add((TensuraSkill)ResistanceSkills.MAGIC_RESISTANCE.get());
      list.add((TensuraSkill)IntrinsicSkills.POSSESSION.get());
      return list;
   }

   public List<Race> getNextEvolutions(Player player) {
      List<Race> list = new ArrayList();
      list.add((Race)TensuraRaces.GREATER_DAEMON.get());
      return list;
   }

   public void raceAbility(Player entity) {
      if (!entity.m_5833_() && !entity.m_7500_()) {
         Level level = entity.m_9236_();
         if (entity.m_150110_().f_35936_) {
            entity.m_150110_().f_35936_ = false;
            entity.m_150110_().f_35935_ = false;
            entity.m_6885_();
            level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11887_, SoundSource.PLAYERS, 1.0F, 1.0F);
         } else {
            entity.m_150110_().f_35936_ = true;
            entity.m_150110_().f_35935_ = true;
            entity.m_6885_();
            level.m_6263_((Player)null, entity.m_20185_(), entity.m_20186_(), entity.m_20189_(), SoundEvents.f_11887_, SoundSource.PLAYERS, 1.0F, 1.0F);
         }

      }
   }
}
