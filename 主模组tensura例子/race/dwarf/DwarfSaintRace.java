package com.github.manasmods.tensura.race.dwarf;

import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.config.TensuraConfig;
import com.github.manasmods.tensura.race.Race;
import com.github.manasmods.tensura.registry.TensuraStats;
import com.github.manasmods.tensura.registry.race.TensuraRaces;
import com.github.manasmods.tensura.util.JumpPowerHelper;
import com.mojang.datafixers.util.Pair;
import java.util.ArrayList;
import java.util.List;
import net.minecraft.network.chat.Component;
import net.minecraft.world.entity.player.Player;
import org.jetbrains.annotations.Nullable;

public class DwarfSaintRace extends EnlightenedDwarfRace {
   public double getBaseHealth() {
      return 1200.0D;
   }

   public double getBaseAttackDamage() {
      return 3.0D;
   }

   public double getBaseAttackSpeed() {
      return 4.4D;
   }

   public double getKnockbackResistance() {
      return 1.0D;
   }

   public double getJumpHeight() {
      return JumpPowerHelper.defaultPlayer(1.0D);
   }

   public double getAdditionalSpiritualHealth() {
      return 50.0D;
   }

   public double getSpiritualHealthMultiplier() {
      return 3.0D;
   }

   public Pair<Double, Double> getBaseAuraRange() {
      return Pair.of(400000.0D, 400000.0D);
   }

   public Pair<Double, Double> getBaseMagiculeRange() {
      return Pair.of(400000.0D, 400000.0D);
   }

   @Nullable
   public Race getDefaultEvolution(Player player) {
      return (Race)TensuraRaces.DIVINE_DWARF.get();
   }

   @Nullable
   public Race getAwakeningEvolution(Player player) {
      return (Race)TensuraRaces.DIVINE_DWARF.get();
   }

   public List<Race> getNextEvolutions(Player player) {
      List<Race> list = new ArrayList();
      list.add((Race)TensuraRaces.DIVINE_DWARF.get());
      return list;
   }

   public List<Race> getPreviousEvolutions(Player player) {
      List<Race> list = new ArrayList();
      list.add((Race)TensuraRaces.ENLIGHTENED_DWARF.get());
      return list;
   }

   public double getEvolutionPercentage(Player player) {
      double ep = Math.min(TensuraPlayerCapability.getBaseEP(player) * 50.0D / (Double)TensuraConfig.INSTANCE.racesConfig.epToSaint.get(), 50.0D);
      double boss = (double)Math.min(TensuraStats.getBossKilled(player) * 50 / (Integer)TensuraConfig.INSTANCE.racesConfig.bossForSaint.get(), 50);
      return ep + boss;
   }

   public List<Component> getRequirementsForRendering(Player player) {
      List<Component> list = new ArrayList();
      list.add(Component.m_237115_("tensura.evolution_menu.ep_requirement"));
      list.add(Component.m_237115_("tensura.evolution_menu.boss_kill_requirement"));
      return list;
   }

   public double getAuraEvolutionReward() {
      return 300000.0D;
   }

   public double getManaEvolutionReward() {
      return 300000.0D;
   }

   public boolean isSpiritual() {
      return true;
   }
}
