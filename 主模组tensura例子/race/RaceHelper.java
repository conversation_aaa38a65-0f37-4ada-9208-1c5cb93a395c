package com.github.manasmods.tensura.race;

import com.github.manasmods.manascore.api.attribute.AttributeModifierHelper;
import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.ManasSkillInstance;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.manascore.api.skills.capability.SkillStorage;
import com.github.manasmods.tensura.ability.SkillHelper;
import com.github.manasmods.tensura.ability.SkillUtils;
import com.github.manasmods.tensura.ability.TensuraSkillInstance;
import com.github.manasmods.tensura.ability.skill.resist.ResistSkill;
import com.github.manasmods.tensura.api.entity.subclass.IRanking;
import com.github.manasmods.tensura.capability.effects.TensuraEffectsCapability;
import com.github.manasmods.tensura.capability.ep.TensuraEPCapability;
import com.github.manasmods.tensura.capability.race.ITensuraPlayerCapability;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.capability.skill.TensuraSkillCapability;
import com.github.manasmods.tensura.client.particle.TensuraParticleHelper;
import com.github.manasmods.tensura.config.TensuraConfig;
import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.data.pack.EntityEPCount;
import com.github.manasmods.tensura.data.pack.TensuraData;
import com.github.manasmods.tensura.entity.human.CloneEntity;
import com.github.manasmods.tensura.handler.CapabilityHandler;
import com.github.manasmods.tensura.race.vampire.GhoulRace;
import com.github.manasmods.tensura.race.wight.WightRace;
import com.github.manasmods.tensura.registry.attribute.TensuraAttributeRegistry;
import com.github.manasmods.tensura.registry.dimensions.HellTeleporter;
import com.github.manasmods.tensura.registry.dimensions.LabyrinthTeleporter;
import com.github.manasmods.tensura.registry.dimensions.TensuraDimensions;
import com.github.manasmods.tensura.registry.effects.TensuraMobEffects;
import com.github.manasmods.tensura.registry.skill.IntrinsicSkills;
import com.github.manasmods.tensura.registry.skill.UniqueSkills;
import com.github.manasmods.tensura.util.TensuraAdvancementsHelper;
import com.github.manasmods.tensura.util.attribute.TensuraAttributeModifierIds;
import com.github.manasmods.tensura.util.damage.DamageSourceHelper;
import com.github.manasmods.tensura.world.TensuraGameRules;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import net.minecraft.ChatFormatting;
import net.minecraft.core.particles.ParticleTypes;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.Style;
import net.minecraft.resources.ResourceKey;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.effect.MobEffect;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.MobSpawnType;
import net.minecraft.world.entity.MobType;
import net.minecraft.world.entity.SpawnGroupData;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.AttributeInstance;
import net.minecraft.world.entity.ai.attributes.AttributeModifier;
import net.minecraft.world.entity.ai.attributes.AttributeSupplier;
import net.minecraft.world.entity.ai.attributes.DefaultAttributes;
import net.minecraft.world.entity.ai.attributes.AttributeModifier.Operation;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import org.jetbrains.annotations.Nullable;

public class RaceHelper {
   public static boolean isSpiritual(LivingEntity entity) {
      return entity.m_6095_().m_204039_(TensuraTags.EntityTypes.SPIRITUAL);
   }

   public static boolean isSpiritualLifeForm(LivingEntity entity) {
      if (entity instanceof Player) {
         Player player = (Player)entity;
         Race race = TensuraPlayerCapability.getRace(player);
         if (race != null && race.isSpiritual()) {
            return true;
         }

         if (TensuraPlayerCapability.isSpiritualForm(player)) {
            return true;
         }
      }

      return isSpiritual(entity);
   }

   public static float spiritualReducePhysicalDamage(DamageSource damageSource) {
      if (!DamageSourceHelper.isPhysicalAttack(damageSource)) {
         return 1.0F;
      } else {
         Entity var2 = damageSource.m_7639_();
         if (var2 instanceof LivingEntity) {
            LivingEntity entity = (LivingEntity)var2;
            MobEffectInstance instance = entity.m_21124_((MobEffect)TensuraMobEffects.HAKI_COAT.get());
            if (instance != null) {
               if (instance.m_19564_() >= 1) {
                  return 1.0F;
               }

               if (instance.m_19564_() >= 0) {
                  return 0.5F;
               }
            }

            if (entity.m_21023_((MobEffect)TensuraMobEffects.MAGIC_AURA.get())) {
               return 0.5F;
            }

            if (!TensuraSkillCapability.isSkillInSlot(entity, (ManasSkill)UniqueSkills.COOK.get())) {
               return 0.5F;
            }
         }

         return 0.001F;
      }
   }

   public static boolean isAffectedByHolyCoat(Entity entity) {
      if (entity.m_6095_().m_204039_(TensuraTags.EntityTypes.MONSTER)) {
         return true;
      } else {
         if (entity instanceof LivingEntity) {
            LivingEntity target = (LivingEntity)entity;
            if (TensuraEPCapability.isMajin(target)) {
               if (entity instanceof Player) {
                  Player player = (Player)entity;
                  return !TensuraEPCapability.isChaos(player);
               }

               return true;
            }
         }

         return false;
      }
   }

   public static boolean isUndead(Entity entity) {
      if (entity instanceof Player) {
         Player player = (Player)entity;
         if (TensuraEPCapability.isChaos(player)) {
            return false;
         } else {
            Race race = TensuraPlayerCapability.getRace(player);
            return race instanceof WightRace ? true : race instanceof GhoulRace;
         }
      } else if (entity.m_6095_().m_204039_(TensuraTags.EntityTypes.UNDEAD)) {
         return true;
      } else {
         boolean var10000;
         if (entity instanceof Mob) {
            Mob mob = (Mob)entity;
            if (mob.m_6336_().equals(MobType.f_21641_)) {
               var10000 = true;
               return var10000;
            }
         }

         var10000 = false;
         return var10000;
      }
   }

   public static boolean hasNoBlood(LivingEntity entity) {
      if (entity.m_6095_().m_204039_(TensuraTags.EntityTypes.NO_BLOOD)) {
         return true;
      } else {
         if (entity instanceof Player) {
            Player player = (Player)entity;
            if (TensuraPlayerCapability.isSpiritualForm(player)) {
               return true;
            }
         }

         return isUndead(entity);
      }
   }

   public static float getSizeMultiplier(LivingEntity entity) {
      return getRaceSizeMultiplier(entity) * getSkillSizeMultiplier(entity) * TensuraEffectsCapability.getHeight(entity);
   }

   public static float getRaceSizeMultiplier(LivingEntity entity) {
      if (entity instanceof CloneEntity) {
         CloneEntity clone = (CloneEntity)entity;
         return clone.getHeight();
      } else {
         Race race = TensuraPlayerCapability.getRace(entity);
         return getRaceSize(race);
      }
   }

   public static float getRaceSize(@Nullable Race race) {
      float size = 1.0F;
      if (race != null) {
         size *= race.getPlayerSize() / 2.0F;
      }

      return (float)Math.max((double)size, (Double)TensuraConfig.INSTANCE.attributeConfig.minimumSize.get());
   }

   public static float getSkillSizeMultiplier(LivingEntity entity) {
      float size = 1.0F;
      if (entity.m_21204_() != null) {
         AttributeInstance instance = entity.m_21051_((Attribute)TensuraAttributeRegistry.SIZE.get());
         if (instance != null) {
            size *= (float)instance.m_22135_();
         }
      }

      return size;
   }

   public static void handleRespawnDimension(Player player, Race race) {
      if (player instanceof ServerPlayer) {
         ServerPlayer serverPlayer = (ServerPlayer)player;
         ResourceKey dimension = race.getRespawnDimension();
         ResourceKey respawnDimension = serverPlayer.m_8963_();
         if (dimension != respawnDimension) {
            ResourceKey<Level> currentDimension = player.m_9236_().m_46472_();
            if (dimension != currentDimension) {
               ServerLevel serverLevel = player.m_9236_().m_7654_().m_129880_(dimension);
               if (serverLevel != null) {
                  if (dimension == TensuraDimensions.HELL) {
                     player.changeDimension(serverLevel, new HellTeleporter(true));
                  } else if (dimension == TensuraDimensions.LABYRINTH) {
                     player.changeDimension(serverLevel, new LabyrinthTeleporter());
                  } else {
                     player.m_5489_(serverLevel);
                  }

               }
            }
         }
      }
   }

   public static void applyMajinChance(Player player) {
      double chance = SkillUtils.hasSkill(player, (ManasSkill)UniqueSkills.VILLAIN.get()) ? 100.0D : (Double)TensuraConfig.INSTANCE.awakeningConfig.majinPercentage.get();
      if ((double)(player.m_217043_().m_188501_() * 100.0F) <= chance) {
         TensuraEPCapability.getFrom(player).ifPresent((cap) -> {
            if (!cap.isMajin()) {
               cap.setMajin(true);
               TensuraEPCapability.sync(player);
            }
         });
      }

   }

   public static boolean evolveRace(Player player) {
      Race race = TensuraPlayerCapability.getRace(player);
      if (race == null) {
         return false;
      } else {
         Race evo = race.getDefaultEvolution(player);
         return evo == null ? false : evolveRace(player, evo, true);
      }
   }

   public static boolean evolveRace(Player player, Race race, boolean triggerRewards) {
      return evolveRace(player, race, triggerRewards, false);
   }

   public static boolean evolveRace(Player player, Race race, boolean triggerRewards, boolean skipNextEvo) {
      if (player.m_9236_().m_5776_()) {
         return false;
      } else {
         Race originalRace = TensuraPlayerCapability.getRace(player);
         if (originalRace == null) {
            return false;
         } else if (!skipNextEvo && !originalRace.getNextEvolutions(player).contains(race)) {
            return false;
         } else {
            if (player instanceof ServerPlayer) {
               ServerPlayer serverPlayer = (ServerPlayer)player;
               TensuraAdvancementsHelper.grant(serverPlayer, TensuraAdvancementsHelper.Advancements.GROWTH_SPURT);
            }

            TensuraPlayerCapability.getFrom(player).ifPresent((cap) -> {
               boolean isMajin = TensuraEPCapability.isMajin(player);
               cap.setRace(player, race, false);
               if (isMajin || race.isMajin()) {
                  TensuraEPCapability.setMajin(player, true);
               }

               if (triggerRewards) {
                  race.triggerEvolutionRewards(player);
               }

               if (!race.getIntrinsicSkills(player).isEmpty()) {
                  Iterator var5 = race.getIntrinsicSkills(player).iterator();

                  while(var5.hasNext()) {
                     ManasSkill skill = (ManasSkill)var5.next();
                     if (SkillUtils.learnSkill(player, (ManasSkillInstance)(new TensuraSkillInstance(skill)))) {
                        cap.addIntrinsicSkill(skill);
                        player.m_5661_(Component.m_237110_("tensura.skill.acquire", new Object[]{skill.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)), false);
                     }
                  }
               }

               if (cap.isHeroEgg() && race.isMajin() && !TensuraEPCapability.isChaos(player)) {
                  player.m_5661_(Component.m_237115_("tensura.evolve.hero.egg_lost.evolution").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.RED)), false);
               }

            });
            TensuraPlayerCapability.sync(player);
            return originalRace != race;
         }
      }
   }

   public static boolean canAwaken(Player player, boolean isHero, int souls) {
      if (shouldNamingStopAwakening(player)) {
         return false;
      } else {
         ITensuraPlayerCapability cap = (ITensuraPlayerCapability)CapabilityHandler.getCapability(player, TensuraPlayerCapability.CAPABILITY);
         if (cap == null) {
            return false;
         } else if (cap.isTrueDemonLord()) {
            return false;
         } else if (cap.isTrueHero()) {
            return false;
         } else if (isHero) {
            if (!cap.isHeroEgg()) {
               return false;
            } else {
               return cap.getRace() != null && cap.getRace().isMajin() ? false : fightingBossForHero(player);
            }
         } else {
            return cap.isDemonLordSeed() && cap.getSoulPoints() >= souls * 1000;
         }
      }
   }

   public static void awakening(Player player, boolean isHero) {
      Level level = player.m_9236_();
      if (player instanceof ServerPlayer) {
         ServerPlayer serverPlayer = (ServerPlayer)player;
         TensuraAdvancementsHelper.grant(serverPlayer, TensuraAdvancementsHelper.Advancements.HIGHER_FORM);
      }

      TensuraPlayerCapability.getFrom(player).ifPresent((cap) -> {
         float multiplier = isHero ? 3.0F : 2.5F;
         cap.setBaseMagicule(cap.getBaseMagicule() * (double)multiplier, player);
         cap.setBaseAura(cap.getBaseAura() * (double)multiplier, player);
         SkillStorage storage = SkillAPI.getSkillsFrom(player);
         Iterator var6 = List.copyOf(storage.getLearnedSkills()).iterator();

         while(var6.hasNext()) {
            ManasSkillInstance instance = (ManasSkillInstance)var6.next();
            ManasSkill patt12472$temp = instance.getSkill();
            if (patt12472$temp instanceof ResistSkill) {
               ResistSkill resistSkill = (ResistSkill)patt12472$temp;
               storage.getSkill(resistSkill).ifPresent((skillInstance) -> {
                  resistSkill.evolveToNullification(skillInstance, player);
               });
            }
         }

         if (cap.getRace() != null && cap.getRace().getAwakeningEvolution(player) != null) {
            evolveRace(player, cap.getRace().getAwakeningEvolution(player), false, true);
         }

         level.m_6263_((Player)null, player.m_20185_(), player.m_20186_(), player.m_20189_(), SoundEvents.f_12496_, SoundSource.PLAYERS, 1.0F, 1.0F);
         TensuraParticleHelper.addServerParticlesAroundSelf(player, ParticleTypes.f_123767_, 1.0D);
         TensuraParticleHelper.addServerParticlesAroundSelf(player, ParticleTypes.f_123767_, 2.0D);
         TensuraParticleHelper.addServerParticlesAroundSelf(player, ParticleTypes.f_123747_, 1.0D);
         Player everyone;
         if (isHero) {
            var6 = level.m_6907_().iterator();

            while(var6.hasNext()) {
               everyone = (Player)var6.next();
               everyone.m_5661_(Component.m_237110_("tensura.evolve.hero.success", new Object[]{player.m_7755_()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)), false);
            }

            ManasSkill skill = (ManasSkill)IntrinsicSkills.UNPREDICTABILITY.get();
            if (SkillUtils.learnSkill(player, (ManasSkill)skill)) {
               cap.addIntrinsicSkill(skill);
               player.m_5661_(Component.m_237110_("tensura.skill.acquire", new Object[]{skill.getName()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)), false);
            }

            TensuraPlayerCapability.sync(player);
         } else {
            var6 = level.m_6907_().iterator();

            while(var6.hasNext()) {
               everyone = (Player)var6.next();
               everyone.m_5661_(Component.m_237110_("tensura.evolve.demon_lord.success", new Object[]{player.m_7755_()}).m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)), false);
               if (Objects.equals(TensuraEPCapability.getPermanentOwner(everyone), player.m_20148_())) {
                  Race race = TensuraPlayerCapability.getRace(player);
                  if (race != null && race.getHarvestFestivalEvolution(player) != null) {
                     everyone.m_5661_(Component.m_237115_("tensura.evolve.demon_lord.subordinate_evolve").m_6270_(Style.f_131099_.m_131140_(ChatFormatting.GOLD)), false);
                     evolveRace(everyone, race.getHarvestFestivalEvolution(player), true);
                  }
               }
            }

            List<LivingEntity> list = level.m_6443_(LivingEntity.class, player.m_20191_().m_82400_(30.0D), (living) -> {
               return !(living instanceof Player) && living.m_6084_();
            });
            if (!list.isEmpty()) {
               Iterator var13 = list.iterator();

               while(var13.hasNext()) {
                  LivingEntity sub = (LivingEntity)var13.next();
                  if (Objects.equals(TensuraEPCapability.getPermanentOwner(sub), player.m_20148_())) {
                     TensuraEPCapability.getFrom(sub).ifPresent((epCap) -> {
                        if (!epCap.isHarvestGift()) {
                           epCap.setHarvestGift(true);
                           epCap.setEP(sub, epCap.getEP() * 2.0D);
                           evolveMobs(sub);
                           TensuraEPCapability.sync(sub);
                        }

                     });
                  }
               }
            }

         }
      });
      TensuraPlayerCapability.sync(player);
   }

   public static boolean shouldNamingStopAwakening(Player player) {
      if (!player.m_9236_().m_46469_().m_46207_(TensuraGameRules.HARDCORE_RACE)) {
         return false;
      } else {
         return TensuraEPCapability.getName(player) != null && TensuraEPCapability.getPermanentOwner(player) != null;
      }
   }

   public static boolean fightingBossForHero(Player player) {
      LivingEntity lastHurtBy = player.m_21188_();
      if (lastHurtBy != null && lastHurtBy.m_6095_().m_204039_(TensuraTags.EntityTypes.HERO_BOSS) && SkillHelper.getSubordinateOwner(lastHurtBy) == null && lastHurtBy.m_21223_() < lastHurtBy.m_21233_() / 2.0F && TensuraEPCapability.getEP(lastHurtBy) >= 400000.0D) {
         return true;
      } else {
         LivingEntity lastHurt = player.m_21214_();
         if (lastHurt == null) {
            return false;
         } else if (!lastHurt.m_6095_().m_204039_(TensuraTags.EntityTypes.HERO_BOSS)) {
            return false;
         } else if (SkillHelper.getSubordinateOwner(lastHurt) != null) {
            return false;
         } else if (lastHurt.m_21223_() >= lastHurt.m_21233_() / 2.0F) {
            return false;
         } else {
            return TensuraEPCapability.getEP(lastHurt) >= 400000.0D;
         }
      }
   }

   public static void applyBaseAttribute(EntityType<? extends LivingEntity> type, LivingEntity entity) {
      applyBaseAttribute(DefaultAttributes.m_22297_(type), entity);
   }

   public static void applyBaseAttribute(AttributeSupplier supplier, LivingEntity entity) {
      applyBaseAttribute(supplier, entity, false);
   }

   public static void applyBaseAttribute(AttributeSupplier supplier, LivingEntity entity, boolean totalReset) {
      Iterator var3 = supplier.f_22241_.values().iterator();

      while(true) {
         AttributeInstance attributeInstance;
         double base;
         do {
            AttributeInstance attribute;
            do {
               if (!var3.hasNext()) {
                  return;
               }

               attribute = (AttributeInstance)var3.next();
               attributeInstance = entity.m_21051_(attribute.m_22099_());
            } while(attributeInstance == null);

            base = attribute.m_22115_();
         } while(!totalReset && !(attributeInstance.m_22115_() < base));

         attributeInstance.m_22100_(base);
      }
   }

   public static void evolveMobs(LivingEntity sub) {
      if (sub instanceof IRanking) {
         IRanking ranking = (IRanking)sub;
         ranking.evolve();
      }

      Iterator var9 = TensuraData.getEntityEP().iterator();

      while(var9.hasNext()) {
         EntityEPCount entityEP = (EntityEPCount)var9.next();
         if (entityEP.getEntity().equals(EntityType.m_20613_(sub.m_6095_()))) {
            Optional<EntityType<?>> optional = EntityType.m_20632_(entityEP.getEvolution().toString());
            if (!optional.isEmpty()) {
               Level level = sub.m_9236_();
               CompoundTag tag = sub.serializeNBT();
               sub.m_146870_();
               Entity entity = ((EntityType)optional.get()).m_20615_(level);
               if (entity != null) {
                  entity.m_20258_(tag);
                  if (entity instanceof Mob) {
                     Mob mob = (Mob)entity;
                     if (level instanceof ServerLevel) {
                        ServerLevel serverLevel = (ServerLevel)level;
                        mob.m_6518_(serverLevel, level.m_6436_(mob.m_20183_()), MobSpawnType.CONVERSION, (SpawnGroupData)null, (CompoundTag)null);
                     }
                  }

                  level.m_7967_(entity);
                  if (!(entity instanceof LivingEntity)) {
                     return;
                  }

                  LivingEntity living = (LivingEntity)entity;
                  applyBaseAttribute((EntityType)optional.get(), living);
                  living.m_21153_(living.m_21233_());
                  updateSpiritualHP(living);
                  updateEntityEPCount(living, entityEP.getEvolution());
               }
            }
         }
      }

   }

   public static void updateSpiritualHP(LivingEntity living) {
      double maxSHP = living.m_21133_((Attribute)TensuraAttributeRegistry.MAX_SPIRITUAL_HEALTH.get());
      if ((double)(living.m_21233_() * 2.0F) > maxSHP) {
         AttributeModifierHelper.setModifier(living, (Attribute)TensuraAttributeRegistry.MAX_SPIRITUAL_HEALTH.get(), new AttributeModifier(TensuraAttributeModifierIds.RACE_BASE_SPIRITUAL_HEALTH_MODIFIER_ID, "tensura:race_base_spiritual_health", (double)(living.m_21233_() * 2.0F) - maxSHP, Operation.ADDITION));
      }

      TensuraEPCapability.setSpiritualHealth(living, living.m_21133_((Attribute)TensuraAttributeRegistry.MAX_SPIRITUAL_HEALTH.get()));
   }

   public static void updateEntityEPCount(LivingEntity living) {
      updateEntityEPCount(living, EntityType.m_20613_(living.m_6095_()));
   }

   public static void updateEntityEPCount(LivingEntity living, ResourceLocation location) {
      Iterator var2 = TensuraData.getEntityEP().iterator();

      while(var2.hasNext()) {
         EntityEPCount evolvedEPCount = (EntityEPCount)var2.next();
         if (evolvedEPCount.getEntity().equals(location)) {
            TensuraEPCapability.getFrom(living).ifPresent((cap) -> {
               int minEP = evolvedEPCount.getMinEP();
               if (cap.getEP() < (double)minEP) {
                  int maxEP = evolvedEPCount.getMaxEP() + 1;
                  if (maxEP <= minEP) {
                     maxEP = minEP + 1;
                  }

                  cap.setEP(living, (double)living.m_217043_().m_216339_(minEP, maxEP));
               }

            });
            Iterator var4 = evolvedEPCount.getSkills().iterator();

            while(var4.hasNext()) {
               ResourceLocation skillID = (ResourceLocation)var4.next();
               ManasSkill skill = (ManasSkill)SkillAPI.getSkillRegistry().getValue(skillID);
               if (skill != null) {
                  TensuraSkillInstance instance = new TensuraSkillInstance(skill);
                  SkillAPI.getSkillsFrom(living).learnSkill(instance);
                  if (instance.canBeToggled(living)) {
                     instance.setToggled(true);
                  }
               }
            }

            return;
         }
      }

   }
}
