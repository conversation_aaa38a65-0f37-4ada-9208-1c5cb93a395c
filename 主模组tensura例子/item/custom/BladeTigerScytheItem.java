package com.github.manasmods.tensura.item.custom;

import com.github.manasmods.tensura.item.TensuraCreativeTab;
import com.github.manasmods.tensura.item.TensuraToolTiers;
import com.github.manasmods.tensura.item.templates.custom.SimpleScytheItem;
import com.github.manasmods.tensura.registry.items.TensuraMobDropItems;
import net.minecraft.world.item.Item;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Item.Properties;

public class BladeTigerScytheItem extends SimpleScytheItem {
   public BladeTigerScytheItem() {
      super(TensuraToolTiers.HIGH_MAGISTEEL, 7, -3.0F, 2.0D, 20.0D, 0.0D, 6, -3.2F, 100.0D, 50.0D, (new Properties()).m_41491_(TensuraCreativeTab.GEAR).m_41499_(2000));
   }

   public boolean m_6832_(ItemStack pToRepair, ItemStack pRepair) {
      return pRepair.m_150930_((Item)TensuraMobDropItems.BLADE_TIGER_TAIL.get());
   }
}
