package com.github.manasmods.tensura.item.custom;

import com.github.manasmods.tensura.item.client.TempestScaleShieldItemRenderer;
import com.github.manasmods.tensura.item.templates.SimpleShieldItem;
import com.github.manasmods.tensura.registry.items.TensuraMobDropItems;
import com.google.common.collect.ImmutableMultimap;
import com.google.common.collect.Multimap;
import java.util.function.Consumer;
import net.minecraft.client.renderer.BlockEntityWithoutLevelRenderer;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.AttributeModifier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.entity.ai.attributes.AttributeModifier.Operation;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Item.Properties;
import net.minecraftforge.client.extensions.common.IClientItemExtensions;
import software.bernie.geckolib3.core.IAnimatable;
import software.bernie.geckolib3.core.manager.AnimationData;
import software.bernie.geckolib3.core.manager.AnimationFactory;
import software.bernie.geckolib3.util.GeckoLibUtil;

public class TempestScaleShieldItem extends SimpleShieldItem implements IAnimatable {
   private final AnimationFactory factory = GeckoLibUtil.createFactory(this, true);

   public TempestScaleShieldItem(Properties pProperties) {
      super(pProperties);
   }

   public boolean m_6832_(ItemStack toRepair, ItemStack repair) {
      return TensuraMobDropItems.CHARYBDIS_SCALE.get() == repair.m_41720_() || super.m_6832_(toRepair, repair);
   }

   public Multimap<Attribute, AttributeModifier> getAttributeModifiers(EquipmentSlot slot, ItemStack stack) {
      return slot != EquipmentSlot.MAINHAND ? ImmutableMultimap.of() : ImmutableMultimap.builder().put(Attributes.f_22281_, new AttributeModifier(f_41374_, "Weapon modifier", 14.0D, Operation.ADDITION)).put(Attributes.f_22283_, new AttributeModifier(f_41375_, "Weapon modifier", -3.0D, Operation.ADDITION)).build();
   }

   public void initializeClient(Consumer<IClientItemExtensions> consumer) {
      consumer.accept(new IClientItemExtensions() {
         private TempestScaleShieldItemRenderer renderer;

         public BlockEntityWithoutLevelRenderer getCustomRenderer() {
            if (this.renderer == null) {
               this.renderer = new TempestScaleShieldItemRenderer();
            }

            return this.renderer;
         }
      });
   }

   public void registerControllers(AnimationData data) {
   }

   public AnimationFactory getFactory() {
      return this.factory;
   }
}
