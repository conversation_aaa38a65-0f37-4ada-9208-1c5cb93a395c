package com.github.manasmods.tensura.item;

import com.github.manasmods.tensura.data.TensuraTags;
import com.github.manasmods.tensura.registry.items.TensuraMaterialItems;
import java.util.List;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.item.Tier;
import net.minecraft.world.item.crafting.Ingredient;
import net.minecraft.world.level.ItemLike;
import net.minecraftforge.common.ForgeTier;
import net.minecraftforge.common.TierSortingRegistry;
import net.minecraftforge.common.Tags.Blocks;

public class TensuraToolTiers {
   private static final ResourceLocation UNIQUE_ID = tierId("unique");
   private static final ResourceLocation SILVER_ID = tierId("silver");
   private static final ResourceLocation LOW_MAGISTEEL_ID = tierId("low_magisteel");
   private static final ResourceLocation HIGH_MAGISTEEL_ID = tierId("high_magisteel");
   private static final ResourceLocation MITHRIL_ID = tierId("mithril");
   private static final ResourceLocation ORICHALCUM_ID = tierId("orichalcum");
   private static final ResourceLocation PURE_MAGISTEEL_ID = tierId("pure_magisteel");
   private static final ResourceLocation ADAMANTITE_ID = tierId("adamanite");
   private static final ResourceLocation HIHIIROKANE_ID = tierId("hihiirokane");
   public static final Tier SILVER;
   public static final Tier LOW_MAGISTEEL;
   public static final Tier HIGH_MAGISTEEL;
   public static final Tier MITHRIL;
   public static final Tier ORICHALCUM;
   public static final Tier PURE_MAGISTEEL;
   public static final Tier ADAMANTITE;
   public static final Tier HIHIIROKANE;

   private static ResourceLocation tierId(String tierName) {
      return new ResourceLocation("tensura", "tier/" + tierName);
   }

   static {
      SILVER = TierSortingRegistry.registerTier(new ForgeTier(1, 150, 10.0F, 2.0F, 20, Blocks.NEEDS_GOLD_TOOL, () -> {
         return Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraMaterialItems.SILVER_INGOT.get()});
      }), SILVER_ID, List.of(new ResourceLocation("iron")), List.of(new ResourceLocation("diamond")));
      LOW_MAGISTEEL = TierSortingRegistry.registerTier(new ForgeTier(3, 1800, 8.0F, 8.0F, 25, Blocks.NEEDS_NETHERITE_TOOL, () -> {
         return Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraMaterialItems.LOW_MAGISTEEL_INGOT.get()});
      }), LOW_MAGISTEEL_ID, List.of(new ResourceLocation("diamond")), List.of(new ResourceLocation("netherite")));
      HIGH_MAGISTEEL = TierSortingRegistry.registerTier(new ForgeTier(4, 2500, 12.0F, 16.0F, 30, TensuraTags.Blocks.NEEDS_MAGISTEEL_TOOL, () -> {
         return Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraMaterialItems.HIGH_MAGISTEEL_INGOT.get()});
      }), HIGH_MAGISTEEL_ID, List.of(new ResourceLocation("netherite")), List.of(MITHRIL_ID));
      MITHRIL = TierSortingRegistry.registerTier(new ForgeTier(5, 2700, 14.0F, 22.0F, 35, TensuraTags.Blocks.NEEDS_MAGISTEEL_TOOL, () -> {
         return Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraMaterialItems.MITHRIL_INGOT.get()});
      }), MITHRIL_ID, List.of(HIGH_MAGISTEEL_ID), List.of(ORICHALCUM_ID));
      ORICHALCUM = TierSortingRegistry.registerTier(new ForgeTier(6, 2800, 16.0F, 26.0F, 40, TensuraTags.Blocks.NEEDS_MAGISTEEL_TOOL, () -> {
         return Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraMaterialItems.ORICHALCUM_INGOT.get()});
      }), ORICHALCUM_ID, List.of(MITHRIL_ID), List.of(PURE_MAGISTEEL_ID));
      PURE_MAGISTEEL = TierSortingRegistry.registerTier(new ForgeTier(7, 3000, 20.0F, 30.0F, 40, TensuraTags.Blocks.NEEDS_MAGISTEEL_TOOL, () -> {
         return Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraMaterialItems.PURE_MAGISTEEL_INGOT.get()});
      }), PURE_MAGISTEEL_ID, List.of(ORICHALCUM_ID), List.of(ADAMANTITE_ID));
      ADAMANTITE = TierSortingRegistry.registerTier(new ForgeTier(8, 3200, 24.0F, 46.0F, 45, TensuraTags.Blocks.NEEDS_MAGISTEEL_TOOL, () -> {
         return Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraMaterialItems.ADAMANTITE_INGOT.get()});
      }), ADAMANTITE_ID, List.of(PURE_MAGISTEEL_ID), List.of(HIHIIROKANE_ID));
      HIHIIROKANE = TierSortingRegistry.registerTier(new ForgeTier(9, 3600, 27.0F, 76.0F, 50, TensuraTags.Blocks.NEEDS_MAGISTEEL_TOOL, () -> {
         return Ingredient.m_43929_(new ItemLike[]{(ItemLike)TensuraMaterialItems.HIHIIROKANE_INGOT.get()});
      }), HIHIIROKANE_ID, List.of(ADAMANTITE_ID), List.of(UNIQUE_ID));
   }
}
