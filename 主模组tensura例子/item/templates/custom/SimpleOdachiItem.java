package com.github.manasmods.tensura.item.templates.custom;

import java.util.List;
import net.minecraft.core.NonNullList;
import net.minecraft.network.chat.Component;
import net.minecraft.world.item.CreativeModeTab;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Tier;
import net.minecraft.world.item.TooltipFlag;
import net.minecraft.world.item.Item.Properties;
import net.minecraft.world.level.Level;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public class SimpleOdachiItem extends TwoHandedLongSword {
   public SimpleOdachiItem(Tier pTier, Properties properties) {
      super(pTier, 6, -3.2F, 2.0D, 20.0D, 0.0D, (int)(-1.0F - pTier.m_6631_()), -4.0F, 100.0D, 0.0D, properties);
   }

   public void m_7373_(@NotNull ItemStack pStack, @Nullable Level pLevel, @NotNull List<Component> pTooltipComponents, @NotNull TooltipFlag pIsAdvanced) {
      pTooltipComponents.add(Component.m_237115_("tooltip.tensura.great_sword.tooltip"));
   }

   public void m_6787_(CreativeModeTab pCategory, NonNullList<ItemStack> pItems) {
      if (this.m_220152_(pCategory)) {
         ItemStack itemStack = new ItemStack(this);
         TwoHandedLongSword.setTwoHandedTag(itemStack, true);
         pItems.add(itemStack);
      }
   }
}
