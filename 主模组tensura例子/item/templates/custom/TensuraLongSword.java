package com.github.manasmods.tensura.item.templates.custom;

import com.google.common.collect.ImmutableMultimap;
import com.google.common.collect.Multimap;
import java.util.UUID;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.AttributeModifier;
import net.minecraft.world.entity.ai.attributes.AttributeModifier.Operation;
import net.minecraft.world.item.Tier;
import net.minecraft.world.item.Item.Properties;
import net.minecraftforge.common.ForgeMod;
import org.jetbrains.annotations.NotNull;

public class TensuraLongSword extends TensuraSword {
   protected static final UUID BASE_ATTACK_RANGE_UUID = UUID.fromString("60882508-a398-11ed-a8fc-0242ac120002");
   private final double attackRangeModifier;

   public TensuraLongSword(Tier pTier, int pAttackDamageModifier, float pAttackSpeedModifier, double attackRangeModifier, double critChance, double critDamageMultiplier, Properties pProperties) {
      this(pTier, pAttackDamageModifier, pAttackSpeedModifier, attackRangeModifier, critChance, critDamageMultiplier, 0.0D, pProperties);
   }

   public TensuraLongSword(Tier pTier, int pAttackDamageModifier, float pAttackSpeedModifier, double attackRangeModifier, double critChance, double critDamageMultiplier, double sweepChance, Properties pProperties) {
      super(pTier, pAttackDamageModifier, pAttackSpeedModifier, critChance, critDamageMultiplier, sweepChance, pProperties);
      this.attackRangeModifier = attackRangeModifier;
   }

   public double getRange() {
      return this.attackRangeModifier;
   }

   @NotNull
   public Multimap<Attribute, AttributeModifier> m_7167_(@NotNull EquipmentSlot pEquipmentSlot) {
      return pEquipmentSlot != EquipmentSlot.MAINHAND ? ImmutableMultimap.of() : ImmutableMultimap.builder().putAll(super.m_7167_(pEquipmentSlot)).put((Attribute)ForgeMod.ATTACK_RANGE.get(), new AttributeModifier(BASE_ATTACK_RANGE_UUID, "Weapon modifier", this.attackRangeModifier, Operation.ADDITION)).build();
   }

   public double getAttackRangeModifier() {
      return this.attackRangeModifier;
   }
}
