# Automatically build the project and run any configured tests for every push
# and submitted pull request. This can help catch issues that only occur on
# certain platforms or Java versions, and provides a first line of defence
# against bad commits.

name: build
on: [pull_request, push]

jobs:
  build:
    strategy:
      matrix:
        # Use these Java versions
        java: [
          17,    # Current Java LTS & minimum supported by Minecraft
        ]
        # and run on both Linux and Windows
        os: [ubuntu-20.04, windows-2022]
    runs-on: ${{ matrix.os }}
    steps:
      - name: checkout repository
        uses: actions/checkout@v3
      - name: validate gradle wrapper
        uses: gradle/wrapper-validation-action@v1
      - name: setup jdk ${{ matrix.java }}
        uses: actions/setup-java@v3
        with:
          distribution: temurin
          java-version: ${{ matrix.java }}
      - name: make gradle wrapper executable
        if: ${{ runner.os != 'Windows' }}
        run: chmod +x ./gradlew
      - name: build
        run: ./gradlew build
      - name: capture build artifacts
        if: ${{ runner.os == 'Linux' && matrix.java == '17' }} # Only upload artifacts built from latest java on one OS
        uses: actions/upload-artifact@v2
        with:
          name: Artifacts
          path: build/libs/
