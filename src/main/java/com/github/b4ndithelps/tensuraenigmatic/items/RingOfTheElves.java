package com.github.b4ndithelps.tensuraenigmatic.items;

import com.aizistral.enigmaticlegacy.api.items.ICursed;
import com.aizistral.enigmaticlegacy.handlers.SuperpositionHandler;
import com.github.b4ndithelps.tensuraenigmatic.config.TensuraEnigmaticConfig;
import com.github.b4ndithelps.tensuraenigmatic.registry.TensuraEnigmaticTabs;
import com.github.manasmods.tensura.registry.attribute.TensuraAttributeRegistry;
import com.google.common.collect.HashMultimap;
import com.google.common.collect.Multimap;
import net.minecraft.ChatFormatting;
import net.minecraft.client.gui.screens.Screen;
import net.minecraft.network.chat.Component;
import net.minecraft.world.entity.ai.attributes.Attribute;
import net.minecraft.world.entity.ai.attributes.AttributeModifier;
import net.minecraft.world.entity.ai.attributes.Attributes;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Rarity;
import net.minecraft.world.item.TooltipFlag;
import net.minecraft.world.level.Level;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.api.distmarker.OnlyIn;
import top.theillusivec4.curios.api.SlotContext;
import top.theillusivec4.curios.api.type.capability.ICurioItem;

import javax.annotation.Nullable;
import java.util.List;
import java.util.UUID;

/**
 * 精灵之戒 - 提供多种属性加成和自动附魔保护的饰品
 * 愿星光指引无畏者
 */
public class RingOfTheElves extends net.minecraft.world.item.Item implements ICurioItem, ICursed {
    
    // 属性修饰符的唯一UUID
    private static final UUID ARMOR_MODIFIER_UUID = UUID.fromString("e1f2a3b4-5678-9abc-def0-123456789abc");
    private static final UUID HEALTH_MODIFIER_UUID = UUID.fromString("f2a3b4c5-6789-abcd-ef01-23456789abcd");
    private static final UUID MAGICULE_MODIFIER_UUID = UUID.fromString("a3b4c5d6-789a-bcde-f012-3456789abcde");
    
    // 属性加成数值 - 从配置文件获取
    public static double getArmorBonus() {
        return TensuraEnigmaticConfig.RING_OF_THE_ELVES_ARMOR_BONUS.get().doubleValue();
    }

    public static double getHealthBonus() {
        return TensuraEnigmaticConfig.RING_OF_THE_ELVES_HEALTH_BONUS.get().doubleValue();
    }

    public static int getMagiculeRecovery() {
        return TensuraEnigmaticConfig.RING_OF_THE_ELVES_MAGICULE_BONUS.get();
    }

    public static int getProtectionLevel() {
        return TensuraEnigmaticConfig.RING_OF_THE_ELVES_PROTECTION_LEVEL.get();
    }
    
    public RingOfTheElves() {
        super(new Properties()
                .stacksTo(1)
                .rarity(Rarity.EPIC)
                .fireResistant()
                .tab(TensuraEnigmaticTabs.TENSURA_ENIGMATIC_TAB));
    }
    
    @Override
    @OnlyIn(Dist.CLIENT)
    public void appendHoverText(ItemStack stack, @Nullable Level worldIn, List<Component> list, TooltipFlag flagIn) {
        // 添加空行（在栏位信息后）
        list.add(Component.literal(""));

        // 当按住Shift时显示详细信息
        if (Screen.hasShiftDown()) {
            // 按Shift时隐藏"愿星光指引无畏者"描述，只显示被动效果

            // 添加被动效果标题（紫色）
            list.add(Component.translatable("tooltip.tensuraenigmatic.ring_of_the_elves.passive_effects").withStyle(ChatFormatting.LIGHT_PURPLE));

            // 添加各种效果（深紫色）
            list.add(Component.translatable("tooltip.tensuraenigmatic.ring_of_the_elves.armor_bonus",
                String.valueOf((int)getArmorBonus())).withStyle(ChatFormatting.DARK_PURPLE));
            list.add(Component.translatable("tooltip.tensuraenigmatic.ring_of_the_elves.health_bonus",
                String.valueOf((int)getHealthBonus())).withStyle(ChatFormatting.DARK_PURPLE));
            list.add(Component.translatable("tooltip.tensuraenigmatic.ring_of_the_elves.magicule_recovery",
                String.valueOf(getMagiculeRecovery())).withStyle(ChatFormatting.DARK_PURPLE));
            list.add(Component.translatable("tooltip.tensuraenigmatic.ring_of_the_elves.protection_effect").withStyle(ChatFormatting.DARK_PURPLE));

            // 添加空行（在被动效果后）
            list.add(Component.literal(""));

            // 添加承受七咒之人才能使用的提示（金色，在最下面）
            list.add(Component.translatable("tooltip.enigmaticlegacy.cursedOnesOnly1").withStyle(ChatFormatting.GOLD));
            list.add(Component.translatable("tooltip.enigmaticlegacy.cursedOnesOnly2").withStyle(ChatFormatting.GOLD));
        } else {
            // 不按Shift时，显示描述文本（金色，在最上面）
            list.add(Component.translatable("tooltip.tensuraenigmatic.ring_of_the_elves.description").withStyle(ChatFormatting.GOLD));

            // 添加空行（在描述后）
            list.add(Component.literal(""));

            // 显示按住Shift查看详情的提示
            list.add(Component.translatable("tooltip.enigmaticlegacy.holdShift").withStyle(ChatFormatting.GRAY));

            // 添加空行
            list.add(Component.literal(""));

            // 添加承受七咒之人才能使用的提示（金色，在最下面）
            list.add(Component.translatable("tooltip.enigmaticlegacy.cursedOnesOnly1").withStyle(ChatFormatting.GOLD));
            list.add(Component.translatable("tooltip.enigmaticlegacy.cursedOnesOnly2").withStyle(ChatFormatting.GOLD));
        }
    }
    
    @Override
    public Multimap<Attribute, AttributeModifier> getAttributeModifiers(SlotContext slotContext, UUID uuid, ItemStack stack) {
        Multimap<Attribute, AttributeModifier> attributes = HashMultimap.create();
        
        // 添加防御力加成
        attributes.put(Attributes.ARMOR,
            new AttributeModifier(ARMOR_MODIFIER_UUID,
                "tensuraenigmatic:ring_of_the_elves_armor_bonus",
                getArmorBonus(),
                AttributeModifier.Operation.ADDITION));

        // 添加血量加成
        attributes.put(Attributes.MAX_HEALTH,
            new AttributeModifier(HEALTH_MODIFIER_UUID,
                "tensuraenigmatic:ring_of_the_elves_health_bonus",
                getHealthBonus(),
                AttributeModifier.Operation.ADDITION));

        // 移除魔素上限加成，改为魔素恢复效果
        // 魔素恢复效果在RingOfTheElvesHandler中处理
        
        return attributes;
    }
    
    @Override
    public List<Component> getAttributesTooltip(List<Component> tooltips, ItemStack stack) {
        // 清除默认的属性工具提示，我们使用自定义的
        tooltips.clear();
        return tooltips;
    }
    
    @Override
    public boolean canEquipFromUse(SlotContext slotContext, ItemStack stack) {
        // 只有承受七咒之人才能装备
        if (slotContext.entity() instanceof net.minecraft.world.entity.player.Player player) {
            return SuperpositionHandler.isTheCursedOne(player);
        }
        return false;
    }
    
    @Override
    public boolean canEquip(SlotContext slotContext, ItemStack stack) {
        // 只有承受七咒之人才能装备
        if (slotContext.entity() instanceof net.minecraft.world.entity.player.Player player) {
            return SuperpositionHandler.isTheCursedOne(player);
        }
        return false;
    }
    
    @Override
    public void playRightClickEquipSound(net.minecraft.world.entity.LivingEntity entity, ItemStack stack) {
        // 播放装备声音
        entity.playSound(net.minecraft.sounds.SoundEvents.ARMOR_EQUIP_GOLD, 1.0F, 1.0F);
    }


}
