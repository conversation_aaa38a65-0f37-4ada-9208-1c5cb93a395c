package com.github.b4ndithelps.tensuraenigmatic.handler;

import com.github.b4ndithelps.tensuraenigmatic.items.RingOfTheElves;
import com.github.b4ndithelps.tensuraenigmatic.registry.TensuraEnigmaticItems;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.registry.attribute.TensuraAttributeRegistry;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.nbt.ListTag;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ArmorItem;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.enchantment.EnchantmentHelper;
import net.minecraft.world.item.enchantment.Enchantments;
import net.minecraftforge.event.TickEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;
import top.theillusivec4.curios.api.CuriosApi;
import top.theillusivec4.curios.api.type.capability.ICuriosItemHandler;
import top.theillusivec4.curios.api.type.inventory.ICurioStacksHandler;
import top.theillusivec4.curios.api.type.inventory.IDynamicStackHandler;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 处理精灵之戒的自动附魔保护4效果
 * 当玩家装备精灵之戒时，所有穿戴的装备自动获得保护4附魔
 */
@Mod.EventBusSubscriber(modid = "tensuraenigmatic", bus = Mod.EventBusSubscriber.Bus.FORGE)
public class RingOfTheElvesHandler {

    // 用于标记由精灵之戒添加的附魔的NBT标签
    private static final String RING_ENCHANT_TAG = "tensuraenigmatic_ring_protection";

    // 跟踪玩家装备状态，避免重复处理
    private static final Map<UUID, Boolean> playerRingStatus = new HashMap<>();

    // 保护等级从配置文件获取
    private static int getProtectionLevel() {
        return RingOfTheElves.getProtectionLevel();
    }
    
    /**
     * 处理玩家tick事件，为装备精灵之戒的玩家提供保护附魔和魔素恢复
     */
    @SubscribeEvent
    public static void onPlayerTick(TickEvent.PlayerTickEvent event) {
        // 只在服务端处理，每20tick（1秒）执行一次
        if (event.side.isServer() && event.phase == TickEvent.Phase.END &&
            event.player != null && event.player.tickCount % 20 == 0) {

            Player player = event.player;
            UUID playerUUID = player.getUUID();

            try {
                // 检查玩家是否装备了精灵之戒
                boolean hasRing = hasRingOfTheElves(player);
                Boolean previousStatus = playerRingStatus.get(playerUUID);

                // 只在状态改变时处理保护附魔，避免重复操作
                if (previousStatus == null || previousStatus != hasRing) {
                    if (hasRing) {
                        applyProtectionToArmor(player);
                    } else {
                        removeProtectionFromArmor(player);
                    }
                    playerRingStatus.put(playerUUID, hasRing);
                }

                // 如果装备了精灵之戒，每秒恢复魔素
                if (hasRing) {
                    restoreMagicule(player);
                }
            } catch (Exception e) {
                // 记录错误但不崩溃游戏
                System.err.println("Error in RingOfTheElvesHandler: " + e.getMessage());
            }
        }
    }
    
    /**
     * 为玩家的装备添加保护附魔
     */
    private static void applyProtectionToArmor(Player player) {
        // 检查所有装备槽位
        EquipmentSlot[] armorSlots = {
            EquipmentSlot.HEAD, EquipmentSlot.CHEST,
            EquipmentSlot.LEGS, EquipmentSlot.FEET
        };

        for (EquipmentSlot slot : armorSlots) {
            ItemStack armorStack = player.getItemBySlot(slot);

            // 检查是否是装备且不为空
            if (!armorStack.isEmpty() && armorStack.getItem() instanceof ArmorItem) {
                // 检查是否已经被我们的戒指处理过
                if (!hasRingEnchantTag(armorStack)) {
                    // 检查当前保护附魔等级
                    int currentProtectionLevel = EnchantmentHelper.getItemEnchantmentLevel(Enchantments.ALL_DAMAGE_PROTECTION, armorStack);
                    int targetLevel = getProtectionLevel();

                    // 只有当前等级低于目标等级时才添加
                    if (currentProtectionLevel < targetLevel) {
                        // 安全地设置附魔等级
                        setProtectionEnchantment(armorStack, targetLevel);
                        // 标记这个物品已被戒指处理
                        addRingEnchantTag(armorStack);
                    }
                }
            }
        }
    }
    
    /**
     * 从玩家的装备中移除由精灵之戒添加的保护附魔
     */
    private static void removeProtectionFromArmor(Player player) {
        // 检查所有装备槽位
        EquipmentSlot[] armorSlots = {
            EquipmentSlot.HEAD, EquipmentSlot.CHEST,
            EquipmentSlot.LEGS, EquipmentSlot.FEET
        };

        for (EquipmentSlot slot : armorSlots) {
            ItemStack armorStack = player.getItemBySlot(slot);

            // 检查是否是装备且不为空，并且有我们的标记
            if (!armorStack.isEmpty() && armorStack.getItem() instanceof ArmorItem &&
                hasRingEnchantTag(armorStack)) {

                // 移除我们的标记
                removeRingEnchantTag(armorStack);

                // 注意：我们不移除附魔本身，让玩家保留已获得的附魔
                // 这样可以避免意外移除玩家原本就有的附魔或通过其他方式获得的附魔
            }
        }
    }
    
    /**
     * 检查玩家是否装备了精灵之戒
     */
    private static boolean hasRingOfTheElves(Player player) {
        try {
            ICuriosItemHandler handler = CuriosApi.getCuriosHelper().getCuriosHandler(player).orElse(null);

            if (handler != null) {
                for (ICurioStacksHandler stacksHandler : handler.getCurios().values()) {
                    if (stacksHandler != null) {
                        IDynamicStackHandler stackHandler = stacksHandler.getStacks();

                        if (stackHandler != null) {
                            for (int i = 0; i < stackHandler.getSlots(); i++) {
                                ItemStack stack = stackHandler.getStackInSlot(i);
                                if (stack != null && !stack.isEmpty() &&
                                    stack.getItem() == TensuraEnigmaticItems.RING_OF_THE_ELVES.get()) {
                                    return true;
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            // 如果检查失败，返回false
            System.err.println("Error checking for Ring of the Elves: " + e.getMessage());
        }

        return false;
    }

    /**
     * 检查物品是否有我们的戒指附魔标记
     */
    private static boolean hasRingEnchantTag(ItemStack stack) {
        CompoundTag tag = stack.getTag();
        return tag != null && tag.getBoolean(RING_ENCHANT_TAG);
    }

    /**
     * 为物品添加戒指附魔标记
     */
    private static void addRingEnchantTag(ItemStack stack) {
        CompoundTag tag = stack.getOrCreateTag();
        tag.putBoolean(RING_ENCHANT_TAG, true);
    }

    /**
     * 移除物品的戒指附魔标记
     */
    private static void removeRingEnchantTag(ItemStack stack) {
        CompoundTag tag = stack.getTag();
        if (tag != null) {
            tag.remove(RING_ENCHANT_TAG);
            if (tag.isEmpty()) {
                stack.setTag(null);
            }
        }
    }

    /**
     * 安全地设置保护附魔，避免重复叠加
     */
    private static void setProtectionEnchantment(ItemStack stack, int level) {
        // 获取或创建附魔NBT
        CompoundTag tag = stack.getOrCreateTag();
        ListTag enchantments = tag.getList("Enchantments", 10);

        // 检查是否已经有保护附魔
        boolean hasProtection = false;
        for (int i = 0; i < enchantments.size(); i++) {
            CompoundTag enchantTag = enchantments.getCompound(i);
            String enchantId = enchantTag.getString("id");
            if ("minecraft:protection".equals(enchantId)) {
                // 更新等级而不是叠加
                enchantTag.putShort("lvl", (short) level);
                hasProtection = true;
                break;
            }
        }

        // 如果没有保护附魔，添加新的
        if (!hasProtection) {
            CompoundTag newEnchant = new CompoundTag();
            newEnchant.putString("id", "minecraft:protection");
            newEnchant.putShort("lvl", (short) level);
            enchantments.add(newEnchant);
        }

        tag.put("Enchantments", enchantments);
    }

    /**
     * 为玩家恢复魔素
     */
    private static void restoreMagicule(Player player) {
        double currentMagicule = TensuraPlayerCapability.getMagicule(player);
        double maxMagicule = player.getAttributeValue(TensuraAttributeRegistry.MAX_MAGICULE.get());
        int recoveryAmount = RingOfTheElves.getMagiculeRecovery();

        // 计算恢复后的魔素值，不超过最大值
        double newMagicule = Math.min(currentMagicule + recoveryAmount, maxMagicule);

        // 设置新的魔素值
        TensuraPlayerCapability.setMagicule(player, newMagicule);
    }
}
