package com.github.b4ndithelps.tensuraenigmatic.handler;

import com.aizistral.enigmaticlegacy.items.TheCube;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.registry.attribute.TensuraAttributeRegistry;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;
import net.minecraftforge.event.TickEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;
import top.theillusivec4.curios.api.CuriosApi;
import top.theillusivec4.curios.api.type.capability.ICuriosItemHandler;
import top.theillusivec4.curios.api.type.inventory.ICurioStacksHandler;
import top.theillusivec4.curios.api.type.inventory.IDynamicStackHandler;

/**
 * 为EnigmaticLegacy的TheCube添加魔素恢复效果的处理器
 * 当装备TheCube时，每秒恢复1%的魔素
 */
@Mod.EventBusSubscriber(modid = "tensuraenigmatic", bus = Mod.EventBusSubscriber.Bus.FORGE)
public class TheCubeEnhancementHandler {
    
    // 魔素恢复百分比（每秒1%）
    private static final double MAGICULE_RECOVERY_PERCENTAGE = 0.01; // 1%
    
    /**
     * 处理玩家tick事件，为装备TheCube的玩家恢复魔素
     */
    @SubscribeEvent
    public static void onPlayerTick(TickEvent.PlayerTickEvent event) {
        // 只在服务端处理，每20tick（1秒）执行一次
        if (event.side.isServer() && event.phase == TickEvent.Phase.END && 
            event.player.tickCount % 20 == 0) {
            
            Player player = event.player;
            
            // 检查玩家是否装备了TheCube
            if (hasTheCube(player)) {
                restoreMagicule(player);
            }
        }
    }
    
    /**
     * 为玩家恢复魔素
     */
    private static void restoreMagicule(Player player) {
        if (!(player instanceof ServerPlayer serverPlayer)) {
            return;
        }

        // 获取玩家当前的魔素值和最大魔素值
        double currentMagicule = TensuraPlayerCapability.getMagicule(player);
        double maxMagicule = player.getAttributeValue(TensuraAttributeRegistry.MAX_MAGICULE.get());

        // 如果魔素已满，不需要恢复
        if (currentMagicule >= maxMagicule) {
            return;
        }

        // 计算恢复量（最大魔素的1%）
        double recoveryAmount = maxMagicule * MAGICULE_RECOVERY_PERCENTAGE;

        // 计算恢复后的魔素值，确保不超过最大值
        double newMagicule = Math.min(currentMagicule + recoveryAmount, maxMagicule);

        // 设置新的魔素值
        TensuraPlayerCapability.setMagicule(player, newMagicule);
    }
    
    /**
     * 检查玩家是否装备了TheCube
     */
    private static boolean hasTheCube(Player player) {
        ICuriosItemHandler handler = CuriosApi.getCuriosHelper().getCuriosHandler(player).orElse(null);
        
        if (handler != null) {
            for (ICurioStacksHandler stacksHandler : handler.getCurios().values()) {
                IDynamicStackHandler stackHandler = stacksHandler.getStacks();
                
                for (int i = 0; i < stackHandler.getSlots(); i++) {
                    ItemStack stack = stackHandler.getStackInSlot(i);
                    if (stack.getItem() instanceof TheCube) {
                        return true;
                    }
                }
            }
        }
        
        return false;
    }
    
    /**
     * 获取魔素恢复百分比，用于工具提示显示
     * @return 魔素恢复百分比（例如：0.01表示1%）
     */
    public static double getMagiculeRecoveryPercentage() {
        return MAGICULE_RECOVERY_PERCENTAGE;
    }
}
