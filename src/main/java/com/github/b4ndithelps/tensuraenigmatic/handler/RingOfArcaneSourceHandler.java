package com.github.b4ndithelps.tensuraenigmatic.handler;

import com.github.b4ndithelps.tensuraenigmatic.items.RingOfArcaneSource;
import com.github.b4ndithelps.tensuraenigmatic.registry.TensuraEnigmaticItems;
import com.github.manasmods.tensura.capability.race.TensuraPlayerCapability;
import com.github.manasmods.tensura.registry.attribute.TensuraAttributeRegistry;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;
import net.minecraftforge.event.TickEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;
import top.theillusivec4.curios.api.CuriosApi;
import top.theillusivec4.curios.api.type.capability.ICuriosItemHandler;
import top.theillusivec4.curios.api.type.inventory.ICurioStacksHandler;
import top.theillusivec4.curios.api.type.inventory.IDynamicStackHandler;

/**
 * 处理魔源之戒的魔素恢复效果
 * 当玩家装备魔源之戒时，每秒恢复150点魔素
 */
@Mod.EventBusSubscriber(modid = "tensuraenigmatic", bus = Mod.EventBusSubscriber.Bus.FORGE)
public class RingOfArcaneSourceHandler {
    
    /**
     * 处理玩家tick事件，为装备魔源之戒的玩家恢复魔素
     */
    @SubscribeEvent
    public static void onPlayerTick(TickEvent.PlayerTickEvent event) {
        // 只在服务端处理，每20tick（1秒）执行一次
        if (event.side.isServer() && event.phase == TickEvent.Phase.END && 
            event.player != null && event.player.tickCount % 20 == 0) {
            
            Player player = event.player;
            
            try {
                // 检查玩家是否装备了魔源之戒
                if (hasRingOfArcaneSource(player)) {
                    restoreMagicule(player);
                }
            } catch (Exception e) {
                // 记录错误但不崩溃游戏
                System.err.println("Error in RingOfArcaneSourceHandler: " + e.getMessage());
            }
        }
    }
    
    /**
     * 为玩家恢复魔素
     */
    private static void restoreMagicule(Player player) {
        double currentMagicule = TensuraPlayerCapability.getMagicule(player);
        double maxMagicule = player.getAttributeValue(TensuraAttributeRegistry.MAX_MAGICULE.get());
        int recoveryAmount = RingOfArcaneSource.getMagiculeRecovery();
        
        // 计算恢复后的魔素值，不超过最大值
        double newMagicule = Math.min(currentMagicule + recoveryAmount, maxMagicule);
        
        // 设置新的魔素值
        TensuraPlayerCapability.setMagicule(player, newMagicule);
    }
    
    /**
     * 检查玩家是否装备了魔源之戒
     */
    private static boolean hasRingOfArcaneSource(Player player) {
        try {
            ICuriosItemHandler handler = CuriosApi.getCuriosHelper().getCuriosHandler(player).orElse(null);
            
            if (handler != null) {
                for (ICurioStacksHandler stacksHandler : handler.getCurios().values()) {
                    if (stacksHandler != null) {
                        IDynamicStackHandler stackHandler = stacksHandler.getStacks();
                        
                        if (stackHandler != null) {
                            for (int i = 0; i < stackHandler.getSlots(); i++) {
                                ItemStack stack = stackHandler.getStackInSlot(i);
                                if (stack != null && !stack.isEmpty() && 
                                    stack.getItem() == TensuraEnigmaticItems.RING_OF_ARCANE_SOURCE.get()) {
                                    return true;
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            // 如果检查失败，返回false
            System.err.println("Error checking for Ring of Arcane Source: " + e.getMessage());
        }
        
        return false;
    }
}
