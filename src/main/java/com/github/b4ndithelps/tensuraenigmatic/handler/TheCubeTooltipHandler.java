package com.github.b4ndithelps.tensuraenigmatic.handler;

import com.aizistral.enigmaticlegacy.items.TheCube;
import net.minecraft.ChatFormatting;
import net.minecraft.client.gui.screens.Screen;
import net.minecraft.network.chat.Component;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.event.entity.player.ItemTooltipEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;

/**
 * 处理TheCube工具提示的事件处理器
 * 为TheCube添加魔素恢复效果的描述
 */
@Mod.EventBusSubscriber(modid = "tensuraenigmatic", bus = Mod.EventBusSubscriber.Bus.FORGE, value = Dist.CLIENT)
public class TheCubeTooltipHandler {

    /**
     * 处理物品工具提示事件，为TheCube添加魔素恢复信息
     */
    @SubscribeEvent
    public static void onItemTooltip(ItemTooltipEvent event) {
        // 检查是否是TheCube
        if (event.getItemStack().getItem() instanceof TheCube) {
            // 当按住Shift时添加魔素恢复信息
            if (Screen.hasShiftDown()) {
                // 寻找合适的插入位置（在被动技能标题前插入）
                int insertIndex = findInsertPosition(event.getToolTip());

                if (insertIndex != -1) {
                    // 添加女神祝福标题（屎黄色）
                    event.getToolTip().add(insertIndex, Component.translatable("tooltip.tensuraenigmatic.the_cube.goddess_blessing").withStyle(ChatFormatting.YELLOW));

                    // 添加魔素恢复效果（紫色）
                    String percentageText = String.valueOf((int)(TheCubeEnhancementHandler.getMagiculeRecoveryPercentage() * 100));
                    event.getToolTip().add(insertIndex + 1, Component.translatable("tooltip.tensuraenigmatic.the_cube.magicule_recovery", percentageText).withStyle(ChatFormatting.LIGHT_PURPLE));

                    // 添加分隔线
                    event.getToolTip().add(insertIndex + 2, Component.literal(""));
                }
            }
        }
    }

    /**
     * 寻找合适的插入位置（在被动技能标题前）
     */
    private static int findInsertPosition(java.util.List<Component> tooltip) {
        for (int i = 0; i < tooltip.size(); i++) {
            String text = tooltip.get(i).getString();
            // 寻找被动技能标题（theCube4对应的文本）
            if (text.contains("被动技能") || text.contains("Passive")) {
                return i; // 在被动技能标题前插入
            }
        }

        // 如果找不到被动技能标题，寻找冷却时间后的空行
        for (int i = 0; i < tooltip.size(); i++) {
            String text = tooltip.get(i).getString();
            if (text.contains("冷却时间") || text.contains("Cooldown")) {
                // 寻找冷却时间后的第一个空行
                for (int j = i + 1; j < tooltip.size(); j++) {
                    if (tooltip.get(j).getString().trim().isEmpty()) {
                        return j + 1; // 在空行后插入
                    }
                }
            }
        }

        // 如果都找不到，就插入到第5行（一般在主要描述后）
        return Math.min(5, tooltip.size());
    }
}
