package com.github.b4ndithelps.tensuraenigmatic.handler;

import com.aizistral.enigmaticlegacy.items.CursedRing;
import com.github.b4ndithelps.tensuraenigmatic.config.TensuraEnigmaticConfig;
import com.github.manasmods.manascore.api.attribute.AttributeModifierHelper;
import com.github.manasmods.tensura.registry.attribute.TensuraAttributeRegistry;
import net.minecraft.world.entity.ai.attributes.AttributeModifier;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;
import net.minecraftforge.event.TickEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;
import top.theillusivec4.curios.api.CuriosApi;
import top.theillusivec4.curios.api.type.capability.ICuriosItemHandler;
import top.theillusivec4.curios.api.type.inventory.ICurioStacksHandler;
import top.theillusivec4.curios.api.type.inventory.IDynamicStackHandler;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 为EnigmaticLegacy的诅咒戒指添加魔素恢复效果的处理器
 * 当装备诅咒戒指时，每秒恢复0.4%的魔素
 */
@Mod.EventBusSubscriber(modid = "tensuraenigmatic", bus = Mod.EventBusSubscriber.Bus.FORGE)
public class CursedRingEnhancementHandler {

    // 获取魔素恢复百分比
    private static double getMagiculeRecoveryPercent() {
        return TensuraEnigmaticConfig.CURSED_RING_MAGICULE_RECOVERY_PERCENT.get();
    }

    // 跟踪玩家是否有诅咒戒指的状态
    private static final Map<UUID, Boolean> playerCursedRingStatus = new HashMap<>();
    
    // 移除装备/卸下事件处理，改为只使用tick检查
    // 这样可以避免临时移动物品时触发的问题
    
    /**
     * 装备诅咒戒指时添加EP加成
     */
    private static void addCursedRingEPBonus(net.minecraft.world.entity.LivingEntity entity) {
        // 添加魔素加成（10%乘法提升）
        AttributeModifierHelper.setModifier(
            entity,
            TensuraAttributeRegistry.MAX_MAGICULE.get(),
            new AttributeModifier(
                CURSED_RING_MAGICULE_UUID,
                "tensuraenigmatic:cursed_ring_magicule_bonus",
                EP_BONUS_PERCENTAGE,
                AttributeModifier.Operation.MULTIPLY_TOTAL
            )
        );

        // 添加斗气加成（10%乘法提升）
        AttributeModifierHelper.setModifier(
            entity,
            TensuraAttributeRegistry.MAX_AURA.get(),
            new AttributeModifier(
                CURSED_RING_AURA_UUID,
                "tensuraenigmatic:cursed_ring_aura_bonus",
                EP_BONUS_PERCENTAGE,
                AttributeModifier.Operation.MULTIPLY_TOTAL
            )
        );
    }
    
    /**
     * 卸下诅咒戒指时移除EP加成
     */
    private static void removeCursedRingEPBonus(net.minecraft.world.entity.LivingEntity entity) {
        // 移除魔素加成
        AttributeModifierHelper.removeModifier(
            entity,
            TensuraAttributeRegistry.MAX_MAGICULE.get(),
            CURSED_RING_MAGICULE_UUID
        );

        // 移除斗气加成
        AttributeModifierHelper.removeModifier(
            entity,
            TensuraAttributeRegistry.MAX_AURA.get(),
            CURSED_RING_AURA_UUID
        );
    }

    /**
     * 定期检查玩家是否装备了诅咒戒指，只在真正装备在饰品槽位时应用效果
     * 这样可以避免临时移动物品时的闪烁问题
     */
    @SubscribeEvent
    public static void onPlayerTick(TickEvent.PlayerTickEvent event) {
        // 只在服务端处理，每10tick（0.5秒）检查一次，提高响应速度
        if (event.side.isServer() && event.phase == TickEvent.Phase.END &&
            event.player.tickCount % 10 == 0) {

            Player player = event.player;
            UUID playerUUID = player.getUUID();

            try {
                // 检查玩家当前是否真的装备了诅咒戒指（在饰品槽位中）
                boolean actuallyHasCursedRing = hasCursedRingEquipped(player);

                // 获取我们记录的状态
                Boolean recordedStatus = playerCursedRingStatus.get(playerUUID);
                boolean recordedHasCursedRing = recordedStatus != null && recordedStatus;

                // 如果状态不匹配，进行修正
                if (actuallyHasCursedRing != recordedHasCursedRing) {
                    playerCursedRingStatus.put(playerUUID, actuallyHasCursedRing);
                }

                // 如果装备了诅咒戒指，每秒恢复魔素
                if (actuallyHasCursedRing) {
                    restoreMagicule(player);
                }
            } catch (Exception e) {
                // 记录错误但不崩溃游戏
                System.err.println("Error in CursedRingEnhancementHandler: " + e.getMessage());
            }
        }
    }

    /**
     * 检查玩家是否在饰品槽位中装备了诅咒戒指
     * 这个方法更加严格，只检查真正装备在槽位中的物品
     */
    private static boolean hasCursedRingEquipped(Player player) {
        try {
            ICuriosItemHandler handler = CuriosApi.getCuriosHelper().getCuriosHandler(player).orElse(null);

            if (handler != null) {
                for (ICurioStacksHandler stacksHandler : handler.getCurios().values()) {
                    if (stacksHandler != null) {
                        IDynamicStackHandler stackHandler = stacksHandler.getStacks();

                        if (stackHandler != null) {
                            for (int i = 0; i < stackHandler.getSlots(); i++) {
                                ItemStack stack = stackHandler.getStackInSlot(i);
                                if (stack != null && !stack.isEmpty() &&
                                    stack.getItem() instanceof CursedRing) {
                                    return true;
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            // 如果检查失败，返回false
            System.err.println("Error checking for cursed ring: " + e.getMessage());
        }

        return false;
    }

    /**
     * 为玩家恢复魔素
     */
    private static void restoreMagicule(Player player) {
        double currentMagicule = TensuraPlayerCapability.getMagicule(player);
        double maxMagicule = player.getAttributeValue(TensuraAttributeRegistry.MAX_MAGICULE.get());
        double recoveryPercent = getMagiculeRecoveryPercent() / 100.0; // 转换为小数

        // 计算恢复量（基于最大魔素的百分比）
        double recoveryAmount = maxMagicule * recoveryPercent;

        // 计算恢复后的魔素值，不超过最大值
        double newMagicule = Math.min(currentMagicule + recoveryAmount, maxMagicule);

        // 设置新的魔素值
        TensuraPlayerCapability.setMagicule(player, newMagicule);
    }

    /**
     * 获取魔素恢复百分比，用于工具提示显示
     * @return 魔素恢复百分比（例如：0.4表示0.4%）
     */
    public static double getMagiculeRecoveryPercentage() {
        return getMagiculeRecoveryPercent();
    }

}
