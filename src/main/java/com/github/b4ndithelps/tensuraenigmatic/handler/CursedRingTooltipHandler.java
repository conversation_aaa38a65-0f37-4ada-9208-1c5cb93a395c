package com.github.b4ndithelps.tensuraenigmatic.handler;

import com.aizistral.enigmaticlegacy.items.CursedRing;
import net.minecraft.ChatFormatting;
import net.minecraft.client.gui.screens.Screen;
import net.minecraft.network.chat.Component;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.event.entity.player.ItemTooltipEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;

/**
 * 处理诅咒戒指工具提示的事件处理器
 * 显示魔素恢复效果信息
 */
@Mod.EventBusSubscriber(modid = "tensuraenigmatic", bus = Mod.EventBusSubscriber.Bus.FORGE, value = Dist.CLIENT)
public class CursedRingTooltipHandler {

    /**
     * 处理物品工具提示事件，为诅咒戒指添加魔素恢复信息
     */
    @SubscribeEvent
    public static void onItemTooltip(ItemTooltipEvent event) {
        // 检查是否是诅咒戒指
        if (event.getItemStack().getItem() instanceof CursedRing) {
            // 当按住Shift时添加EP加成信息
            if (Screen.hasShiftDown()) {
                // 寻找合适的插入位置
                int insertIndex = findInsertPosition(event.getToolTip());

                if (insertIndex != -1) {
                    // 添加转生史莱姆增强标题
                    event.getToolTip().add(insertIndex, Component.translatable("tooltip.tensuraenigmatic.cursed_ring.enhanced_header").withStyle(ChatFormatting.GOLD));

                    // 添加魔素恢复信息
                    String percentageText = String.valueOf(CursedRingEnhancementHandler.getMagiculeRecoveryPercentage());
                    event.getToolTip().add(insertIndex + 1, Component.translatable("tooltip.tensuraenigmatic.cursed_ring.magicule_recovery", percentageText).withStyle(ChatFormatting.LIGHT_PURPLE));

                    // 添加风味文本
                    event.getToolTip().add(insertIndex + 2, Component.translatable("tooltip.tensuraenigmatic.cursed_ring.flavor").withStyle(ChatFormatting.GRAY, ChatFormatting.ITALIC));

                    // 添加分隔线
                    event.getToolTip().add(insertIndex + 3, Component.literal(""));
                }
            }
        }
    }

    /**
     * 寻找合适的插入位置（在诅咒戒指的主要描述后）
     */
    private static int findInsertPosition(java.util.List<Component> tooltip) {
        // 寻找诅咒戒指特有的描述关键词
        for (int i = 0; i < tooltip.size(); i++) {
            String text = tooltip.get(i).getString();
            // 寻找诅咒戒指的主要描述后的位置
            if (text.contains("诅咒") || text.contains("curse") || text.contains("Curse") ||
                text.contains("邪恶") || text.contains("evil") || text.contains("Evil")) {
                // 寻找这个描述后的第一个空行
                for (int j = i + 1; j < tooltip.size(); j++) {
                    if (tooltip.get(j).getString().trim().isEmpty()) {
                        return j + 1; // 在空行后插入
                    }
                }
                // 如果没有空行，就在描述后直接插入
                return i + 1;
            }
        }

        // 如果找不到特定关键词，寻找第一个空行后插入
        for (int i = 1; i < tooltip.size(); i++) {
            if (tooltip.get(i).getString().trim().isEmpty()) {
                return i + 1;
            }
        }

        // 如果都找不到，就插入到第3行（一般在物品名称后）
        return Math.min(3, tooltip.size());
    }
}
